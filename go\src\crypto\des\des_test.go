// Copyright 2011 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

package des_test

import (
	"bytes"
	"crypto/cipher"
	"crypto/des"
	"crypto/internal/cryptotest"
	"testing"
)

type CryptTest struct {
	key []byte
	in  []byte
	out []byte
}

// some custom tests for DES
var encryptDESTests = []CryptTest{
	{
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0x8c, 0xa6, 0x4d, 0xe9, 0xc1, 0xb1, 0x23, 0xa7}},
	{
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff},
		[]byte{0x35, 0x55, 0x50, 0xb2, 0x15, 0x0e, 0x24, 0x51}},
	{
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0x01, 0x23, 0x45, 0x67, 0x89, 0xab, 0xcd, 0xef},
		[]byte{0x61, 0x7b, 0x3a, 0x0c, 0xe8, 0xf0, 0x71, 0x00}},
	{
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10},
		[]byte{0x92, 0x31, 0xf2, 0x36, 0xff, 0x9a, 0xa9, 0x5c}},
	{
		[]byte{0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff},
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0xca, 0xaa, 0xaf, 0x4d, 0xea, 0xf1, 0xdb, 0xae}},
	{
		[]byte{0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff},
		[]byte{0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff},
		[]byte{0x73, 0x59, 0xb2, 0x16, 0x3e, 0x4e, 0xdc, 0x58}},
	{
		[]byte{0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff},
		[]byte{0x01, 0x23, 0x45, 0x67, 0x89, 0xab, 0xcd, 0xef},
		[]byte{0x6d, 0xce, 0x0d, 0xc9, 0x00, 0x65, 0x56, 0xa3}},
	{
		[]byte{0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff},
		[]byte{0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10},
		[]byte{0x9e, 0x84, 0xc5, 0xf3, 0x17, 0x0f, 0x8e, 0xff}},
	{
		[]byte{0x01, 0x23, 0x45, 0x67, 0x89, 0xab, 0xcd, 0xef},
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0xd5, 0xd4, 0x4f, 0xf7, 0x20, 0x68, 0x3d, 0x0d}},
	{
		[]byte{0x01, 0x23, 0x45, 0x67, 0x89, 0xab, 0xcd, 0xef},
		[]byte{0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff},
		[]byte{0x59, 0x73, 0x23, 0x56, 0xf3, 0x6f, 0xde, 0x06}},
	{
		[]byte{0x01, 0x23, 0x45, 0x67, 0x89, 0xab, 0xcd, 0xef},
		[]byte{0x01, 0x23, 0x45, 0x67, 0x89, 0xab, 0xcd, 0xef},
		[]byte{0x56, 0xcc, 0x09, 0xe7, 0xcf, 0xdc, 0x4c, 0xef}},
	{
		[]byte{0x01, 0x23, 0x45, 0x67, 0x89, 0xab, 0xcd, 0xef},
		[]byte{0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10},
		[]byte{0x12, 0xc6, 0x26, 0xaf, 0x05, 0x8b, 0x43, 0x3b}},
	{
		[]byte{0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10},
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0xa6, 0x8c, 0xdc, 0xa9, 0x0c, 0x90, 0x21, 0xf9}},
	{
		[]byte{0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10},
		[]byte{0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff},
		[]byte{0x2a, 0x2b, 0xb0, 0x08, 0xdf, 0x97, 0xc2, 0xf2}},
	{
		[]byte{0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10},
		[]byte{0x01, 0x23, 0x45, 0x67, 0x89, 0xab, 0xcd, 0xef},
		[]byte{0xed, 0x39, 0xd9, 0x50, 0xfa, 0x74, 0xbc, 0xc4}},
	{
		[]byte{0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10},
		[]byte{0xfe, 0xdc, 0xba, 0x98, 0x76, 0x54, 0x32, 0x10},
		[]byte{0xa9, 0x33, 0xf6, 0x18, 0x30, 0x23, 0xb3, 0x10}},
	{
		[]byte{0x01, 0x23, 0x45, 0x67, 0x89, 0xab, 0xcd, 0xef},
		[]byte{0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11},
		[]byte{0x17, 0x66, 0x8d, 0xfc, 0x72, 0x92, 0x53, 0x2d}},
	{
		[]byte{0x01, 0x23, 0x45, 0x67, 0x89, 0xab, 0xcd, 0xef},
		[]byte{0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01},
		[]byte{0xb4, 0xfd, 0x23, 0x16, 0x47, 0xa5, 0xbe, 0xc0}},
	{
		[]byte{0x0e, 0x32, 0x92, 0x32, 0xea, 0x6d, 0x0d, 0x73},
		[]byte{0x87, 0x87, 0x87, 0x87, 0x87, 0x87, 0x87, 0x87},
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}},
	{
		[]byte{0x73, 0x65, 0x63, 0x52, 0x33, 0x74, 0x24, 0x3b}, // "secR3t$;"
		[]byte{0x61, 0x20, 0x74, 0x65, 0x73, 0x74, 0x31, 0x32}, // "a test12"
		[]byte{0x37, 0x0d, 0xee, 0x2c, 0x1f, 0xb4, 0xf7, 0xa5}},
	{
		[]byte{0x61, 0x62, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68}, // "abcdefgh"
		[]byte{0x61, 0x62, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68}, // "abcdefgh"
		[]byte{0x2a, 0x8d, 0x69, 0xde, 0x9d, 0x5f, 0xdf, 0xf9}},
	{
		[]byte{0x61, 0x62, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68}, // "abcdefgh"
		[]byte{0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38}, // "12345678"
		[]byte{0x21, 0xc6, 0x0d, 0xa5, 0x34, 0x24, 0x8b, 0xce}},
	{
		[]byte{0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38}, // "12345678"
		[]byte{0x61, 0x62, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68}, // "abcdefgh"
		[]byte{0x94, 0xd4, 0x43, 0x6b, 0xc3, 0xb5, 0xb6, 0x93}},
	{
		[]byte{0x1f, 0x79, 0x90, 0x5f, 0x88, 0x01, 0xc8, 0x88}, // random
		[]byte{0xc7, 0x46, 0x18, 0x73, 0xaf, 0x48, 0x5f, 0xb3}, // random
		[]byte{0xb0, 0x93, 0x50, 0x88, 0xf9, 0x92, 0x44, 0x6a}},
	{
		[]byte{0xe6, 0xf4, 0xf2, 0xdb, 0x31, 0x42, 0x53, 0x01}, // random
		[]byte{0xff, 0x3d, 0x25, 0x50, 0x12, 0xe3, 0x4a, 0xc5}, // random
		[]byte{0x86, 0x08, 0xd3, 0xd1, 0x6c, 0x2f, 0xd2, 0x55}},
	{
		[]byte{0x69, 0xc1, 0x9d, 0xc1, 0x15, 0xc5, 0xfb, 0x2b}, // random
		[]byte{0x1a, 0x22, 0x5c, 0xaf, 0x1f, 0x1d, 0xa3, 0xf9}, // random
		[]byte{0x64, 0xba, 0x31, 0x67, 0x56, 0x91, 0x1e, 0xa7}},
	{
		[]byte{0x6e, 0x5e, 0xe2, 0x47, 0xc4, 0xbf, 0xf6, 0x51}, // random
		[]byte{0x11, 0xc9, 0x57, 0xff, 0x66, 0x89, 0x0e, 0xf0}, // random
		[]byte{0x94, 0xc5, 0x35, 0xb2, 0xc5, 0x8b, 0x39, 0x72}},
}

var weakKeyTests = []CryptTest{
	{
		[]byte{0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01},
		[]byte{0x55, 0x74, 0xc0, 0xbd, 0x7c, 0xdf, 0xf7, 0x39}, // random
		nil},
	{
		[]byte{0xfe, 0xfe, 0xfe, 0xfe, 0xfe, 0xfe, 0xfe, 0xfe},
		[]byte{0xe8, 0xe1, 0xa7, 0xc1, 0xde, 0x11, 0x89, 0xaa}, // random
		nil},
	{
		[]byte{0xe0, 0xe0, 0xe0, 0xe0, 0xf1, 0xf1, 0xf1, 0xf1},
		[]byte{0x50, 0x6a, 0x4b, 0x94, 0x3b, 0xed, 0x7d, 0xdc}, // random
		nil},
	{
		[]byte{0x1f, 0x1f, 0x1f, 0x1f, 0x0e, 0x0e, 0x0e, 0x0e},
		[]byte{0x88, 0x81, 0x56, 0x38, 0xec, 0x3b, 0x1c, 0x97}, // random
		nil},
	{
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0x17, 0xa0, 0x83, 0x62, 0x32, 0xfe, 0x9a, 0x0b}, // random
		nil},
	{
		[]byte{0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff},
		[]byte{0xca, 0x8f, 0xca, 0x1f, 0x50, 0xc5, 0x7b, 0x49}, // random
		nil},
	{
		[]byte{0xe1, 0xe1, 0xe1, 0xe1, 0xf0, 0xf0, 0xf0, 0xf0},
		[]byte{0xb1, 0xea, 0xad, 0x7d, 0xe7, 0xc3, 0x7a, 0x43}, // random
		nil},
	{
		[]byte{0x1e, 0x1e, 0x1e, 0x1e, 0x0f, 0x0f, 0x0f, 0x0f},
		[]byte{0xae, 0x74, 0x7d, 0x6f, 0xef, 0x16, 0xbb, 0x81}, // random
		nil},
}

var semiWeakKeyTests = []CryptTest{
	// key and out contain the semi-weak key pair
	{
		[]byte{0x01, 0x1f, 0x01, 0x1f, 0x01, 0x0e, 0x01, 0x0e},
		[]byte{0x12, 0xfa, 0x31, 0x16, 0xf9, 0xc5, 0x0a, 0xe4}, // random
		[]byte{0x1f, 0x01, 0x1f, 0x01, 0x0e, 0x01, 0x0e, 0x01}},
	{
		[]byte{0x01, 0xe0, 0x01, 0xe0, 0x01, 0xf1, 0x01, 0xf1},
		[]byte{0xb0, 0x4c, 0x7a, 0xee, 0xd2, 0xe5, 0x4d, 0xb7}, // random
		[]byte{0xe0, 0x01, 0xe0, 0x01, 0xf1, 0x01, 0xf1, 0x01}},
	{
		[]byte{0x01, 0xfe, 0x01, 0xfe, 0x01, 0xfe, 0x01, 0xfe},
		[]byte{0xa4, 0x81, 0xcd, 0xb1, 0x64, 0x6f, 0xd3, 0xbc}, // random
		[]byte{0xfe, 0x01, 0xfe, 0x01, 0xfe, 0x01, 0xfe, 0x01}},
	{
		[]byte{0x1f, 0xe0, 0x1f, 0xe0, 0x0e, 0xf1, 0x0e, 0xf1},
		[]byte{0xee, 0x27, 0xdd, 0x88, 0x4c, 0x22, 0xcd, 0xce}, // random
		[]byte{0xe0, 0x1f, 0xe0, 0x1f, 0xf1, 0x0e, 0xf1, 0x0e}},
	{
		[]byte{0x1f, 0xfe, 0x1f, 0xfe, 0x0e, 0xfe, 0x0e, 0xfe},
		[]byte{0x19, 0x3d, 0xcf, 0x97, 0x70, 0xfb, 0xab, 0xe1}, // random
		[]byte{0xfe, 0x1f, 0xfe, 0x1f, 0xfe, 0x0e, 0xfe, 0x0e}},
	{
		[]byte{0xe0, 0xfe, 0xe0, 0xfe, 0xf1, 0xfe, 0xf1, 0xfe},
		[]byte{0x7c, 0x82, 0x69, 0xe4, 0x1e, 0x86, 0x99, 0xd7}, // random
		[]byte{0xfe, 0xe0, 0xfe, 0xe0, 0xfe, 0xf1, 0xfe, 0xf1}},
}

// some custom tests for TripleDES
var encryptTripleDESTests = []CryptTest{
	{
		[]byte{
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0x92, 0x95, 0xb5, 0x9b, 0xb3, 0x84, 0x73, 0x6e}},
	{
		[]byte{
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff},
		[]byte{0xc1, 0x97, 0xf5, 0x58, 0x74, 0x8a, 0x20, 0xe7}},
	{
		[]byte{
			0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff},
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0x3e, 0x68, 0x0a, 0xa7, 0x8b, 0x75, 0xdf, 0x18}},
	{
		[]byte{
			0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff},
		[]byte{0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff},
		[]byte{0x6d, 0x6a, 0x4a, 0x64, 0x4c, 0x7b, 0x8c, 0x91}},
	{
		[]byte{ // "abcdefgh12345678ABCDEFGH"
			0x61, 0x62, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68,
			0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38,
			0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47, 0x48},
		[]byte{0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30}, // "00000000"
		[]byte{0xe4, 0x61, 0xb7, 0x59, 0x68, 0x8b, 0xff, 0x66}},
	{
		[]byte{ // "abcdefgh12345678ABCDEFGH"
			0x61, 0x62, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68,
			0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38,
			0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47, 0x48},
		[]byte{0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38}, // "12345678"
		[]byte{0xdb, 0xd0, 0x92, 0xde, 0xf8, 0x34, 0xff, 0x58}},
	{
		[]byte{ // "abcdefgh12345678ABCDEFGH"
			0x61, 0x62, 0x63, 0x64, 0x65, 0x66, 0x67, 0x68,
			0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38,
			0x41, 0x42, 0x43, 0x44, 0x45, 0x46, 0x47, 0x48},
		[]byte{0xf0, 0xc5, 0x82, 0x22, 0xd3, 0xe6, 0x12, 0xd2}, // random
		[]byte{0xba, 0xe4, 0x41, 0xb1, 0x3c, 0x37, 0x4d, 0xf4}},
	{
		[]byte{ // random
			0xd3, 0x7d, 0x45, 0xee, 0x22, 0xe9, 0xcf, 0x52,
			0xf4, 0x65, 0xa2, 0x4f, 0x70, 0xd1, 0x81, 0x8a,
			0x3d, 0xbe, 0x2f, 0x39, 0xc7, 0x71, 0xd2, 0xe9},
		[]byte{0x49, 0x53, 0xc3, 0xe9, 0x78, 0xdf, 0x9f, 0xaf}, // random
		[]byte{0x53, 0x40, 0x51, 0x24, 0xd8, 0x3c, 0xf9, 0x88}},
	{
		[]byte{ // random
			0xcb, 0x10, 0x7d, 0xda, 0x7e, 0x96, 0x57, 0x0a,
			0xe8, 0xeb, 0xe8, 0x07, 0x8e, 0x87, 0xd3, 0x57,
			0xb2, 0x61, 0x12, 0xb8, 0x2a, 0x90, 0xb7, 0x2f},
		[]byte{0xa3, 0xc2, 0x60, 0xb1, 0x0b, 0xb7, 0x28, 0x6e}, // random
		[]byte{0x56, 0x73, 0x7d, 0xfb, 0xb5, 0xa1, 0xc3, 0xde}},
}

// NIST Special Publication 800-20, Appendix A
// Key for use with Table A.1 tests
var tableA1Key = []byte{
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
}

// Table A.1 Resulting Ciphertext from the Variable Plaintext Known Answer Test
var tableA1Tests = []CryptTest{
	{nil, // 0
		[]byte{0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0x95, 0xf8, 0xa5, 0xe5, 0xdd, 0x31, 0xd9, 0x00}},
	{nil, // 1
		[]byte{0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0xdd, 0x7f, 0x12, 0x1c, 0xa5, 0x01, 0x56, 0x19}},
	{nil, // 2
		[]byte{0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0x2e, 0x86, 0x53, 0x10, 0x4f, 0x38, 0x34, 0xea}},
	{nil, // 3
		[]byte{0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0x4b, 0xd3, 0x88, 0xff, 0x6c, 0xd8, 0x1d, 0x4f}},
	{nil, // 4
		[]byte{0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0x20, 0xb9, 0xe7, 0x67, 0xb2, 0xfb, 0x14, 0x56}},
	{nil, // 5
		[]byte{0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0x55, 0x57, 0x93, 0x80, 0xd7, 0x71, 0x38, 0xef}},
	{nil, // 6
		[]byte{0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0x6c, 0xc5, 0xde, 0xfa, 0xaf, 0x04, 0x51, 0x2f}},
	{nil, // 7
		[]byte{0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0x0d, 0x9f, 0x27, 0x9b, 0xa5, 0xd8, 0x72, 0x60}},
	{nil, // 8
		[]byte{0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0xd9, 0x03, 0x1b, 0x02, 0x71, 0xbd, 0x5a, 0x0a}},
	{nil, // 9
		[]byte{0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0x42, 0x42, 0x50, 0xb3, 0x7c, 0x3d, 0xd9, 0x51}},
	{nil, // 10
		[]byte{0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0xb8, 0x06, 0x1b, 0x7e, 0xcd, 0x9a, 0x21, 0xe5}},
	{nil, // 11
		[]byte{0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0xf1, 0x5d, 0x0f, 0x28, 0x6b, 0x65, 0xbd, 0x28}},
	{nil, // 12
		[]byte{0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0xad, 0xd0, 0xcc, 0x8d, 0x6e, 0x5d, 0xeb, 0xa1}},
	{nil, // 13
		[]byte{0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0xe6, 0xd5, 0xf8, 0x27, 0x52, 0xad, 0x63, 0xd1}},
	{nil, // 14
		[]byte{0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0xec, 0xbf, 0xe3, 0xbd, 0x3f, 0x59, 0x1a, 0x5e}},
	{nil, // 15
		[]byte{0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0xf3, 0x56, 0x83, 0x43, 0x79, 0xd1, 0x65, 0xcd}},
	{nil, // 16
		[]byte{0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0x2b, 0x9f, 0x98, 0x2f, 0x20, 0x03, 0x7f, 0xa9}},
	{nil, // 17
		[]byte{0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0x88, 0x9d, 0xe0, 0x68, 0xa1, 0x6f, 0x0b, 0xe6}},
	{nil, // 18
		[]byte{0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0xe1, 0x9e, 0x27, 0x5d, 0x84, 0x6a, 0x12, 0x98}},
	{nil, // 19
		[]byte{0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0x32, 0x9a, 0x8e, 0xd5, 0x23, 0xd7, 0x1a, 0xec}},
	{nil, // 20
		[]byte{0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0xe7, 0xfc, 0xe2, 0x25, 0x57, 0xd2, 0x3c, 0x97}},
	{nil, // 21
		[]byte{0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0x12, 0xa9, 0xf5, 0x81, 0x7f, 0xf2, 0xd6, 0x5d}},
	{nil, // 22
		[]byte{0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0xa4, 0x84, 0xc3, 0xad, 0x38, 0xdc, 0x9c, 0x19}},
	{nil, // 23
		[]byte{0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00},
		[]byte{0xfb, 0xe0, 0x0a, 0x8a, 0x1e, 0xf8, 0xad, 0x72}},
	{nil, // 24
		[]byte{0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00, 0x00},
		[]byte{0x75, 0x0d, 0x07, 0x94, 0x07, 0x52, 0x13, 0x63}},
	{nil, // 25
		[]byte{0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00},
		[]byte{0x64, 0xfe, 0xed, 0x9c, 0x72, 0x4c, 0x2f, 0xaf}},
	{nil, // 26
		[]byte{0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00},
		[]byte{0xf0, 0x2b, 0x26, 0x3b, 0x32, 0x8e, 0x2b, 0x60}},
	{nil, // 27
		[]byte{0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00},
		[]byte{0x9d, 0x64, 0x55, 0x5a, 0x9a, 0x10, 0xb8, 0x52}},
	{nil, // 28
		[]byte{0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x00},
		[]byte{0xd1, 0x06, 0xff, 0x0b, 0xed, 0x52, 0x55, 0xd7}},
	{nil, // 29
		[]byte{0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00},
		[]byte{0xe1, 0x65, 0x2c, 0x6b, 0x13, 0x8c, 0x64, 0xa5}},
	{nil, // 30
		[]byte{0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00, 0x00},
		[]byte{0xe4, 0x28, 0x58, 0x11, 0x86, 0xec, 0x8f, 0x46}},
	{nil, // 31
		[]byte{0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00},
		[]byte{0xae, 0xb5, 0xf5, 0xed, 0xe2, 0x2d, 0x1a, 0x36}},
	{nil, // 32
		[]byte{0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x00},
		[]byte{0xe9, 0x43, 0xd7, 0x56, 0x8a, 0xec, 0x0c, 0x5c}},
	{nil, // 33
		[]byte{0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00, 0x00},
		[]byte{0xdf, 0x98, 0xc8, 0x27, 0x6f, 0x54, 0xb0, 0x4b}},
	{nil, // 34
		[]byte{0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00},
		[]byte{0xb1, 0x60, 0xe4, 0x68, 0x0f, 0x6c, 0x69, 0x6f}},
	{nil, // 35
		[]byte{0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00},
		[]byte{0xfa, 0x07, 0x52, 0xb0, 0x7d, 0x9c, 0x4a, 0xb8}},
	{nil, // 36
		[]byte{0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00},
		[]byte{0xca, 0x3a, 0x2b, 0x03, 0x6d, 0xbc, 0x85, 0x02}},
	{nil, // 37
		[]byte{0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00},
		[]byte{0x5e, 0x09, 0x05, 0x51, 0x7b, 0xb5, 0x9b, 0xcf}},
	{nil, // 38
		[]byte{0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00},
		[]byte{0x81, 0x4e, 0xeb, 0x3b, 0x91, 0xd9, 0x07, 0x26}},
	{nil, // 39
		[]byte{0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00},
		[]byte{0x4d, 0x49, 0xdb, 0x15, 0x32, 0x91, 0x9c, 0x9f}},
	{nil, // 40
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00},
		[]byte{0x25, 0xeb, 0x5f, 0xc3, 0xf8, 0xcf, 0x06, 0x21}},
	{nil, // 41
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00},
		[]byte{0xab, 0x6a, 0x20, 0xc0, 0x62, 0x0d, 0x1c, 0x6f}},
	{nil, // 42
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00},
		[]byte{0x79, 0xe9, 0x0d, 0xbc, 0x98, 0xf9, 0x2c, 0xca}},
	{nil, // 43
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00},
		[]byte{0x86, 0x6e, 0xce, 0xdd, 0x80, 0x72, 0xbb, 0x0e}},
	{nil, // 44
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00},
		[]byte{0x8b, 0x54, 0x53, 0x6f, 0x2f, 0x3e, 0x64, 0xa8}},
	{nil, // 45
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00},
		[]byte{0xea, 0x51, 0xd3, 0x97, 0x55, 0x95, 0xb8, 0x6b}},
	{nil, // 46
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00},
		[]byte{0xca, 0xff, 0xc6, 0xac, 0x45, 0x42, 0xde, 0x31}},
	{nil, // 47
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00},
		[]byte{0x8d, 0xd4, 0x5a, 0x2d, 0xdf, 0x90, 0x79, 0x6c}},
	{nil, // 48
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00},
		[]byte{0x10, 0x29, 0xd5, 0x5e, 0x88, 0x0e, 0xc2, 0xd0}},
	{nil, // 49
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00},
		[]byte{0x5d, 0x86, 0xcb, 0x23, 0x63, 0x9d, 0xbe, 0xa9}},
	{nil, // 50
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00},
		[]byte{0x1d, 0x1c, 0xa8, 0x53, 0xae, 0x7c, 0x0c, 0x5f}},
	{nil, // 51
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00},
		[]byte{0xce, 0x33, 0x23, 0x29, 0x24, 0x8f, 0x32, 0x28}},
	{nil, // 52
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00},
		[]byte{0x84, 0x05, 0xd1, 0xab, 0xe2, 0x4f, 0xb9, 0x42}},
	{nil, // 53
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00},
		[]byte{0xe6, 0x43, 0xd7, 0x80, 0x90, 0xca, 0x42, 0x07}},
	{nil, // 54
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0x00},
		[]byte{0x48, 0x22, 0x1b, 0x99, 0x37, 0x74, 0x8a, 0x23}},
	{nil, // 55
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00},
		[]byte{0xdd, 0x7c, 0x0b, 0xbd, 0x61, 0xfa, 0xfd, 0x54}},
	{nil, // 56
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80},
		[]byte{0x2f, 0xbc, 0x29, 0x1a, 0x57, 0x0d, 0xb5, 0xc4}},
	{nil, // 57
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40},
		[]byte{0xe0, 0x7c, 0x30, 0xd7, 0xe4, 0xe2, 0x6e, 0x12}},
	{nil, // 58
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20},
		[]byte{0x09, 0x53, 0xe2, 0x25, 0x8e, 0x8e, 0x90, 0xa1}},
	{nil, // 59
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10},
		[]byte{0x5b, 0x71, 0x1b, 0xc4, 0xce, 0xeb, 0xf2, 0xee}},
	{nil, // 60
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08},
		[]byte{0xcc, 0x08, 0x3f, 0x1e, 0x6d, 0x9e, 0x85, 0xf6}},
	{nil, // 61
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04},
		[]byte{0xd2, 0xfd, 0x88, 0x67, 0xd5, 0x0d, 0x2d, 0xfe}},
	{nil, // 62
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02},
		[]byte{0x06, 0xe7, 0xea, 0x22, 0xce, 0x92, 0x70, 0x8f}},
	{nil, // 63
		[]byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01},
		[]byte{0x16, 0x6b, 0x40, 0xb4, 0x4a, 0xba, 0x4b, 0xd6}},
}

// Plaintext for use with Table A.2 tests
var tableA2Plaintext = []byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}

// Table A.2 Resulting Ciphertext from the Variable Key Known Answer Test
var tableA2Tests = []CryptTest{
	{ // 0
		[]byte{
			0x80, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x80, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x80, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01},
		nil,
		[]byte{0x95, 0xa8, 0xd7, 0x28, 0x13, 0xda, 0xa9, 0x4d}},
	{ // 1
		[]byte{
			0x40, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x40, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x40, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01},
		nil,
		[]byte{0x0e, 0xec, 0x14, 0x87, 0xdd, 0x8c, 0x26, 0xd5}},
	{ // 2
		[]byte{
			0x20, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x20, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x20, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01},
		nil,
		[]byte{0x7a, 0xd1, 0x6f, 0xfb, 0x79, 0xc4, 0x59, 0x26}},
	{ // 3
		[]byte{
			0x10, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x10, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x10, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01},
		nil,
		[]byte{0xd3, 0x74, 0x62, 0x94, 0xca, 0x6a, 0x6c, 0xf3}},
	{ // 4
		[]byte{
			0x08, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x08, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x08, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01},
		nil,
		[]byte{0x80, 0x9f, 0x5f, 0x87, 0x3c, 0x1f, 0xd7, 0x61}},
	{ // 5
		[]byte{
			0x04, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x04, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x04, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01},
		nil,
		[]byte{0xc0, 0x2f, 0xaf, 0xfe, 0xc9, 0x89, 0xd1, 0xfc}},
	{ // 6
		[]byte{
			0x02, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x02, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x02, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01},
		nil,
		[]byte{0x46, 0x15, 0xaa, 0x1d, 0x33, 0xe7, 0x2f, 0x10}},
	{ // 7
		[]byte{
			0x01, 0x80, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x80, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x80, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01},
		nil,
		[]byte{0x20, 0x55, 0x12, 0x33, 0x50, 0xc0, 0x08, 0x58}},
	{ // 8
		[]byte{
			0x01, 0x40, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x40, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x40, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01},
		nil,
		[]byte{0xdf, 0x3b, 0x99, 0xd6, 0x57, 0x73, 0x97, 0xc8}},
	{ // 9
		[]byte{
			0x01, 0x20, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x20, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x20, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01},
		nil,
		[]byte{0x31, 0xfe, 0x17, 0x36, 0x9b, 0x52, 0x88, 0xc9}},
	{ // 10
		[]byte{
			0x01, 0x10, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x10, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x10, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01},
		nil,
		[]byte{0xdf, 0xdd, 0x3c, 0xc6, 0x4d, 0xae, 0x16, 0x42}},
	{ // 11
		[]byte{
			0x01, 0x08, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x08, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x08, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01},
		nil,
		[]byte{0x17, 0x8c, 0x83, 0xce, 0x2b, 0x39, 0x9d, 0x94}},
	{ // 12
		[]byte{
			0x01, 0x04, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x04, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x04, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01},
		nil,
		[]byte{0x50, 0xf6, 0x36, 0x32, 0x4a, 0x9b, 0x7f, 0x80}},
	{ // 13
		[]byte{
			0x01, 0x02, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x02, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x02, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01},
		nil,
		[]byte{0xa8, 0x46, 0x8e, 0xe3, 0xbc, 0x18, 0xf0, 0x6d}},
	{ // 14
		[]byte{
			0x01, 0x01, 0x80, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x80, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x80, 0x01, 0x01, 0x01, 0x01, 0x01},
		nil,
		[]byte{0xa2, 0xdc, 0x9e, 0x92, 0xfd, 0x3c, 0xde, 0x92}},
	{ // 15
		[]byte{
			0x01, 0x01, 0x40, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x40, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x40, 0x01, 0x01, 0x01, 0x01, 0x01},
		nil,
		[]byte{0xca, 0xc0, 0x9f, 0x79, 0x7d, 0x03, 0x12, 0x87}},
	{ // 16
		[]byte{
			0x01, 0x01, 0x20, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x20, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x20, 0x01, 0x01, 0x01, 0x01, 0x01},
		nil,
		[]byte{0x90, 0xba, 0x68, 0x0b, 0x22, 0xae, 0xb5, 0x25}},
	{ // 17
		[]byte{
			0x01, 0x01, 0x10, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x10, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x10, 0x01, 0x01, 0x01, 0x01, 0x01},
		nil,
		[]byte{0xce, 0x7a, 0x24, 0xf3, 0x50, 0xe2, 0x80, 0xb6}},
	{ // 18
		[]byte{
			0x01, 0x01, 0x08, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x08, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x08, 0x01, 0x01, 0x01, 0x01, 0x01},
		nil,
		[]byte{0x88, 0x2b, 0xff, 0x0a, 0xa0, 0x1a, 0x0b, 0x87}},
	{ // 19
		[]byte{
			0x01, 0x01, 0x04, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x04, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x04, 0x01, 0x01, 0x01, 0x01, 0x01},
		nil,
		[]byte{0x25, 0x61, 0x02, 0x88, 0x92, 0x45, 0x11, 0xc2}},
	{ // 20
		[]byte{
			0x01, 0x01, 0x02, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x02, 0x01, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x02, 0x01, 0x01, 0x01, 0x01, 0x01},
		nil,
		[]byte{0xc7, 0x15, 0x16, 0xc2, 0x9c, 0x75, 0xd1, 0x70}},
	{ // 21
		[]byte{
			0x01, 0x01, 0x01, 0x80, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x80, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x80, 0x01, 0x01, 0x01, 0x01},
		nil,
		[]byte{0x51, 0x99, 0xc2, 0x9a, 0x52, 0xc9, 0xf0, 0x59}},
	{ // 22
		[]byte{
			0x01, 0x01, 0x01, 0x40, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x40, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x40, 0x01, 0x01, 0x01, 0x01},
		nil,
		[]byte{0xc2, 0x2f, 0x0a, 0x29, 0x4a, 0x71, 0xf2, 0x9f}},
	{ // 23
		[]byte{
			0x01, 0x01, 0x01, 0x20, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x20, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x20, 0x01, 0x01, 0x01, 0x01},
		nil,
		[]byte{0xee, 0x37, 0x14, 0x83, 0x71, 0x4c, 0x02, 0xea}},
	{ // 24
		[]byte{
			0x01, 0x01, 0x01, 0x10, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x10, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x10, 0x01, 0x01, 0x01, 0x01},
		nil,
		[]byte{0xa8, 0x1f, 0xbd, 0x44, 0x8f, 0x9e, 0x52, 0x2f}},
	{ // 25
		[]byte{
			0x01, 0x01, 0x01, 0x08, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x08, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x08, 0x01, 0x01, 0x01, 0x01},
		nil,
		[]byte{0x4f, 0x64, 0x4c, 0x92, 0xe1, 0x92, 0xdf, 0xed}},
	{ // 26
		[]byte{
			0x01, 0x01, 0x01, 0x04, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x04, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x04, 0x01, 0x01, 0x01, 0x01},
		nil,
		[]byte{0x1a, 0xfa, 0x9a, 0x66, 0xa6, 0xdf, 0x92, 0xae}},
	{ // 27
		[]byte{
			0x01, 0x01, 0x01, 0x02, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x02, 0x01, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x02, 0x01, 0x01, 0x01, 0x01},
		nil,
		[]byte{0xb3, 0xc1, 0xcc, 0x71, 0x5c, 0xb8, 0x79, 0xd8}},
	{ // 28
		[]byte{
			0x01, 0x01, 0x01, 0x01, 0x80, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x80, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x80, 0x01, 0x01, 0x01},
		nil,
		[]byte{0x19, 0xd0, 0x32, 0xe6, 0x4a, 0xb0, 0xbd, 0x8b}},
	{ // 29
		[]byte{
			0x01, 0x01, 0x01, 0x01, 0x40, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x40, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x40, 0x01, 0x01, 0x01},
		nil,
		[]byte{0x3c, 0xfa, 0xa7, 0xa7, 0xdc, 0x87, 0x20, 0xdc}},
	{ // 30
		[]byte{
			0x01, 0x01, 0x01, 0x01, 0x20, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x20, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x20, 0x01, 0x01, 0x01},
		nil,
		[]byte{0xb7, 0x26, 0x5f, 0x7f, 0x44, 0x7a, 0xc6, 0xf3}},
	{ // 31
		[]byte{
			0x01, 0x01, 0x01, 0x01, 0x10, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x10, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x10, 0x01, 0x01, 0x01},
		nil,
		[]byte{0x9d, 0xb7, 0x3b, 0x3c, 0x0d, 0x16, 0x3f, 0x54}},
	{ // 32
		[]byte{
			0x01, 0x01, 0x01, 0x01, 0x08, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x08, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x08, 0x01, 0x01, 0x01},
		nil,
		[]byte{0x81, 0x81, 0xb6, 0x5b, 0xab, 0xf4, 0xa9, 0x75}},
	{ // 33
		[]byte{
			0x01, 0x01, 0x01, 0x01, 0x04, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x04, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x04, 0x01, 0x01, 0x01},
		nil,
		[]byte{0x93, 0xc9, 0xb6, 0x40, 0x42, 0xea, 0xa2, 0x40}},
	{ // 34
		[]byte{
			0x01, 0x01, 0x01, 0x01, 0x02, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x02, 0x01, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x02, 0x01, 0x01, 0x01},
		nil,
		[]byte{0x55, 0x70, 0x53, 0x08, 0x29, 0x70, 0x55, 0x92}},
	{ // 35
		[]byte{
			0x01, 0x01, 0x01, 0x01, 0x01, 0x80, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x80, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x80, 0x01, 0x01},
		nil,
		[]byte{0x86, 0x38, 0x80, 0x9e, 0x87, 0x87, 0x87, 0xa0}},
	{ // 36
		[]byte{
			0x01, 0x01, 0x01, 0x01, 0x01, 0x40, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x40, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x40, 0x01, 0x01},
		nil,
		[]byte{0x41, 0xb9, 0xa7, 0x9a, 0xf7, 0x9a, 0xc2, 0x08}},
	{ // 37
		[]byte{
			0x01, 0x01, 0x01, 0x01, 0x01, 0x20, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x20, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x20, 0x01, 0x01},
		nil,
		[]byte{0x7a, 0x9b, 0xe4, 0x2f, 0x20, 0x09, 0xa8, 0x92}},
	{ // 38
		[]byte{
			0x01, 0x01, 0x01, 0x01, 0x01, 0x10, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x10, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x10, 0x01, 0x01},
		nil,
		[]byte{0x29, 0x03, 0x8d, 0x56, 0xba, 0x6d, 0x27, 0x45}},
	{ // 39
		[]byte{
			0x01, 0x01, 0x01, 0x01, 0x01, 0x08, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x08, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x08, 0x01, 0x01},
		nil,
		[]byte{0x54, 0x95, 0xc6, 0xab, 0xf1, 0xe5, 0xdf, 0x51}},
	{ // 40
		[]byte{
			0x01, 0x01, 0x01, 0x01, 0x01, 0x04, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x04, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x04, 0x01, 0x01},
		nil,
		[]byte{0xae, 0x13, 0xdb, 0xd5, 0x61, 0x48, 0x89, 0x33}},
	{ // 41
		[]byte{
			0x01, 0x01, 0x01, 0x01, 0x01, 0x02, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x02, 0x01, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x02, 0x01, 0x01},
		nil,
		[]byte{0x02, 0x4d, 0x1f, 0xfa, 0x89, 0x04, 0xe3, 0x89}},
	{ // 42
		[]byte{
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x80, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x80, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x80, 0x01},
		nil,
		[]byte{0xd1, 0x39, 0x97, 0x12, 0xf9, 0x9b, 0xf0, 0x2e}},
	{ // 43
		[]byte{
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x40, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x40, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x40, 0x01},
		nil,
		[]byte{0x14, 0xc1, 0xd7, 0xc1, 0xcf, 0xfe, 0xc7, 0x9e}},
	{ // 44
		[]byte{
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x20, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x20, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x20, 0x01},
		nil,
		[]byte{0x1d, 0xe5, 0x27, 0x9d, 0xae, 0x3b, 0xed, 0x6f}},
	{ // 45
		[]byte{
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x10, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x10, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x10, 0x01},
		nil,
		[]byte{0xe9, 0x41, 0xa3, 0x3f, 0x85, 0x50, 0x13, 0x03}},
	{ // 46
		[]byte{
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x08, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x08, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x08, 0x01},
		nil,
		[]byte{0xda, 0x99, 0xdb, 0xbc, 0x9a, 0x03, 0xf3, 0x79}},
	{ // 47
		[]byte{
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x04, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x04, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x04, 0x01},
		nil,
		[]byte{0xb7, 0xfc, 0x92, 0xf9, 0x1d, 0x8e, 0x92, 0xe9}},
	{ // 48
		[]byte{
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x02, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x02, 0x01,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x02, 0x01},
		nil,
		[]byte{0xae, 0x8e, 0x5c, 0xaa, 0x3c, 0xa0, 0x4e, 0x85}},
	{ // 49
		[]byte{
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x80,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x80,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x80},
		nil,
		[]byte{0x9c, 0xc6, 0x2d, 0xf4, 0x3b, 0x6e, 0xed, 0x74}},
	{ // 50
		[]byte{
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x40,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x40,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x40},
		nil,
		[]byte{0xd8, 0x63, 0xdb, 0xb5, 0xc5, 0x9a, 0x91, 0xa0}},
	{ // 50
		[]byte{
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x20,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x20,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x20},
		nil,
		[]byte{0xa1, 0xab, 0x21, 0x90, 0x54, 0x5b, 0x91, 0xd7}},
	{ // 52
		[]byte{
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x10,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x10,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x10},
		nil,
		[]byte{0x08, 0x75, 0x04, 0x1e, 0x64, 0xc5, 0x70, 0xf7}},
	{ // 53
		[]byte{
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x08,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x08,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x08},
		nil,
		[]byte{0x5a, 0x59, 0x45, 0x28, 0xbe, 0xbe, 0xf1, 0xcc}},
	{ // 54
		[]byte{
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x04,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x04,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x04},
		nil,
		[]byte{0xfc, 0xdb, 0x32, 0x91, 0xde, 0x21, 0xf0, 0xc0}},
	{ // 55
		[]byte{
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x02,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x02,
			0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x02},
		nil,
		[]byte{0x86, 0x9e, 0xfd, 0x7f, 0x9f, 0x26, 0x5a, 0x09}},
}

// Plaintext for use with Table A.3 tests
var tableA3Plaintext = []byte{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}

// Table A.3 Values To Be Used for the Permutation Operation Known Answer Test
var tableA3Tests = []CryptTest{
	{ // 0
		[]byte{
			0x10, 0x46, 0x91, 0x34, 0x89, 0x98, 0x01, 0x31,
			0x10, 0x46, 0x91, 0x34, 0x89, 0x98, 0x01, 0x31,
			0x10, 0x46, 0x91, 0x34, 0x89, 0x98, 0x01, 0x31,
		},
		nil,
		[]byte{0x88, 0xd5, 0x5e, 0x54, 0xf5, 0x4c, 0x97, 0xb4}},
	{ // 1
		[]byte{
			0x10, 0x07, 0x10, 0x34, 0x89, 0x98, 0x80, 0x20,
			0x10, 0x07, 0x10, 0x34, 0x89, 0x98, 0x80, 0x20,
			0x10, 0x07, 0x10, 0x34, 0x89, 0x98, 0x80, 0x20,
		},
		nil,
		[]byte{0x0c, 0x0c, 0xc0, 0x0c, 0x83, 0xea, 0x48, 0xfd}},
	{ // 2
		[]byte{
			0x10, 0x07, 0x10, 0x34, 0xc8, 0x98, 0x01, 0x20,
			0x10, 0x07, 0x10, 0x34, 0xc8, 0x98, 0x01, 0x20,
			0x10, 0x07, 0x10, 0x34, 0xc8, 0x98, 0x01, 0x20,
		},
		nil,
		[]byte{0x83, 0xbc, 0x8e, 0xf3, 0xa6, 0x57, 0x01, 0x83}},
	{ // 3
		[]byte{
			0x10, 0x46, 0x10, 0x34, 0x89, 0x98, 0x80, 0x20,
			0x10, 0x46, 0x10, 0x34, 0x89, 0x98, 0x80, 0x20,
			0x10, 0x46, 0x10, 0x34, 0x89, 0x98, 0x80, 0x20,
		},
		nil,
		[]byte{0xdf, 0x72, 0x5d, 0xca, 0xd9, 0x4e, 0xa2, 0xe9}},
	{ // 4
		[]byte{
			0x10, 0x86, 0x91, 0x15, 0x19, 0x19, 0x01, 0x01,
			0x10, 0x86, 0x91, 0x15, 0x19, 0x19, 0x01, 0x01,
			0x10, 0x86, 0x91, 0x15, 0x19, 0x19, 0x01, 0x01,
		},
		nil,
		[]byte{0xe6, 0x52, 0xb5, 0x3b, 0x55, 0x0b, 0xe8, 0xb0}},
	{ // 5
		[]byte{
			0x10, 0x86, 0x91, 0x15, 0x19, 0x58, 0x01, 0x01,
			0x10, 0x86, 0x91, 0x15, 0x19, 0x58, 0x01, 0x01,
			0x10, 0x86, 0x91, 0x15, 0x19, 0x58, 0x01, 0x01,
		},
		nil,
		[]byte{0xaf, 0x52, 0x71, 0x20, 0xc4, 0x85, 0xcb, 0xb0}},
	{ // 6
		[]byte{
			0x51, 0x07, 0xb0, 0x15, 0x19, 0x58, 0x01, 0x01,
			0x51, 0x07, 0xb0, 0x15, 0x19, 0x58, 0x01, 0x01,
			0x51, 0x07, 0xb0, 0x15, 0x19, 0x58, 0x01, 0x01,
		},
		nil,
		[]byte{0x0f, 0x04, 0xce, 0x39, 0x3d, 0xb9, 0x26, 0xd5}},
	{ // 7
		[]byte{
			0x10, 0x07, 0xb0, 0x15, 0x19, 0x19, 0x01, 0x01,
			0x10, 0x07, 0xb0, 0x15, 0x19, 0x19, 0x01, 0x01,
			0x10, 0x07, 0xb0, 0x15, 0x19, 0x19, 0x01, 0x01,
		},
		nil,
		[]byte{0xc9, 0xf0, 0x0f, 0xfc, 0x74, 0x07, 0x90, 0x67}},
	{ // 8
		[]byte{
			0x31, 0x07, 0x91, 0x54, 0x98, 0x08, 0x01, 0x01,
			0x31, 0x07, 0x91, 0x54, 0x98, 0x08, 0x01, 0x01,
			0x31, 0x07, 0x91, 0x54, 0x98, 0x08, 0x01, 0x01,
		},
		nil,
		[]byte{0x7c, 0xfd, 0x82, 0xa5, 0x93, 0x25, 0x2b, 0x4e}},
	{ // 9
		[]byte{
			0x31, 0x07, 0x91, 0x94, 0x98, 0x08, 0x01, 0x01,
			0x31, 0x07, 0x91, 0x94, 0x98, 0x08, 0x01, 0x01,
			0x31, 0x07, 0x91, 0x94, 0x98, 0x08, 0x01, 0x01,
		},
		nil,
		[]byte{0xcb, 0x49, 0xa2, 0xf9, 0xe9, 0x13, 0x63, 0xe3}},
	{ // 10
		[]byte{
			0x10, 0x07, 0x91, 0x15, 0xb9, 0x08, 0x01, 0x40,
			0x10, 0x07, 0x91, 0x15, 0xb9, 0x08, 0x01, 0x40,
			0x10, 0x07, 0x91, 0x15, 0xb9, 0x08, 0x01, 0x40,
		},
		nil,
		[]byte{0x00, 0xb5, 0x88, 0xbe, 0x70, 0xd2, 0x3f, 0x56}},
	{ // 11
		[]byte{
			0x31, 0x07, 0x91, 0x15, 0x98, 0x08, 0x01, 0x40,
			0x31, 0x07, 0x91, 0x15, 0x98, 0x08, 0x01, 0x40,
			0x31, 0x07, 0x91, 0x15, 0x98, 0x08, 0x01, 0x40,
		},
		nil,
		[]byte{0x40, 0x6a, 0x9a, 0x6a, 0xb4, 0x33, 0x99, 0xae}},
	{ // 12
		[]byte{
			0x10, 0x07, 0xd0, 0x15, 0x89, 0x98, 0x01, 0x01,
			0x10, 0x07, 0xd0, 0x15, 0x89, 0x98, 0x01, 0x01,
			0x10, 0x07, 0xd0, 0x15, 0x89, 0x98, 0x01, 0x01,
		},
		nil,
		[]byte{0x6c, 0xb7, 0x73, 0x61, 0x1d, 0xca, 0x9a, 0xda}},
	{ // 13
		[]byte{
			0x91, 0x07, 0x91, 0x15, 0x89, 0x98, 0x01, 0x01,
			0x91, 0x07, 0x91, 0x15, 0x89, 0x98, 0x01, 0x01,
			0x91, 0x07, 0x91, 0x15, 0x89, 0x98, 0x01, 0x01,
		},
		nil,
		[]byte{0x67, 0xfd, 0x21, 0xc1, 0x7d, 0xbb, 0x5d, 0x70}},
	{ // 14
		[]byte{
			0x91, 0x07, 0xd0, 0x15, 0x89, 0x19, 0x01, 0x01,
			0x91, 0x07, 0xd0, 0x15, 0x89, 0x19, 0x01, 0x01,
			0x91, 0x07, 0xd0, 0x15, 0x89, 0x19, 0x01, 0x01,
		},
		nil,
		[]byte{0x95, 0x92, 0xcb, 0x41, 0x10, 0x43, 0x07, 0x87}},
	{ // 15
		[]byte{
			0x10, 0x07, 0xd0, 0x15, 0x98, 0x98, 0x01, 0x20,
			0x10, 0x07, 0xd0, 0x15, 0x98, 0x98, 0x01, 0x20,
			0x10, 0x07, 0xd0, 0x15, 0x98, 0x98, 0x01, 0x20,
		},
		nil,
		[]byte{0xa6, 0xb7, 0xff, 0x68, 0xa3, 0x18, 0xdd, 0xd3}},
	{ // 16
		[]byte{
			0x10, 0x07, 0x94, 0x04, 0x98, 0x19, 0x01, 0x01,
			0x10, 0x07, 0x94, 0x04, 0x98, 0x19, 0x01, 0x01,
			0x10, 0x07, 0x94, 0x04, 0x98, 0x19, 0x01, 0x01,
		},
		nil,
		[]byte{0x4d, 0x10, 0x21, 0x96, 0xc9, 0x14, 0xca, 0x16}},
	{ // 17
		[]byte{
			0x01, 0x07, 0x91, 0x04, 0x91, 0x19, 0x04, 0x01,
			0x01, 0x07, 0x91, 0x04, 0x91, 0x19, 0x04, 0x01,
			0x01, 0x07, 0x91, 0x04, 0x91, 0x19, 0x04, 0x01,
		},
		nil,
		[]byte{0x2d, 0xfa, 0x9f, 0x45, 0x73, 0x59, 0x49, 0x65}},
	{ // 18
		[]byte{
			0x01, 0x07, 0x91, 0x04, 0x91, 0x19, 0x01, 0x01,
			0x01, 0x07, 0x91, 0x04, 0x91, 0x19, 0x01, 0x01,
			0x01, 0x07, 0x91, 0x04, 0x91, 0x19, 0x01, 0x01,
		},
		nil,
		[]byte{0xb4, 0x66, 0x04, 0x81, 0x6c, 0x0e, 0x07, 0x74}},
	{ // 19
		[]byte{
			0x01, 0x07, 0x94, 0x04, 0x91, 0x19, 0x04, 0x01,
			0x01, 0x07, 0x94, 0x04, 0x91, 0x19, 0x04, 0x01,
			0x01, 0x07, 0x94, 0x04, 0x91, 0x19, 0x04, 0x01,
		},
		nil,
		[]byte{0x6e, 0x7e, 0x62, 0x21, 0xa4, 0xf3, 0x4e, 0x87}},
	{ // 20
		[]byte{
			0x19, 0x07, 0x92, 0x10, 0x98, 0x1a, 0x01, 0x01,
			0x19, 0x07, 0x92, 0x10, 0x98, 0x1a, 0x01, 0x01,
			0x19, 0x07, 0x92, 0x10, 0x98, 0x1a, 0x01, 0x01,
		},
		nil,
		[]byte{0xaa, 0x85, 0xe7, 0x46, 0x43, 0x23, 0x31, 0x99}},
	{ // 21
		[]byte{
			0x10, 0x07, 0x91, 0x19, 0x98, 0x19, 0x08, 0x01,
			0x10, 0x07, 0x91, 0x19, 0x98, 0x19, 0x08, 0x01,
			0x10, 0x07, 0x91, 0x19, 0x98, 0x19, 0x08, 0x01,
		},
		nil,
		[]byte{0x2e, 0x5a, 0x19, 0xdb, 0x4d, 0x19, 0x62, 0xd6}},
	{ // 22
		[]byte{
			0x10, 0x07, 0x91, 0x19, 0x98, 0x1a, 0x08, 0x01,
			0x10, 0x07, 0x91, 0x19, 0x98, 0x1a, 0x08, 0x01,
			0x10, 0x07, 0x91, 0x19, 0x98, 0x1a, 0x08, 0x01,
		},
		nil,
		[]byte{0x23, 0xa8, 0x66, 0xa8, 0x09, 0xd3, 0x08, 0x94}},
	{ // 23
		[]byte{
			0x10, 0x07, 0x92, 0x10, 0x98, 0x19, 0x01, 0x01,
			0x10, 0x07, 0x92, 0x10, 0x98, 0x19, 0x01, 0x01,
			0x10, 0x07, 0x92, 0x10, 0x98, 0x19, 0x01, 0x01,
		},
		nil,
		[]byte{0xd8, 0x12, 0xd9, 0x61, 0xf0, 0x17, 0xd3, 0x20}},
	{ // 24
		[]byte{
			0x10, 0x07, 0x91, 0x15, 0x98, 0x19, 0x01, 0x0b,
			0x10, 0x07, 0x91, 0x15, 0x98, 0x19, 0x01, 0x0b,
			0x10, 0x07, 0x91, 0x15, 0x98, 0x19, 0x01, 0x0b,
		},
		nil,
		[]byte{0x05, 0x56, 0x05, 0x81, 0x6e, 0x58, 0x60, 0x8f}},
	{ // 25
		[]byte{
			0x10, 0x04, 0x80, 0x15, 0x98, 0x19, 0x01, 0x01,
			0x10, 0x04, 0x80, 0x15, 0x98, 0x19, 0x01, 0x01,
			0x10, 0x04, 0x80, 0x15, 0x98, 0x19, 0x01, 0x01,
		},
		nil,
		[]byte{0xab, 0xd8, 0x8e, 0x8b, 0x1b, 0x77, 0x16, 0xf1}},
	{ // 26
		[]byte{
			0x10, 0x04, 0x80, 0x15, 0x98, 0x19, 0x01, 0x02,
			0x10, 0x04, 0x80, 0x15, 0x98, 0x19, 0x01, 0x02,
			0x10, 0x04, 0x80, 0x15, 0x98, 0x19, 0x01, 0x02,
		},
		nil,
		[]byte{0x53, 0x7a, 0xc9, 0x5b, 0xe6, 0x9d, 0xa1, 0xe1}},
	{ // 27
		[]byte{
			0x10, 0x04, 0x80, 0x15, 0x98, 0x19, 0x01, 0x08,
			0x10, 0x04, 0x80, 0x15, 0x98, 0x19, 0x01, 0x08,
			0x10, 0x04, 0x80, 0x15, 0x98, 0x19, 0x01, 0x08,
		},
		nil,
		[]byte{0xae, 0xd0, 0xf6, 0xae, 0x3c, 0x25, 0xcd, 0xd8}},
	{ // 28
		[]byte{
			0x10, 0x02, 0x91, 0x15, 0x98, 0x10, 0x01, 0x04,
			0x10, 0x02, 0x91, 0x15, 0x98, 0x10, 0x01, 0x04,
			0x10, 0x02, 0x91, 0x15, 0x98, 0x10, 0x01, 0x04,
		},
		nil,
		[]byte{0xb3, 0xe3, 0x5a, 0x5e, 0xe5, 0x3e, 0x7b, 0x8d}},
	{ // 29
		[]byte{
			0x10, 0x02, 0x91, 0x15, 0x98, 0x19, 0x01, 0x04,
			0x10, 0x02, 0x91, 0x15, 0x98, 0x19, 0x01, 0x04,
			0x10, 0x02, 0x91, 0x15, 0x98, 0x19, 0x01, 0x04,
		},
		nil,
		[]byte{0x61, 0xc7, 0x9c, 0x71, 0x92, 0x1a, 0x2e, 0xf8}},
	{ // 30
		[]byte{
			0x10, 0x02, 0x91, 0x15, 0x98, 0x10, 0x02, 0x01,
			0x10, 0x02, 0x91, 0x15, 0x98, 0x10, 0x02, 0x01,
			0x10, 0x02, 0x91, 0x15, 0x98, 0x10, 0x02, 0x01,
		},
		nil,
		[]byte{0xe2, 0xf5, 0x72, 0x8f, 0x09, 0x95, 0x01, 0x3c}},
	{ // 31
		[]byte{
			0x10, 0x02, 0x91, 0x16, 0x98, 0x10, 0x01, 0x01,
			0x10, 0x02, 0x91, 0x16, 0x98, 0x10, 0x01, 0x01,
			0x10, 0x02, 0x91, 0x16, 0x98, 0x10, 0x01, 0x01,
		},
		nil,
		[]byte{0x1a, 0xea, 0xc3, 0x9a, 0x61, 0xf0, 0xa4, 0x64}},
}

// Table A.4 Values To Be Used for the Substitution Table Known Answer Test
var tableA4Tests = []CryptTest{
	{ // 0
		[]byte{
			0x7c, 0xa1, 0x10, 0x45, 0x4a, 0x1a, 0x6e, 0x57,
			0x7c, 0xa1, 0x10, 0x45, 0x4a, 0x1a, 0x6e, 0x57,
			0x7c, 0xa1, 0x10, 0x45, 0x4a, 0x1a, 0x6e, 0x57},
		[]byte{0x01, 0xa1, 0xd6, 0xd0, 0x39, 0x77, 0x67, 0x42},
		[]byte{0x69, 0x0f, 0x5b, 0x0d, 0x9a, 0x26, 0x93, 0x9b}},
	{ // 1
		[]byte{
			0x01, 0x31, 0xd9, 0x61, 0x9d, 0xc1, 0x37, 0x6e,
			0x01, 0x31, 0xd9, 0x61, 0x9d, 0xc1, 0x37, 0x6e,
			0x01, 0x31, 0xd9, 0x61, 0x9d, 0xc1, 0x37, 0x6e},
		[]byte{0x5c, 0xd5, 0x4c, 0xa8, 0x3d, 0xef, 0x57, 0xda},
		[]byte{0x7a, 0x38, 0x9d, 0x10, 0x35, 0x4b, 0xd2, 0x71}},
	{ // 2
		[]byte{
			0x07, 0xa1, 0x13, 0x3e, 0x4a, 0x0b, 0x26, 0x86,
			0x07, 0xa1, 0x13, 0x3e, 0x4a, 0x0b, 0x26, 0x86,
			0x07, 0xa1, 0x13, 0x3e, 0x4a, 0x0b, 0x26, 0x86},
		[]byte{0x02, 0x48, 0xd4, 0x38, 0x06, 0xf6, 0x71, 0x72},
		[]byte{0x86, 0x8e, 0xbb, 0x51, 0xca, 0xb4, 0x59, 0x9a}},
	{ // 3
		[]byte{
			0x38, 0x49, 0x67, 0x4c, 0x26, 0x02, 0x31, 0x9e,
			0x38, 0x49, 0x67, 0x4c, 0x26, 0x02, 0x31, 0x9e,
			0x38, 0x49, 0x67, 0x4c, 0x26, 0x02, 0x31, 0x9e},
		[]byte{0x51, 0x45, 0x4b, 0x58, 0x2d, 0xdf, 0x44, 0x0a},
		[]byte{0x71, 0x78, 0x87, 0x6e, 0x01, 0xf1, 0x9b, 0x2a}},
	{ // 4
		[]byte{
			0x04, 0xb9, 0x15, 0xba, 0x43, 0xfe, 0xb5, 0xb6,
			0x04, 0xb9, 0x15, 0xba, 0x43, 0xfe, 0xb5, 0xb6,
			0x04, 0xb9, 0x15, 0xba, 0x43, 0xfe, 0xb5, 0xb6},
		[]byte{0x42, 0xfd, 0x44, 0x30, 0x59, 0x57, 0x7f, 0xa2},
		[]byte{0xaf, 0x37, 0xfb, 0x42, 0x1f, 0x8c, 0x40, 0x95}},
	{ // 5
		[]byte{
			0x01, 0x13, 0xb9, 0x70, 0xfd, 0x34, 0xf2, 0xce,
			0x01, 0x13, 0xb9, 0x70, 0xfd, 0x34, 0xf2, 0xce,
			0x01, 0x13, 0xb9, 0x70, 0xfd, 0x34, 0xf2, 0xce},
		[]byte{0x05, 0x9b, 0x5e, 0x08, 0x51, 0xcf, 0x14, 0x3a},
		[]byte{0x86, 0xa5, 0x60, 0xf1, 0x0e, 0xc6, 0xd8, 0x5b}},
	{ // 6
		[]byte{
			0x01, 0x70, 0xf1, 0x75, 0x46, 0x8f, 0xb5, 0xe6,
			0x01, 0x70, 0xf1, 0x75, 0x46, 0x8f, 0xb5, 0xe6,
			0x01, 0x70, 0xf1, 0x75, 0x46, 0x8f, 0xb5, 0xe6},
		[]byte{0x07, 0x56, 0xd8, 0xe0, 0x77, 0x47, 0x61, 0xd2},
		[]byte{0x0c, 0xd3, 0xda, 0x02, 0x00, 0x21, 0xdc, 0x09}},
	{ // 7
		[]byte{
			0x43, 0x29, 0x7f, 0xad, 0x38, 0xe3, 0x73, 0xfe,
			0x43, 0x29, 0x7f, 0xad, 0x38, 0xe3, 0x73, 0xfe,
			0x43, 0x29, 0x7f, 0xad, 0x38, 0xe3, 0x73, 0xfe},
		[]byte{0x76, 0x25, 0x14, 0xb8, 0x29, 0xbf, 0x48, 0x6a},
		[]byte{0xea, 0x67, 0x6b, 0x2c, 0xb7, 0xdb, 0x2b, 0x7a}},
	{ // 8
		[]byte{
			0x07, 0xa7, 0x13, 0x70, 0x45, 0xda, 0x2a, 0x16,
			0x07, 0xa7, 0x13, 0x70, 0x45, 0xda, 0x2a, 0x16,
			0x07, 0xa7, 0x13, 0x70, 0x45, 0xda, 0x2a, 0x16},
		[]byte{0x3b, 0xdd, 0x11, 0x90, 0x49, 0x37, 0x28, 0x02},
		[]byte{0xdf, 0xd6, 0x4a, 0x81, 0x5c, 0xaf, 0x1a, 0x0f}},
	{ // 9
		[]byte{
			0x04, 0x68, 0x91, 0x04, 0xc2, 0xfd, 0x3b, 0x2f,
			0x04, 0x68, 0x91, 0x04, 0xc2, 0xfd, 0x3b, 0x2f,
			0x04, 0x68, 0x91, 0x04, 0xc2, 0xfd, 0x3b, 0x2f},
		[]byte{0x26, 0x95, 0x5f, 0x68, 0x35, 0xaf, 0x60, 0x9a},
		[]byte{0x5c, 0x51, 0x3c, 0x9c, 0x48, 0x86, 0xc0, 0x88}},
	{ // 10
		[]byte{
			0x37, 0xd0, 0x6b, 0xb5, 0x16, 0xcb, 0x75, 0x46,
			0x37, 0xd0, 0x6b, 0xb5, 0x16, 0xcb, 0x75, 0x46,
			0x37, 0xd0, 0x6b, 0xb5, 0x16, 0xcb, 0x75, 0x46},
		[]byte{0x16, 0x4d, 0x5e, 0x40, 0x4f, 0x27, 0x52, 0x32},
		[]byte{0x0a, 0x2a, 0xee, 0xae, 0x3f, 0xf4, 0xab, 0x77}},
	{ // 11
		[]byte{
			0x1f, 0x08, 0x26, 0x0d, 0x1a, 0xc2, 0x46, 0x5e,
			0x1f, 0x08, 0x26, 0x0d, 0x1a, 0xc2, 0x46, 0x5e,
			0x1f, 0x08, 0x26, 0x0d, 0x1a, 0xc2, 0x46, 0x5e},
		[]byte{0x6b, 0x05, 0x6e, 0x18, 0x75, 0x9f, 0x5c, 0xca},
		[]byte{0xef, 0x1b, 0xf0, 0x3e, 0x5d, 0xfa, 0x57, 0x5a}},
	{ // 12
		[]byte{
			0x58, 0x40, 0x23, 0x64, 0x1a, 0xba, 0x61, 0x76,
			0x58, 0x40, 0x23, 0x64, 0x1a, 0xba, 0x61, 0x76,
			0x58, 0x40, 0x23, 0x64, 0x1a, 0xba, 0x61, 0x76},
		[]byte{0x00, 0x4b, 0xd6, 0xef, 0x09, 0x17, 0x60, 0x62},
		[]byte{0x88, 0xbf, 0x0d, 0xb6, 0xd7, 0x0d, 0xee, 0x56}},
	{ // 13
		[]byte{
			0x02, 0x58, 0x16, 0x16, 0x46, 0x29, 0xb0, 0x07,
			0x02, 0x58, 0x16, 0x16, 0x46, 0x29, 0xb0, 0x07,
			0x02, 0x58, 0x16, 0x16, 0x46, 0x29, 0xb0, 0x07},
		[]byte{0x48, 0x0d, 0x39, 0x00, 0x6e, 0xe7, 0x62, 0xf2},
		[]byte{0xa1, 0xf9, 0x91, 0x55, 0x41, 0x02, 0x0b, 0x56}},
	{ // 14
		[]byte{
			0x49, 0x79, 0x3e, 0xbc, 0x79, 0xb3, 0x25, 0x8f,
			0x49, 0x79, 0x3e, 0xbc, 0x79, 0xb3, 0x25, 0x8f,
			0x49, 0x79, 0x3e, 0xbc, 0x79, 0xb3, 0x25, 0x8f},
		[]byte{0x43, 0x75, 0x40, 0xc8, 0x69, 0x8f, 0x3c, 0xfa},
		[]byte{0x6f, 0xbf, 0x1c, 0xaf, 0xcf, 0xfd, 0x05, 0x56}},
	{ // 15
		[]byte{
			0x4f, 0xb0, 0x5e, 0x15, 0x15, 0xab, 0x73, 0xa7,
			0x4f, 0xb0, 0x5e, 0x15, 0x15, 0xab, 0x73, 0xa7,
			0x4f, 0xb0, 0x5e, 0x15, 0x15, 0xab, 0x73, 0xa7},
		[]byte{0x07, 0x2d, 0x43, 0xa0, 0x77, 0x07, 0x52, 0x92},
		[]byte{0x2f, 0x22, 0xe4, 0x9b, 0xab, 0x7c, 0xa1, 0xac}},
	{ // 16
		[]byte{
			0x49, 0xe9, 0x5d, 0x6d, 0x4c, 0xa2, 0x29, 0xbf,
			0x49, 0xe9, 0x5d, 0x6d, 0x4c, 0xa2, 0x29, 0xbf,
			0x49, 0xe9, 0x5d, 0x6d, 0x4c, 0xa2, 0x29, 0xbf},
		[]byte{0x02, 0xfe, 0x55, 0x77, 0x81, 0x17, 0xf1, 0x2a},
		[]byte{0x5a, 0x6b, 0x61, 0x2c, 0xc2, 0x6c, 0xce, 0x4a}},
	{ // 17
		[]byte{
			0x01, 0x83, 0x10, 0xdc, 0x40, 0x9b, 0x26, 0xd6,
			0x01, 0x83, 0x10, 0xdc, 0x40, 0x9b, 0x26, 0xd6,
			0x01, 0x83, 0x10, 0xdc, 0x40, 0x9b, 0x26, 0xd6},
		[]byte{0x1d, 0x9d, 0x5c, 0x50, 0x18, 0xf7, 0x28, 0xc2},
		[]byte{0x5f, 0x4c, 0x03, 0x8e, 0xd1, 0x2b, 0x2e, 0x41}},
	{ // 18
		[]byte{
			0x1c, 0x58, 0x7f, 0x1c, 0x13, 0x92, 0x4f, 0xef,
			0x1c, 0x58, 0x7f, 0x1c, 0x13, 0x92, 0x4f, 0xef,
			0x1c, 0x58, 0x7f, 0x1c, 0x13, 0x92, 0x4f, 0xef},
		[]byte{0x30, 0x55, 0x32, 0x28, 0x6d, 0x6f, 0x29, 0x5a},
		[]byte{0x63, 0xfa, 0xc0, 0xd0, 0x34, 0xd9, 0xf7, 0x93}},
}

func newCipher(key []byte) cipher.Block {
	c, err := des.NewCipher(key)
	if err != nil {
		panic("NewCipher failed: " + err.Error())
	}
	return c
}

// Use the known weak keys to test DES implementation
func TestWeakKeys(t *testing.T) {
	for i, tt := range weakKeyTests {
		var encrypt = func(in []byte) (out []byte) {
			c := newCipher(tt.key)
			out = make([]byte, len(in))
			c.Encrypt(out, in)
			return
		}

		// Encrypting twice with a DES weak
		// key should reproduce the original input
		result := encrypt(tt.in)
		result = encrypt(result)

		if !bytes.Equal(result, tt.in) {
			t.Errorf("#%d: result: %x want: %x", i, result, tt.in)
		}
	}
}

// Use the known semi-weak key pairs to test DES implementation
func TestSemiWeakKeyPairs(t *testing.T) {
	for i, tt := range semiWeakKeyTests {
		var encrypt = func(key, in []byte) (out []byte) {
			c := newCipher(key)
			out = make([]byte, len(in))
			c.Encrypt(out, in)
			return
		}

		// Encrypting with one member of the semi-weak pair
		// and then encrypting the result with the other member
		// should reproduce the original input.
		result := encrypt(tt.key, tt.in)
		result = encrypt(tt.out, result)

		if !bytes.Equal(result, tt.in) {
			t.Errorf("#%d: result: %x want: %x", i, result, tt.in)
		}
	}
}

func TestDESEncryptBlock(t *testing.T) {
	for i, tt := range encryptDESTests {
		c := newCipher(tt.key)
		out := make([]byte, len(tt.in))
		c.Encrypt(out, tt.in)

		if !bytes.Equal(out, tt.out) {
			t.Errorf("#%d: result: %x want: %x", i, out, tt.out)
		}
	}
}

func TestDESDecryptBlock(t *testing.T) {
	for i, tt := range encryptDESTests {
		c := newCipher(tt.key)
		plain := make([]byte, len(tt.in))
		c.Decrypt(plain, tt.out)

		if !bytes.Equal(plain, tt.in) {
			t.Errorf("#%d: result: %x want: %x", i, plain, tt.in)
		}
	}
}

func TestEncryptTripleDES(t *testing.T) {
	for i, tt := range encryptTripleDESTests {
		c, _ := des.NewTripleDESCipher(tt.key)
		out := make([]byte, len(tt.in))
		c.Encrypt(out, tt.in)

		if !bytes.Equal(out, tt.out) {
			t.Errorf("#%d: result: %x want: %x", i, out, tt.out)
		}
	}
}

func TestDecryptTripleDES(t *testing.T) {
	for i, tt := range encryptTripleDESTests {
		c, _ := des.NewTripleDESCipher(tt.key)

		plain := make([]byte, len(tt.in))
		c.Decrypt(plain, tt.out)

		if !bytes.Equal(plain, tt.in) {
			t.Errorf("#%d: result: %x want: %x", i, plain, tt.in)
		}
	}
}

// Defined in Pub 800-20
func TestVariablePlaintextKnownAnswer(t *testing.T) {
	for i, tt := range tableA1Tests {
		c, _ := des.NewTripleDESCipher(tableA1Key)

		out := make([]byte, len(tt.in))
		c.Encrypt(out, tt.in)

		if !bytes.Equal(out, tt.out) {
			t.Errorf("#%d: result: %x want: %x", i, out, tt.out)
		}
	}
}

// Defined in Pub 800-20
func TestVariableCiphertextKnownAnswer(t *testing.T) {
	for i, tt := range tableA1Tests {
		c, _ := des.NewTripleDESCipher(tableA1Key)

		plain := make([]byte, len(tt.out))
		c.Decrypt(plain, tt.out)

		if !bytes.Equal(plain, tt.in) {
			t.Errorf("#%d: result: %x want: %x", i, plain, tt.in)
		}
	}
}

// Defined in Pub 800-20
// Encrypting the Table A.1 ciphertext with the
// 0x01... key produces the original plaintext
func TestInversePermutationKnownAnswer(t *testing.T) {
	for i, tt := range tableA1Tests {
		c, _ := des.NewTripleDESCipher(tableA1Key)

		plain := make([]byte, len(tt.in))
		c.Encrypt(plain, tt.out)

		if !bytes.Equal(plain, tt.in) {
			t.Errorf("#%d: result: %x want: %x", i, plain, tt.in)
		}
	}
}

// Defined in Pub 800-20
// Decrypting the Table A.1 plaintext with the
// 0x01... key produces the corresponding ciphertext
func TestInitialPermutationKnownAnswer(t *testing.T) {
	for i, tt := range tableA1Tests {
		c, _ := des.NewTripleDESCipher(tableA1Key)

		out := make([]byte, len(tt.in))
		c.Decrypt(out, tt.in)

		if !bytes.Equal(out, tt.out) {
			t.Errorf("#%d: result: %x want: %x", i, out, tt.out)
		}
	}
}

// Defined in Pub 800-20
func TestVariableKeyKnownAnswerEncrypt(t *testing.T) {
	for i, tt := range tableA2Tests {
		c, _ := des.NewTripleDESCipher(tt.key)

		out := make([]byte, len(tableA2Plaintext))
		c.Encrypt(out, tableA2Plaintext)

		if !bytes.Equal(out, tt.out) {
			t.Errorf("#%d: result: %x want: %x", i, out, tt.out)
		}
	}
}

// Defined in Pub 800-20
func TestVariableKeyKnownAnswerDecrypt(t *testing.T) {
	for i, tt := range tableA2Tests {
		c, _ := des.NewTripleDESCipher(tt.key)

		out := make([]byte, len(tt.out))
		c.Decrypt(out, tt.out)

		if !bytes.Equal(out, tableA2Plaintext) {
			t.Errorf("#%d: result: %x want: %x", i, out, tableA2Plaintext)
		}
	}
}

// Defined in Pub 800-20
func TestPermutationOperationKnownAnswerEncrypt(t *testing.T) {
	for i, tt := range tableA3Tests {
		c, _ := des.NewTripleDESCipher(tt.key)

		out := make([]byte, len(tableA3Plaintext))
		c.Encrypt(out, tableA3Plaintext)

		if !bytes.Equal(out, tt.out) {
			t.Errorf("#%d: result: %x want: %x", i, out, tt.out)
		}
	}
}

// Defined in Pub 800-20
func TestPermutationOperationKnownAnswerDecrypt(t *testing.T) {
	for i, tt := range tableA3Tests {
		c, _ := des.NewTripleDESCipher(tt.key)

		out := make([]byte, len(tt.out))
		c.Decrypt(out, tt.out)

		if !bytes.Equal(out, tableA3Plaintext) {
			t.Errorf("#%d: result: %x want: %x", i, out, tableA3Plaintext)
		}
	}
}

// Defined in Pub 800-20
func TestSubstitutionTableKnownAnswerEncrypt(t *testing.T) {
	for i, tt := range tableA4Tests {
		c, _ := des.NewTripleDESCipher(tt.key)

		out := make([]byte, len(tt.in))
		c.Encrypt(out, tt.in)

		if !bytes.Equal(out, tt.out) {
			t.Errorf("#%d: result: %x want: %x", i, out, tt.out)
		}
	}
}

// Defined in Pub 800-20
func TestSubstitutionTableKnownAnswerDecrypt(t *testing.T) {
	for i, tt := range tableA4Tests {
		c, _ := des.NewTripleDESCipher(tt.key)

		out := make([]byte, len(tt.out))
		c.Decrypt(out, tt.out)

		if !bytes.Equal(out, tt.in) {
			t.Errorf("#%d: result: %x want: %x", i, out, tt.in)
		}
	}
}

// Test DES against the general cipher.Block interface tester
func TestDESBlock(t *testing.T) {
	t.Run("DES", func(t *testing.T) {
		cryptotest.TestBlock(t, 8, des.NewCipher)
	})

	t.Run("TripleDES", func(t *testing.T) {
		cryptotest.TestBlock(t, 24, des.NewTripleDESCipher)
	})
}

func BenchmarkEncrypt(b *testing.B) {
	tt := encryptDESTests[0]
	c, err := des.NewCipher(tt.key)
	if err != nil {
		b.Fatal("NewCipher:", err)
	}
	out := make([]byte, len(tt.in))
	b.SetBytes(int64(len(out)))
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		c.Encrypt(out, tt.in)
	}
}

func BenchmarkDecrypt(b *testing.B) {
	tt := encryptDESTests[0]
	c, err := des.NewCipher(tt.key)
	if err != nil {
		b.Fatal("NewCipher:", err)
	}
	out := make([]byte, len(tt.out))
	b.SetBytes(int64(len(out)))
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		c.Decrypt(out, tt.out)
	}
}

func BenchmarkTDESEncrypt(b *testing.B) {
	tt := encryptTripleDESTests[0]
	c, err := des.NewTripleDESCipher(tt.key)
	if err != nil {
		b.Fatal("NewCipher:", err)
	}
	out := make([]byte, len(tt.in))
	b.SetBytes(int64(len(out)))
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		c.Encrypt(out, tt.in)
	}
}

func BenchmarkTDESDecrypt(b *testing.B) {
	tt := encryptTripleDESTests[0]
	c, err := des.NewTripleDESCipher(tt.key)
	if err != nil {
		b.Fatal("NewCipher:", err)
	}
	out := make([]byte, len(tt.out))
	b.SetBytes(int64(len(out)))
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		c.Decrypt(out, tt.out)
	}
}
