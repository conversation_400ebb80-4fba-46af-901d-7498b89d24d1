Vet is a tool that checks correctness of Go programs. It runs a suite of tests,
each tailored to check for a particular class of errors. Examples include incorrect
Printf format verbs and malformed build tags.

Over time many checks have been added to vet's suite, but many more have been
rejected as not appropriate for the tool. The criteria applied when selecting which
checks to add are:

Correctness:

Vet's checks are about correctness, not style. A vet check must identify real or
potential bugs that could cause incorrect compilation or execution. A check that
only identifies stylistic points or alternative correct approaches to a situation
is not acceptable.

Frequency:

Vet is run every day by many programmers, often as part of every compilation or
submission. The cost in execution time is considerable, especially in aggregate,
so checks must be likely enough to find real problems that they are worth the
overhead of the added check. A new check that finds only a handful of problems
across all existing programs, even if the problem is significant, is not worth
adding to the suite everyone runs daily.

Precision:

Most of vet's checks are heuristic and can generate both false positives (flagging
correct programs) and false negatives (not flagging incorrect ones). The rate of
both these failures must be very small. A check that is too noisy will be ignored
by the programmer overwhelmed by the output; a check that misses too many of the
cases it's looking for will give a false sense of security. Neither is acceptable.
A vet check must be accurate enough that everything it reports is worth examining,
and complete enough to encourage real confidence.
