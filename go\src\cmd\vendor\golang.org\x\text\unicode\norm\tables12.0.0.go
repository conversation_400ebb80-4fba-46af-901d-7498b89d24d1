// Code generated by running "go generate" in golang.org/x/text. DO NOT EDIT.

//go:build go1.14 && !go1.16

package norm

import "sync"

const (
	// Version is the Unicode edition from which the tables are derived.
	Version = "12.0.0"

	// MaxTransformChunkSize indicates the maximum number of bytes that Transform
	// may need to write atomically for any Form. Making a destination buffer at
	// least this size ensures that Transform can always make progress and that
	// the user does not need to grow the buffer on an ErrShortDst.
	MaxTransformChunkSize = 35 + maxNonStarters*4
)

var ccc = [55]uint8{
	0, 1, 7, 8, 9, 10, 11, 12,
	13, 14, 15, 16, 17, 18, 19, 20,
	21, 22, 23, 24, 25, 26, 27, 28,
	29, 30, 31, 32, 33, 34, 35, 36,
	84, 91, 103, 107, 118, 122, 129, 130,
	132, 202, 214, 216, 218, 220, 222, 224,
	226, 228, 230, 232, 233, 234, 240,
}

const (
	firstMulti            = 0x186D
	firstCCC              = 0x2CA1
	endMulti              = 0x2F63
	firstLeadingCCC       = 0x49B1
	firstCCCZeroExcept    = 0x4A7B
	firstStarterWithNLead = 0x4AA2
	lastDecomp            = 0x4AA4
	maxDecomp             = 0x8000
)

// decomps: 19108 bytes
var decomps = [...]byte{
	// Bytes 0 - 3f
	0x00, 0x41, 0x20, 0x41, 0x21, 0x41, 0x22, 0x41,
	0x23, 0x41, 0x24, 0x41, 0x25, 0x41, 0x26, 0x41,
	0x27, 0x41, 0x28, 0x41, 0x29, 0x41, 0x2A, 0x41,
	0x2B, 0x41, 0x2C, 0x41, 0x2D, 0x41, 0x2E, 0x41,
	0x2F, 0x41, 0x30, 0x41, 0x31, 0x41, 0x32, 0x41,
	0x33, 0x41, 0x34, 0x41, 0x35, 0x41, 0x36, 0x41,
	0x37, 0x41, 0x38, 0x41, 0x39, 0x41, 0x3A, 0x41,
	0x3B, 0x41, 0x3C, 0x41, 0x3D, 0x41, 0x3E, 0x41,
	// Bytes 40 - 7f
	0x3F, 0x41, 0x40, 0x41, 0x41, 0x41, 0x42, 0x41,
	0x43, 0x41, 0x44, 0x41, 0x45, 0x41, 0x46, 0x41,
	0x47, 0x41, 0x48, 0x41, 0x49, 0x41, 0x4A, 0x41,
	0x4B, 0x41, 0x4C, 0x41, 0x4D, 0x41, 0x4E, 0x41,
	0x4F, 0x41, 0x50, 0x41, 0x51, 0x41, 0x52, 0x41,
	0x53, 0x41, 0x54, 0x41, 0x55, 0x41, 0x56, 0x41,
	0x57, 0x41, 0x58, 0x41, 0x59, 0x41, 0x5A, 0x41,
	0x5B, 0x41, 0x5C, 0x41, 0x5D, 0x41, 0x5E, 0x41,
	// Bytes 80 - bf
	0x5F, 0x41, 0x60, 0x41, 0x61, 0x41, 0x62, 0x41,
	0x63, 0x41, 0x64, 0x41, 0x65, 0x41, 0x66, 0x41,
	0x67, 0x41, 0x68, 0x41, 0x69, 0x41, 0x6A, 0x41,
	0x6B, 0x41, 0x6C, 0x41, 0x6D, 0x41, 0x6E, 0x41,
	0x6F, 0x41, 0x70, 0x41, 0x71, 0x41, 0x72, 0x41,
	0x73, 0x41, 0x74, 0x41, 0x75, 0x41, 0x76, 0x41,
	0x77, 0x41, 0x78, 0x41, 0x79, 0x41, 0x7A, 0x41,
	0x7B, 0x41, 0x7C, 0x41, 0x7D, 0x41, 0x7E, 0x42,
	// Bytes c0 - ff
	0xC2, 0xA2, 0x42, 0xC2, 0xA3, 0x42, 0xC2, 0xA5,
	0x42, 0xC2, 0xA6, 0x42, 0xC2, 0xAC, 0x42, 0xC2,
	0xB7, 0x42, 0xC3, 0x86, 0x42, 0xC3, 0xB0, 0x42,
	0xC4, 0xA6, 0x42, 0xC4, 0xA7, 0x42, 0xC4, 0xB1,
	0x42, 0xC5, 0x8B, 0x42, 0xC5, 0x93, 0x42, 0xC6,
	0x8E, 0x42, 0xC6, 0x90, 0x42, 0xC6, 0xAB, 0x42,
	0xC8, 0xA2, 0x42, 0xC8, 0xB7, 0x42, 0xC9, 0x90,
	0x42, 0xC9, 0x91, 0x42, 0xC9, 0x92, 0x42, 0xC9,
	// Bytes 100 - 13f
	0x94, 0x42, 0xC9, 0x95, 0x42, 0xC9, 0x99, 0x42,
	0xC9, 0x9B, 0x42, 0xC9, 0x9C, 0x42, 0xC9, 0x9F,
	0x42, 0xC9, 0xA1, 0x42, 0xC9, 0xA3, 0x42, 0xC9,
	0xA5, 0x42, 0xC9, 0xA6, 0x42, 0xC9, 0xA8, 0x42,
	0xC9, 0xA9, 0x42, 0xC9, 0xAA, 0x42, 0xC9, 0xAB,
	0x42, 0xC9, 0xAD, 0x42, 0xC9, 0xAF, 0x42, 0xC9,
	0xB0, 0x42, 0xC9, 0xB1, 0x42, 0xC9, 0xB2, 0x42,
	0xC9, 0xB3, 0x42, 0xC9, 0xB4, 0x42, 0xC9, 0xB5,
	// Bytes 140 - 17f
	0x42, 0xC9, 0xB8, 0x42, 0xC9, 0xB9, 0x42, 0xC9,
	0xBB, 0x42, 0xCA, 0x81, 0x42, 0xCA, 0x82, 0x42,
	0xCA, 0x83, 0x42, 0xCA, 0x89, 0x42, 0xCA, 0x8A,
	0x42, 0xCA, 0x8B, 0x42, 0xCA, 0x8C, 0x42, 0xCA,
	0x90, 0x42, 0xCA, 0x91, 0x42, 0xCA, 0x92, 0x42,
	0xCA, 0x95, 0x42, 0xCA, 0x9D, 0x42, 0xCA, 0x9F,
	0x42, 0xCA, 0xB9, 0x42, 0xCE, 0x91, 0x42, 0xCE,
	0x92, 0x42, 0xCE, 0x93, 0x42, 0xCE, 0x94, 0x42,
	// Bytes 180 - 1bf
	0xCE, 0x95, 0x42, 0xCE, 0x96, 0x42, 0xCE, 0x97,
	0x42, 0xCE, 0x98, 0x42, 0xCE, 0x99, 0x42, 0xCE,
	0x9A, 0x42, 0xCE, 0x9B, 0x42, 0xCE, 0x9C, 0x42,
	0xCE, 0x9D, 0x42, 0xCE, 0x9E, 0x42, 0xCE, 0x9F,
	0x42, 0xCE, 0xA0, 0x42, 0xCE, 0xA1, 0x42, 0xCE,
	0xA3, 0x42, 0xCE, 0xA4, 0x42, 0xCE, 0xA5, 0x42,
	0xCE, 0xA6, 0x42, 0xCE, 0xA7, 0x42, 0xCE, 0xA8,
	0x42, 0xCE, 0xA9, 0x42, 0xCE, 0xB1, 0x42, 0xCE,
	// Bytes 1c0 - 1ff
	0xB2, 0x42, 0xCE, 0xB3, 0x42, 0xCE, 0xB4, 0x42,
	0xCE, 0xB5, 0x42, 0xCE, 0xB6, 0x42, 0xCE, 0xB7,
	0x42, 0xCE, 0xB8, 0x42, 0xCE, 0xB9, 0x42, 0xCE,
	0xBA, 0x42, 0xCE, 0xBB, 0x42, 0xCE, 0xBC, 0x42,
	0xCE, 0xBD, 0x42, 0xCE, 0xBE, 0x42, 0xCE, 0xBF,
	0x42, 0xCF, 0x80, 0x42, 0xCF, 0x81, 0x42, 0xCF,
	0x82, 0x42, 0xCF, 0x83, 0x42, 0xCF, 0x84, 0x42,
	0xCF, 0x85, 0x42, 0xCF, 0x86, 0x42, 0xCF, 0x87,
	// Bytes 200 - 23f
	0x42, 0xCF, 0x88, 0x42, 0xCF, 0x89, 0x42, 0xCF,
	0x9C, 0x42, 0xCF, 0x9D, 0x42, 0xD0, 0xBD, 0x42,
	0xD1, 0x8A, 0x42, 0xD1, 0x8C, 0x42, 0xD7, 0x90,
	0x42, 0xD7, 0x91, 0x42, 0xD7, 0x92, 0x42, 0xD7,
	0x93, 0x42, 0xD7, 0x94, 0x42, 0xD7, 0x9B, 0x42,
	0xD7, 0x9C, 0x42, 0xD7, 0x9D, 0x42, 0xD7, 0xA2,
	0x42, 0xD7, 0xA8, 0x42, 0xD7, 0xAA, 0x42, 0xD8,
	0xA1, 0x42, 0xD8, 0xA7, 0x42, 0xD8, 0xA8, 0x42,
	// Bytes 240 - 27f
	0xD8, 0xA9, 0x42, 0xD8, 0xAA, 0x42, 0xD8, 0xAB,
	0x42, 0xD8, 0xAC, 0x42, 0xD8, 0xAD, 0x42, 0xD8,
	0xAE, 0x42, 0xD8, 0xAF, 0x42, 0xD8, 0xB0, 0x42,
	0xD8, 0xB1, 0x42, 0xD8, 0xB2, 0x42, 0xD8, 0xB3,
	0x42, 0xD8, 0xB4, 0x42, 0xD8, 0xB5, 0x42, 0xD8,
	0xB6, 0x42, 0xD8, 0xB7, 0x42, 0xD8, 0xB8, 0x42,
	0xD8, 0xB9, 0x42, 0xD8, 0xBA, 0x42, 0xD9, 0x81,
	0x42, 0xD9, 0x82, 0x42, 0xD9, 0x83, 0x42, 0xD9,
	// Bytes 280 - 2bf
	0x84, 0x42, 0xD9, 0x85, 0x42, 0xD9, 0x86, 0x42,
	0xD9, 0x87, 0x42, 0xD9, 0x88, 0x42, 0xD9, 0x89,
	0x42, 0xD9, 0x8A, 0x42, 0xD9, 0xAE, 0x42, 0xD9,
	0xAF, 0x42, 0xD9, 0xB1, 0x42, 0xD9, 0xB9, 0x42,
	0xD9, 0xBA, 0x42, 0xD9, 0xBB, 0x42, 0xD9, 0xBE,
	0x42, 0xD9, 0xBF, 0x42, 0xDA, 0x80, 0x42, 0xDA,
	0x83, 0x42, 0xDA, 0x84, 0x42, 0xDA, 0x86, 0x42,
	0xDA, 0x87, 0x42, 0xDA, 0x88, 0x42, 0xDA, 0x8C,
	// Bytes 2c0 - 2ff
	0x42, 0xDA, 0x8D, 0x42, 0xDA, 0x8E, 0x42, 0xDA,
	0x91, 0x42, 0xDA, 0x98, 0x42, 0xDA, 0xA1, 0x42,
	0xDA, 0xA4, 0x42, 0xDA, 0xA6, 0x42, 0xDA, 0xA9,
	0x42, 0xDA, 0xAD, 0x42, 0xDA, 0xAF, 0x42, 0xDA,
	0xB1, 0x42, 0xDA, 0xB3, 0x42, 0xDA, 0xBA, 0x42,
	0xDA, 0xBB, 0x42, 0xDA, 0xBE, 0x42, 0xDB, 0x81,
	0x42, 0xDB, 0x85, 0x42, 0xDB, 0x86, 0x42, 0xDB,
	0x87, 0x42, 0xDB, 0x88, 0x42, 0xDB, 0x89, 0x42,
	// Bytes 300 - 33f
	0xDB, 0x8B, 0x42, 0xDB, 0x8C, 0x42, 0xDB, 0x90,
	0x42, 0xDB, 0x92, 0x43, 0xE0, 0xBC, 0x8B, 0x43,
	0xE1, 0x83, 0x9C, 0x43, 0xE1, 0x84, 0x80, 0x43,
	0xE1, 0x84, 0x81, 0x43, 0xE1, 0x84, 0x82, 0x43,
	0xE1, 0x84, 0x83, 0x43, 0xE1, 0x84, 0x84, 0x43,
	0xE1, 0x84, 0x85, 0x43, 0xE1, 0x84, 0x86, 0x43,
	0xE1, 0x84, 0x87, 0x43, 0xE1, 0x84, 0x88, 0x43,
	0xE1, 0x84, 0x89, 0x43, 0xE1, 0x84, 0x8A, 0x43,
	// Bytes 340 - 37f
	0xE1, 0x84, 0x8B, 0x43, 0xE1, 0x84, 0x8C, 0x43,
	0xE1, 0x84, 0x8D, 0x43, 0xE1, 0x84, 0x8E, 0x43,
	0xE1, 0x84, 0x8F, 0x43, 0xE1, 0x84, 0x90, 0x43,
	0xE1, 0x84, 0x91, 0x43, 0xE1, 0x84, 0x92, 0x43,
	0xE1, 0x84, 0x94, 0x43, 0xE1, 0x84, 0x95, 0x43,
	0xE1, 0x84, 0x9A, 0x43, 0xE1, 0x84, 0x9C, 0x43,
	0xE1, 0x84, 0x9D, 0x43, 0xE1, 0x84, 0x9E, 0x43,
	0xE1, 0x84, 0xA0, 0x43, 0xE1, 0x84, 0xA1, 0x43,
	// Bytes 380 - 3bf
	0xE1, 0x84, 0xA2, 0x43, 0xE1, 0x84, 0xA3, 0x43,
	0xE1, 0x84, 0xA7, 0x43, 0xE1, 0x84, 0xA9, 0x43,
	0xE1, 0x84, 0xAB, 0x43, 0xE1, 0x84, 0xAC, 0x43,
	0xE1, 0x84, 0xAD, 0x43, 0xE1, 0x84, 0xAE, 0x43,
	0xE1, 0x84, 0xAF, 0x43, 0xE1, 0x84, 0xB2, 0x43,
	0xE1, 0x84, 0xB6, 0x43, 0xE1, 0x85, 0x80, 0x43,
	0xE1, 0x85, 0x87, 0x43, 0xE1, 0x85, 0x8C, 0x43,
	0xE1, 0x85, 0x97, 0x43, 0xE1, 0x85, 0x98, 0x43,
	// Bytes 3c0 - 3ff
	0xE1, 0x85, 0x99, 0x43, 0xE1, 0x85, 0xA0, 0x43,
	0xE1, 0x86, 0x84, 0x43, 0xE1, 0x86, 0x85, 0x43,
	0xE1, 0x86, 0x88, 0x43, 0xE1, 0x86, 0x91, 0x43,
	0xE1, 0x86, 0x92, 0x43, 0xE1, 0x86, 0x94, 0x43,
	0xE1, 0x86, 0x9E, 0x43, 0xE1, 0x86, 0xA1, 0x43,
	0xE1, 0x87, 0x87, 0x43, 0xE1, 0x87, 0x88, 0x43,
	0xE1, 0x87, 0x8C, 0x43, 0xE1, 0x87, 0x8E, 0x43,
	0xE1, 0x87, 0x93, 0x43, 0xE1, 0x87, 0x97, 0x43,
	// Bytes 400 - 43f
	0xE1, 0x87, 0x99, 0x43, 0xE1, 0x87, 0x9D, 0x43,
	0xE1, 0x87, 0x9F, 0x43, 0xE1, 0x87, 0xB1, 0x43,
	0xE1, 0x87, 0xB2, 0x43, 0xE1, 0xB4, 0x82, 0x43,
	0xE1, 0xB4, 0x96, 0x43, 0xE1, 0xB4, 0x97, 0x43,
	0xE1, 0xB4, 0x9C, 0x43, 0xE1, 0xB4, 0x9D, 0x43,
	0xE1, 0xB4, 0xA5, 0x43, 0xE1, 0xB5, 0xBB, 0x43,
	0xE1, 0xB6, 0x85, 0x43, 0xE2, 0x80, 0x82, 0x43,
	0xE2, 0x80, 0x83, 0x43, 0xE2, 0x80, 0x90, 0x43,
	// Bytes 440 - 47f
	0xE2, 0x80, 0x93, 0x43, 0xE2, 0x80, 0x94, 0x43,
	0xE2, 0x82, 0xA9, 0x43, 0xE2, 0x86, 0x90, 0x43,
	0xE2, 0x86, 0x91, 0x43, 0xE2, 0x86, 0x92, 0x43,
	0xE2, 0x86, 0x93, 0x43, 0xE2, 0x88, 0x82, 0x43,
	0xE2, 0x88, 0x87, 0x43, 0xE2, 0x88, 0x91, 0x43,
	0xE2, 0x88, 0x92, 0x43, 0xE2, 0x94, 0x82, 0x43,
	0xE2, 0x96, 0xA0, 0x43, 0xE2, 0x97, 0x8B, 0x43,
	0xE2, 0xA6, 0x85, 0x43, 0xE2, 0xA6, 0x86, 0x43,
	// Bytes 480 - 4bf
	0xE2, 0xB5, 0xA1, 0x43, 0xE3, 0x80, 0x81, 0x43,
	0xE3, 0x80, 0x82, 0x43, 0xE3, 0x80, 0x88, 0x43,
	0xE3, 0x80, 0x89, 0x43, 0xE3, 0x80, 0x8A, 0x43,
	0xE3, 0x80, 0x8B, 0x43, 0xE3, 0x80, 0x8C, 0x43,
	0xE3, 0x80, 0x8D, 0x43, 0xE3, 0x80, 0x8E, 0x43,
	0xE3, 0x80, 0x8F, 0x43, 0xE3, 0x80, 0x90, 0x43,
	0xE3, 0x80, 0x91, 0x43, 0xE3, 0x80, 0x92, 0x43,
	0xE3, 0x80, 0x94, 0x43, 0xE3, 0x80, 0x95, 0x43,
	// Bytes 4c0 - 4ff
	0xE3, 0x80, 0x96, 0x43, 0xE3, 0x80, 0x97, 0x43,
	0xE3, 0x82, 0xA1, 0x43, 0xE3, 0x82, 0xA2, 0x43,
	0xE3, 0x82, 0xA3, 0x43, 0xE3, 0x82, 0xA4, 0x43,
	0xE3, 0x82, 0xA5, 0x43, 0xE3, 0x82, 0xA6, 0x43,
	0xE3, 0x82, 0xA7, 0x43, 0xE3, 0x82, 0xA8, 0x43,
	0xE3, 0x82, 0xA9, 0x43, 0xE3, 0x82, 0xAA, 0x43,
	0xE3, 0x82, 0xAB, 0x43, 0xE3, 0x82, 0xAD, 0x43,
	0xE3, 0x82, 0xAF, 0x43, 0xE3, 0x82, 0xB1, 0x43,
	// Bytes 500 - 53f
	0xE3, 0x82, 0xB3, 0x43, 0xE3, 0x82, 0xB5, 0x43,
	0xE3, 0x82, 0xB7, 0x43, 0xE3, 0x82, 0xB9, 0x43,
	0xE3, 0x82, 0xBB, 0x43, 0xE3, 0x82, 0xBD, 0x43,
	0xE3, 0x82, 0xBF, 0x43, 0xE3, 0x83, 0x81, 0x43,
	0xE3, 0x83, 0x83, 0x43, 0xE3, 0x83, 0x84, 0x43,
	0xE3, 0x83, 0x86, 0x43, 0xE3, 0x83, 0x88, 0x43,
	0xE3, 0x83, 0x8A, 0x43, 0xE3, 0x83, 0x8B, 0x43,
	0xE3, 0x83, 0x8C, 0x43, 0xE3, 0x83, 0x8D, 0x43,
	// Bytes 540 - 57f
	0xE3, 0x83, 0x8E, 0x43, 0xE3, 0x83, 0x8F, 0x43,
	0xE3, 0x83, 0x92, 0x43, 0xE3, 0x83, 0x95, 0x43,
	0xE3, 0x83, 0x98, 0x43, 0xE3, 0x83, 0x9B, 0x43,
	0xE3, 0x83, 0x9E, 0x43, 0xE3, 0x83, 0x9F, 0x43,
	0xE3, 0x83, 0xA0, 0x43, 0xE3, 0x83, 0xA1, 0x43,
	0xE3, 0x83, 0xA2, 0x43, 0xE3, 0x83, 0xA3, 0x43,
	0xE3, 0x83, 0xA4, 0x43, 0xE3, 0x83, 0xA5, 0x43,
	0xE3, 0x83, 0xA6, 0x43, 0xE3, 0x83, 0xA7, 0x43,
	// Bytes 580 - 5bf
	0xE3, 0x83, 0xA8, 0x43, 0xE3, 0x83, 0xA9, 0x43,
	0xE3, 0x83, 0xAA, 0x43, 0xE3, 0x83, 0xAB, 0x43,
	0xE3, 0x83, 0xAC, 0x43, 0xE3, 0x83, 0xAD, 0x43,
	0xE3, 0x83, 0xAF, 0x43, 0xE3, 0x83, 0xB0, 0x43,
	0xE3, 0x83, 0xB1, 0x43, 0xE3, 0x83, 0xB2, 0x43,
	0xE3, 0x83, 0xB3, 0x43, 0xE3, 0x83, 0xBB, 0x43,
	0xE3, 0x83, 0xBC, 0x43, 0xE3, 0x92, 0x9E, 0x43,
	0xE3, 0x92, 0xB9, 0x43, 0xE3, 0x92, 0xBB, 0x43,
	// Bytes 5c0 - 5ff
	0xE3, 0x93, 0x9F, 0x43, 0xE3, 0x94, 0x95, 0x43,
	0xE3, 0x9B, 0xAE, 0x43, 0xE3, 0x9B, 0xBC, 0x43,
	0xE3, 0x9E, 0x81, 0x43, 0xE3, 0xA0, 0xAF, 0x43,
	0xE3, 0xA1, 0xA2, 0x43, 0xE3, 0xA1, 0xBC, 0x43,
	0xE3, 0xA3, 0x87, 0x43, 0xE3, 0xA3, 0xA3, 0x43,
	0xE3, 0xA4, 0x9C, 0x43, 0xE3, 0xA4, 0xBA, 0x43,
	0xE3, 0xA8, 0xAE, 0x43, 0xE3, 0xA9, 0xAC, 0x43,
	0xE3, 0xAB, 0xA4, 0x43, 0xE3, 0xAC, 0x88, 0x43,
	// Bytes 600 - 63f
	0xE3, 0xAC, 0x99, 0x43, 0xE3, 0xAD, 0x89, 0x43,
	0xE3, 0xAE, 0x9D, 0x43, 0xE3, 0xB0, 0x98, 0x43,
	0xE3, 0xB1, 0x8E, 0x43, 0xE3, 0xB4, 0xB3, 0x43,
	0xE3, 0xB6, 0x96, 0x43, 0xE3, 0xBA, 0xAC, 0x43,
	0xE3, 0xBA, 0xB8, 0x43, 0xE3, 0xBC, 0x9B, 0x43,
	0xE3, 0xBF, 0xBC, 0x43, 0xE4, 0x80, 0x88, 0x43,
	0xE4, 0x80, 0x98, 0x43, 0xE4, 0x80, 0xB9, 0x43,
	0xE4, 0x81, 0x86, 0x43, 0xE4, 0x82, 0x96, 0x43,
	// Bytes 640 - 67f
	0xE4, 0x83, 0xA3, 0x43, 0xE4, 0x84, 0xAF, 0x43,
	0xE4, 0x88, 0x82, 0x43, 0xE4, 0x88, 0xA7, 0x43,
	0xE4, 0x8A, 0xA0, 0x43, 0xE4, 0x8C, 0x81, 0x43,
	0xE4, 0x8C, 0xB4, 0x43, 0xE4, 0x8D, 0x99, 0x43,
	0xE4, 0x8F, 0x95, 0x43, 0xE4, 0x8F, 0x99, 0x43,
	0xE4, 0x90, 0x8B, 0x43, 0xE4, 0x91, 0xAB, 0x43,
	0xE4, 0x94, 0xAB, 0x43, 0xE4, 0x95, 0x9D, 0x43,
	0xE4, 0x95, 0xA1, 0x43, 0xE4, 0x95, 0xAB, 0x43,
	// Bytes 680 - 6bf
	0xE4, 0x97, 0x97, 0x43, 0xE4, 0x97, 0xB9, 0x43,
	0xE4, 0x98, 0xB5, 0x43, 0xE4, 0x9A, 0xBE, 0x43,
	0xE4, 0x9B, 0x87, 0x43, 0xE4, 0xA6, 0x95, 0x43,
	0xE4, 0xA7, 0xA6, 0x43, 0xE4, 0xA9, 0xAE, 0x43,
	0xE4, 0xA9, 0xB6, 0x43, 0xE4, 0xAA, 0xB2, 0x43,
	0xE4, 0xAC, 0xB3, 0x43, 0xE4, 0xAF, 0x8E, 0x43,
	0xE4, 0xB3, 0x8E, 0x43, 0xE4, 0xB3, 0xAD, 0x43,
	0xE4, 0xB3, 0xB8, 0x43, 0xE4, 0xB5, 0x96, 0x43,
	// Bytes 6c0 - 6ff
	0xE4, 0xB8, 0x80, 0x43, 0xE4, 0xB8, 0x81, 0x43,
	0xE4, 0xB8, 0x83, 0x43, 0xE4, 0xB8, 0x89, 0x43,
	0xE4, 0xB8, 0x8A, 0x43, 0xE4, 0xB8, 0x8B, 0x43,
	0xE4, 0xB8, 0x8D, 0x43, 0xE4, 0xB8, 0x99, 0x43,
	0xE4, 0xB8, 0xA6, 0x43, 0xE4, 0xB8, 0xA8, 0x43,
	0xE4, 0xB8, 0xAD, 0x43, 0xE4, 0xB8, 0xB2, 0x43,
	0xE4, 0xB8, 0xB6, 0x43, 0xE4, 0xB8, 0xB8, 0x43,
	0xE4, 0xB8, 0xB9, 0x43, 0xE4, 0xB8, 0xBD, 0x43,
	// Bytes 700 - 73f
	0xE4, 0xB8, 0xBF, 0x43, 0xE4, 0xB9, 0x81, 0x43,
	0xE4, 0xB9, 0x99, 0x43, 0xE4, 0xB9, 0x9D, 0x43,
	0xE4, 0xBA, 0x82, 0x43, 0xE4, 0xBA, 0x85, 0x43,
	0xE4, 0xBA, 0x86, 0x43, 0xE4, 0xBA, 0x8C, 0x43,
	0xE4, 0xBA, 0x94, 0x43, 0xE4, 0xBA, 0xA0, 0x43,
	0xE4, 0xBA, 0xA4, 0x43, 0xE4, 0xBA, 0xAE, 0x43,
	0xE4, 0xBA, 0xBA, 0x43, 0xE4, 0xBB, 0x80, 0x43,
	0xE4, 0xBB, 0x8C, 0x43, 0xE4, 0xBB, 0xA4, 0x43,
	// Bytes 740 - 77f
	0xE4, 0xBC, 0x81, 0x43, 0xE4, 0xBC, 0x91, 0x43,
	0xE4, 0xBD, 0xA0, 0x43, 0xE4, 0xBE, 0x80, 0x43,
	0xE4, 0xBE, 0x86, 0x43, 0xE4, 0xBE, 0x8B, 0x43,
	0xE4, 0xBE, 0xAE, 0x43, 0xE4, 0xBE, 0xBB, 0x43,
	0xE4, 0xBE, 0xBF, 0x43, 0xE5, 0x80, 0x82, 0x43,
	0xE5, 0x80, 0xAB, 0x43, 0xE5, 0x81, 0xBA, 0x43,
	0xE5, 0x82, 0x99, 0x43, 0xE5, 0x83, 0x8F, 0x43,
	0xE5, 0x83, 0x9A, 0x43, 0xE5, 0x83, 0xA7, 0x43,
	// Bytes 780 - 7bf
	0xE5, 0x84, 0xAA, 0x43, 0xE5, 0x84, 0xBF, 0x43,
	0xE5, 0x85, 0x80, 0x43, 0xE5, 0x85, 0x85, 0x43,
	0xE5, 0x85, 0x8D, 0x43, 0xE5, 0x85, 0x94, 0x43,
	0xE5, 0x85, 0xA4, 0x43, 0xE5, 0x85, 0xA5, 0x43,
	0xE5, 0x85, 0xA7, 0x43, 0xE5, 0x85, 0xA8, 0x43,
	0xE5, 0x85, 0xA9, 0x43, 0xE5, 0x85, 0xAB, 0x43,
	0xE5, 0x85, 0xAD, 0x43, 0xE5, 0x85, 0xB7, 0x43,
	0xE5, 0x86, 0x80, 0x43, 0xE5, 0x86, 0x82, 0x43,
	// Bytes 7c0 - 7ff
	0xE5, 0x86, 0x8D, 0x43, 0xE5, 0x86, 0x92, 0x43,
	0xE5, 0x86, 0x95, 0x43, 0xE5, 0x86, 0x96, 0x43,
	0xE5, 0x86, 0x97, 0x43, 0xE5, 0x86, 0x99, 0x43,
	0xE5, 0x86, 0xA4, 0x43, 0xE5, 0x86, 0xAB, 0x43,
	0xE5, 0x86, 0xAC, 0x43, 0xE5, 0x86, 0xB5, 0x43,
	0xE5, 0x86, 0xB7, 0x43, 0xE5, 0x87, 0x89, 0x43,
	0xE5, 0x87, 0x8C, 0x43, 0xE5, 0x87, 0x9C, 0x43,
	0xE5, 0x87, 0x9E, 0x43, 0xE5, 0x87, 0xA0, 0x43,
	// Bytes 800 - 83f
	0xE5, 0x87, 0xB5, 0x43, 0xE5, 0x88, 0x80, 0x43,
	0xE5, 0x88, 0x83, 0x43, 0xE5, 0x88, 0x87, 0x43,
	0xE5, 0x88, 0x97, 0x43, 0xE5, 0x88, 0x9D, 0x43,
	0xE5, 0x88, 0xA9, 0x43, 0xE5, 0x88, 0xBA, 0x43,
	0xE5, 0x88, 0xBB, 0x43, 0xE5, 0x89, 0x86, 0x43,
	0xE5, 0x89, 0x8D, 0x43, 0xE5, 0x89, 0xB2, 0x43,
	0xE5, 0x89, 0xB7, 0x43, 0xE5, 0x8A, 0x89, 0x43,
	0xE5, 0x8A, 0x9B, 0x43, 0xE5, 0x8A, 0xA3, 0x43,
	// Bytes 840 - 87f
	0xE5, 0x8A, 0xB3, 0x43, 0xE5, 0x8A, 0xB4, 0x43,
	0xE5, 0x8B, 0x87, 0x43, 0xE5, 0x8B, 0x89, 0x43,
	0xE5, 0x8B, 0x92, 0x43, 0xE5, 0x8B, 0x9E, 0x43,
	0xE5, 0x8B, 0xA4, 0x43, 0xE5, 0x8B, 0xB5, 0x43,
	0xE5, 0x8B, 0xB9, 0x43, 0xE5, 0x8B, 0xBA, 0x43,
	0xE5, 0x8C, 0x85, 0x43, 0xE5, 0x8C, 0x86, 0x43,
	0xE5, 0x8C, 0x95, 0x43, 0xE5, 0x8C, 0x97, 0x43,
	0xE5, 0x8C, 0x9A, 0x43, 0xE5, 0x8C, 0xB8, 0x43,
	// Bytes 880 - 8bf
	0xE5, 0x8C, 0xBB, 0x43, 0xE5, 0x8C, 0xBF, 0x43,
	0xE5, 0x8D, 0x81, 0x43, 0xE5, 0x8D, 0x84, 0x43,
	0xE5, 0x8D, 0x85, 0x43, 0xE5, 0x8D, 0x89, 0x43,
	0xE5, 0x8D, 0x91, 0x43, 0xE5, 0x8D, 0x94, 0x43,
	0xE5, 0x8D, 0x9A, 0x43, 0xE5, 0x8D, 0x9C, 0x43,
	0xE5, 0x8D, 0xA9, 0x43, 0xE5, 0x8D, 0xB0, 0x43,
	0xE5, 0x8D, 0xB3, 0x43, 0xE5, 0x8D, 0xB5, 0x43,
	0xE5, 0x8D, 0xBD, 0x43, 0xE5, 0x8D, 0xBF, 0x43,
	// Bytes 8c0 - 8ff
	0xE5, 0x8E, 0x82, 0x43, 0xE5, 0x8E, 0xB6, 0x43,
	0xE5, 0x8F, 0x83, 0x43, 0xE5, 0x8F, 0x88, 0x43,
	0xE5, 0x8F, 0x8A, 0x43, 0xE5, 0x8F, 0x8C, 0x43,
	0xE5, 0x8F, 0x9F, 0x43, 0xE5, 0x8F, 0xA3, 0x43,
	0xE5, 0x8F, 0xA5, 0x43, 0xE5, 0x8F, 0xAB, 0x43,
	0xE5, 0x8F, 0xAF, 0x43, 0xE5, 0x8F, 0xB1, 0x43,
	0xE5, 0x8F, 0xB3, 0x43, 0xE5, 0x90, 0x86, 0x43,
	0xE5, 0x90, 0x88, 0x43, 0xE5, 0x90, 0x8D, 0x43,
	// Bytes 900 - 93f
	0xE5, 0x90, 0x8F, 0x43, 0xE5, 0x90, 0x9D, 0x43,
	0xE5, 0x90, 0xB8, 0x43, 0xE5, 0x90, 0xB9, 0x43,
	0xE5, 0x91, 0x82, 0x43, 0xE5, 0x91, 0x88, 0x43,
	0xE5, 0x91, 0xA8, 0x43, 0xE5, 0x92, 0x9E, 0x43,
	0xE5, 0x92, 0xA2, 0x43, 0xE5, 0x92, 0xBD, 0x43,
	0xE5, 0x93, 0xB6, 0x43, 0xE5, 0x94, 0x90, 0x43,
	0xE5, 0x95, 0x8F, 0x43, 0xE5, 0x95, 0x93, 0x43,
	0xE5, 0x95, 0x95, 0x43, 0xE5, 0x95, 0xA3, 0x43,
	// Bytes 940 - 97f
	0xE5, 0x96, 0x84, 0x43, 0xE5, 0x96, 0x87, 0x43,
	0xE5, 0x96, 0x99, 0x43, 0xE5, 0x96, 0x9D, 0x43,
	0xE5, 0x96, 0xAB, 0x43, 0xE5, 0x96, 0xB3, 0x43,
	0xE5, 0x96, 0xB6, 0x43, 0xE5, 0x97, 0x80, 0x43,
	0xE5, 0x97, 0x82, 0x43, 0xE5, 0x97, 0xA2, 0x43,
	0xE5, 0x98, 0x86, 0x43, 0xE5, 0x99, 0x91, 0x43,
	0xE5, 0x99, 0xA8, 0x43, 0xE5, 0x99, 0xB4, 0x43,
	0xE5, 0x9B, 0x97, 0x43, 0xE5, 0x9B, 0x9B, 0x43,
	// Bytes 980 - 9bf
	0xE5, 0x9B, 0xB9, 0x43, 0xE5, 0x9C, 0x96, 0x43,
	0xE5, 0x9C, 0x97, 0x43, 0xE5, 0x9C, 0x9F, 0x43,
	0xE5, 0x9C, 0xB0, 0x43, 0xE5, 0x9E, 0x8B, 0x43,
	0xE5, 0x9F, 0x8E, 0x43, 0xE5, 0x9F, 0xB4, 0x43,
	0xE5, 0xA0, 0x8D, 0x43, 0xE5, 0xA0, 0xB1, 0x43,
	0xE5, 0xA0, 0xB2, 0x43, 0xE5, 0xA1, 0x80, 0x43,
	0xE5, 0xA1, 0x9A, 0x43, 0xE5, 0xA1, 0x9E, 0x43,
	0xE5, 0xA2, 0xA8, 0x43, 0xE5, 0xA2, 0xAC, 0x43,
	// Bytes 9c0 - 9ff
	0xE5, 0xA2, 0xB3, 0x43, 0xE5, 0xA3, 0x98, 0x43,
	0xE5, 0xA3, 0x9F, 0x43, 0xE5, 0xA3, 0xAB, 0x43,
	0xE5, 0xA3, 0xAE, 0x43, 0xE5, 0xA3, 0xB0, 0x43,
	0xE5, 0xA3, 0xB2, 0x43, 0xE5, 0xA3, 0xB7, 0x43,
	0xE5, 0xA4, 0x82, 0x43, 0xE5, 0xA4, 0x86, 0x43,
	0xE5, 0xA4, 0x8A, 0x43, 0xE5, 0xA4, 0x95, 0x43,
	0xE5, 0xA4, 0x9A, 0x43, 0xE5, 0xA4, 0x9C, 0x43,
	0xE5, 0xA4, 0xA2, 0x43, 0xE5, 0xA4, 0xA7, 0x43,
	// Bytes a00 - a3f
	0xE5, 0xA4, 0xA9, 0x43, 0xE5, 0xA5, 0x84, 0x43,
	0xE5, 0xA5, 0x88, 0x43, 0xE5, 0xA5, 0x91, 0x43,
	0xE5, 0xA5, 0x94, 0x43, 0xE5, 0xA5, 0xA2, 0x43,
	0xE5, 0xA5, 0xB3, 0x43, 0xE5, 0xA7, 0x98, 0x43,
	0xE5, 0xA7, 0xAC, 0x43, 0xE5, 0xA8, 0x9B, 0x43,
	0xE5, 0xA8, 0xA7, 0x43, 0xE5, 0xA9, 0xA2, 0x43,
	0xE5, 0xA9, 0xA6, 0x43, 0xE5, 0xAA, 0xB5, 0x43,
	0xE5, 0xAC, 0x88, 0x43, 0xE5, 0xAC, 0xA8, 0x43,
	// Bytes a40 - a7f
	0xE5, 0xAC, 0xBE, 0x43, 0xE5, 0xAD, 0x90, 0x43,
	0xE5, 0xAD, 0x97, 0x43, 0xE5, 0xAD, 0xA6, 0x43,
	0xE5, 0xAE, 0x80, 0x43, 0xE5, 0xAE, 0x85, 0x43,
	0xE5, 0xAE, 0x97, 0x43, 0xE5, 0xAF, 0x83, 0x43,
	0xE5, 0xAF, 0x98, 0x43, 0xE5, 0xAF, 0xA7, 0x43,
	0xE5, 0xAF, 0xAE, 0x43, 0xE5, 0xAF, 0xB3, 0x43,
	0xE5, 0xAF, 0xB8, 0x43, 0xE5, 0xAF, 0xBF, 0x43,
	0xE5, 0xB0, 0x86, 0x43, 0xE5, 0xB0, 0x8F, 0x43,
	// Bytes a80 - abf
	0xE5, 0xB0, 0xA2, 0x43, 0xE5, 0xB0, 0xB8, 0x43,
	0xE5, 0xB0, 0xBF, 0x43, 0xE5, 0xB1, 0xA0, 0x43,
	0xE5, 0xB1, 0xA2, 0x43, 0xE5, 0xB1, 0xA4, 0x43,
	0xE5, 0xB1, 0xA5, 0x43, 0xE5, 0xB1, 0xAE, 0x43,
	0xE5, 0xB1, 0xB1, 0x43, 0xE5, 0xB2, 0x8D, 0x43,
	0xE5, 0xB3, 0x80, 0x43, 0xE5, 0xB4, 0x99, 0x43,
	0xE5, 0xB5, 0x83, 0x43, 0xE5, 0xB5, 0x90, 0x43,
	0xE5, 0xB5, 0xAB, 0x43, 0xE5, 0xB5, 0xAE, 0x43,
	// Bytes ac0 - aff
	0xE5, 0xB5, 0xBC, 0x43, 0xE5, 0xB6, 0xB2, 0x43,
	0xE5, 0xB6, 0xBA, 0x43, 0xE5, 0xB7, 0x9B, 0x43,
	0xE5, 0xB7, 0xA1, 0x43, 0xE5, 0xB7, 0xA2, 0x43,
	0xE5, 0xB7, 0xA5, 0x43, 0xE5, 0xB7, 0xA6, 0x43,
	0xE5, 0xB7, 0xB1, 0x43, 0xE5, 0xB7, 0xBD, 0x43,
	0xE5, 0xB7, 0xBE, 0x43, 0xE5, 0xB8, 0xA8, 0x43,
	0xE5, 0xB8, 0xBD, 0x43, 0xE5, 0xB9, 0xA9, 0x43,
	0xE5, 0xB9, 0xB2, 0x43, 0xE5, 0xB9, 0xB4, 0x43,
	// Bytes b00 - b3f
	0xE5, 0xB9, 0xBA, 0x43, 0xE5, 0xB9, 0xBC, 0x43,
	0xE5, 0xB9, 0xBF, 0x43, 0xE5, 0xBA, 0xA6, 0x43,
	0xE5, 0xBA, 0xB0, 0x43, 0xE5, 0xBA, 0xB3, 0x43,
	0xE5, 0xBA, 0xB6, 0x43, 0xE5, 0xBB, 0x89, 0x43,
	0xE5, 0xBB, 0x8A, 0x43, 0xE5, 0xBB, 0x92, 0x43,
	0xE5, 0xBB, 0x93, 0x43, 0xE5, 0xBB, 0x99, 0x43,
	0xE5, 0xBB, 0xAC, 0x43, 0xE5, 0xBB, 0xB4, 0x43,
	0xE5, 0xBB, 0xBE, 0x43, 0xE5, 0xBC, 0x84, 0x43,
	// Bytes b40 - b7f
	0xE5, 0xBC, 0x8B, 0x43, 0xE5, 0xBC, 0x93, 0x43,
	0xE5, 0xBC, 0xA2, 0x43, 0xE5, 0xBD, 0x90, 0x43,
	0xE5, 0xBD, 0x93, 0x43, 0xE5, 0xBD, 0xA1, 0x43,
	0xE5, 0xBD, 0xA2, 0x43, 0xE5, 0xBD, 0xA9, 0x43,
	0xE5, 0xBD, 0xAB, 0x43, 0xE5, 0xBD, 0xB3, 0x43,
	0xE5, 0xBE, 0x8B, 0x43, 0xE5, 0xBE, 0x8C, 0x43,
	0xE5, 0xBE, 0x97, 0x43, 0xE5, 0xBE, 0x9A, 0x43,
	0xE5, 0xBE, 0xA9, 0x43, 0xE5, 0xBE, 0xAD, 0x43,
	// Bytes b80 - bbf
	0xE5, 0xBF, 0x83, 0x43, 0xE5, 0xBF, 0x8D, 0x43,
	0xE5, 0xBF, 0x97, 0x43, 0xE5, 0xBF, 0xB5, 0x43,
	0xE5, 0xBF, 0xB9, 0x43, 0xE6, 0x80, 0x92, 0x43,
	0xE6, 0x80, 0x9C, 0x43, 0xE6, 0x81, 0xB5, 0x43,
	0xE6, 0x82, 0x81, 0x43, 0xE6, 0x82, 0x94, 0x43,
	0xE6, 0x83, 0x87, 0x43, 0xE6, 0x83, 0x98, 0x43,
	0xE6, 0x83, 0xA1, 0x43, 0xE6, 0x84, 0x88, 0x43,
	0xE6, 0x85, 0x84, 0x43, 0xE6, 0x85, 0x88, 0x43,
	// Bytes bc0 - bff
	0xE6, 0x85, 0x8C, 0x43, 0xE6, 0x85, 0x8E, 0x43,
	0xE6, 0x85, 0xA0, 0x43, 0xE6, 0x85, 0xA8, 0x43,
	0xE6, 0x85, 0xBA, 0x43, 0xE6, 0x86, 0x8E, 0x43,
	0xE6, 0x86, 0x90, 0x43, 0xE6, 0x86, 0xA4, 0x43,
	0xE6, 0x86, 0xAF, 0x43, 0xE6, 0x86, 0xB2, 0x43,
	0xE6, 0x87, 0x9E, 0x43, 0xE6, 0x87, 0xB2, 0x43,
	0xE6, 0x87, 0xB6, 0x43, 0xE6, 0x88, 0x80, 0x43,
	0xE6, 0x88, 0x88, 0x43, 0xE6, 0x88, 0x90, 0x43,
	// Bytes c00 - c3f
	0xE6, 0x88, 0x9B, 0x43, 0xE6, 0x88, 0xAE, 0x43,
	0xE6, 0x88, 0xB4, 0x43, 0xE6, 0x88, 0xB6, 0x43,
	0xE6, 0x89, 0x8B, 0x43, 0xE6, 0x89, 0x93, 0x43,
	0xE6, 0x89, 0x9D, 0x43, 0xE6, 0x8A, 0x95, 0x43,
	0xE6, 0x8A, 0xB1, 0x43, 0xE6, 0x8B, 0x89, 0x43,
	0xE6, 0x8B, 0x8F, 0x43, 0xE6, 0x8B, 0x93, 0x43,
	0xE6, 0x8B, 0x94, 0x43, 0xE6, 0x8B, 0xBC, 0x43,
	0xE6, 0x8B, 0xBE, 0x43, 0xE6, 0x8C, 0x87, 0x43,
	// Bytes c40 - c7f
	0xE6, 0x8C, 0xBD, 0x43, 0xE6, 0x8D, 0x90, 0x43,
	0xE6, 0x8D, 0x95, 0x43, 0xE6, 0x8D, 0xA8, 0x43,
	0xE6, 0x8D, 0xBB, 0x43, 0xE6, 0x8E, 0x83, 0x43,
	0xE6, 0x8E, 0xA0, 0x43, 0xE6, 0x8E, 0xA9, 0x43,
	0xE6, 0x8F, 0x84, 0x43, 0xE6, 0x8F, 0x85, 0x43,
	0xE6, 0x8F, 0xA4, 0x43, 0xE6, 0x90, 0x9C, 0x43,
	0xE6, 0x90, 0xA2, 0x43, 0xE6, 0x91, 0x92, 0x43,
	0xE6, 0x91, 0xA9, 0x43, 0xE6, 0x91, 0xB7, 0x43,
	// Bytes c80 - cbf
	0xE6, 0x91, 0xBE, 0x43, 0xE6, 0x92, 0x9A, 0x43,
	0xE6, 0x92, 0x9D, 0x43, 0xE6, 0x93, 0x84, 0x43,
	0xE6, 0x94, 0xAF, 0x43, 0xE6, 0x94, 0xB4, 0x43,
	0xE6, 0x95, 0x8F, 0x43, 0xE6, 0x95, 0x96, 0x43,
	0xE6, 0x95, 0xAC, 0x43, 0xE6, 0x95, 0xB8, 0x43,
	0xE6, 0x96, 0x87, 0x43, 0xE6, 0x96, 0x97, 0x43,
	0xE6, 0x96, 0x99, 0x43, 0xE6, 0x96, 0xA4, 0x43,
	0xE6, 0x96, 0xB0, 0x43, 0xE6, 0x96, 0xB9, 0x43,
	// Bytes cc0 - cff
	0xE6, 0x97, 0x85, 0x43, 0xE6, 0x97, 0xA0, 0x43,
	0xE6, 0x97, 0xA2, 0x43, 0xE6, 0x97, 0xA3, 0x43,
	0xE6, 0x97, 0xA5, 0x43, 0xE6, 0x98, 0x93, 0x43,
	0xE6, 0x98, 0xA0, 0x43, 0xE6, 0x99, 0x89, 0x43,
	0xE6, 0x99, 0xB4, 0x43, 0xE6, 0x9A, 0x88, 0x43,
	0xE6, 0x9A, 0x91, 0x43, 0xE6, 0x9A, 0x9C, 0x43,
	0xE6, 0x9A, 0xB4, 0x43, 0xE6, 0x9B, 0x86, 0x43,
	0xE6, 0x9B, 0xB0, 0x43, 0xE6, 0x9B, 0xB4, 0x43,
	// Bytes d00 - d3f
	0xE6, 0x9B, 0xB8, 0x43, 0xE6, 0x9C, 0x80, 0x43,
	0xE6, 0x9C, 0x88, 0x43, 0xE6, 0x9C, 0x89, 0x43,
	0xE6, 0x9C, 0x97, 0x43, 0xE6, 0x9C, 0x9B, 0x43,
	0xE6, 0x9C, 0xA1, 0x43, 0xE6, 0x9C, 0xA8, 0x43,
	0xE6, 0x9D, 0x8E, 0x43, 0xE6, 0x9D, 0x93, 0x43,
	0xE6, 0x9D, 0x96, 0x43, 0xE6, 0x9D, 0x9E, 0x43,
	0xE6, 0x9D, 0xBB, 0x43, 0xE6, 0x9E, 0x85, 0x43,
	0xE6, 0x9E, 0x97, 0x43, 0xE6, 0x9F, 0xB3, 0x43,
	// Bytes d40 - d7f
	0xE6, 0x9F, 0xBA, 0x43, 0xE6, 0xA0, 0x97, 0x43,
	0xE6, 0xA0, 0x9F, 0x43, 0xE6, 0xA0, 0xAA, 0x43,
	0xE6, 0xA1, 0x92, 0x43, 0xE6, 0xA2, 0x81, 0x43,
	0xE6, 0xA2, 0x85, 0x43, 0xE6, 0xA2, 0x8E, 0x43,
	0xE6, 0xA2, 0xA8, 0x43, 0xE6, 0xA4, 0x94, 0x43,
	0xE6, 0xA5, 0x82, 0x43, 0xE6, 0xA6, 0xA3, 0x43,
	0xE6, 0xA7, 0xAA, 0x43, 0xE6, 0xA8, 0x82, 0x43,
	0xE6, 0xA8, 0x93, 0x43, 0xE6, 0xAA, 0xA8, 0x43,
	// Bytes d80 - dbf
	0xE6, 0xAB, 0x93, 0x43, 0xE6, 0xAB, 0x9B, 0x43,
	0xE6, 0xAC, 0x84, 0x43, 0xE6, 0xAC, 0xA0, 0x43,
	0xE6, 0xAC, 0xA1, 0x43, 0xE6, 0xAD, 0x94, 0x43,
	0xE6, 0xAD, 0xA2, 0x43, 0xE6, 0xAD, 0xA3, 0x43,
	0xE6, 0xAD, 0xB2, 0x43, 0xE6, 0xAD, 0xB7, 0x43,
	0xE6, 0xAD, 0xB9, 0x43, 0xE6, 0xAE, 0x9F, 0x43,
	0xE6, 0xAE, 0xAE, 0x43, 0xE6, 0xAE, 0xB3, 0x43,
	0xE6, 0xAE, 0xBA, 0x43, 0xE6, 0xAE, 0xBB, 0x43,
	// Bytes dc0 - dff
	0xE6, 0xAF, 0x8B, 0x43, 0xE6, 0xAF, 0x8D, 0x43,
	0xE6, 0xAF, 0x94, 0x43, 0xE6, 0xAF, 0x9B, 0x43,
	0xE6, 0xB0, 0x8F, 0x43, 0xE6, 0xB0, 0x94, 0x43,
	0xE6, 0xB0, 0xB4, 0x43, 0xE6, 0xB1, 0x8E, 0x43,
	0xE6, 0xB1, 0xA7, 0x43, 0xE6, 0xB2, 0x88, 0x43,
	0xE6, 0xB2, 0xBF, 0x43, 0xE6, 0xB3, 0x8C, 0x43,
	0xE6, 0xB3, 0x8D, 0x43, 0xE6, 0xB3, 0xA5, 0x43,
	0xE6, 0xB3, 0xA8, 0x43, 0xE6, 0xB4, 0x96, 0x43,
	// Bytes e00 - e3f
	0xE6, 0xB4, 0x9B, 0x43, 0xE6, 0xB4, 0x9E, 0x43,
	0xE6, 0xB4, 0xB4, 0x43, 0xE6, 0xB4, 0xBE, 0x43,
	0xE6, 0xB5, 0x81, 0x43, 0xE6, 0xB5, 0xA9, 0x43,
	0xE6, 0xB5, 0xAA, 0x43, 0xE6, 0xB5, 0xB7, 0x43,
	0xE6, 0xB5, 0xB8, 0x43, 0xE6, 0xB6, 0x85, 0x43,
	0xE6, 0xB7, 0x8B, 0x43, 0xE6, 0xB7, 0x9A, 0x43,
	0xE6, 0xB7, 0xAA, 0x43, 0xE6, 0xB7, 0xB9, 0x43,
	0xE6, 0xB8, 0x9A, 0x43, 0xE6, 0xB8, 0xAF, 0x43,
	// Bytes e40 - e7f
	0xE6, 0xB9, 0xAE, 0x43, 0xE6, 0xBA, 0x80, 0x43,
	0xE6, 0xBA, 0x9C, 0x43, 0xE6, 0xBA, 0xBA, 0x43,
	0xE6, 0xBB, 0x87, 0x43, 0xE6, 0xBB, 0x8B, 0x43,
	0xE6, 0xBB, 0x91, 0x43, 0xE6, 0xBB, 0x9B, 0x43,
	0xE6, 0xBC, 0x8F, 0x43, 0xE6, 0xBC, 0x94, 0x43,
	0xE6, 0xBC, 0xA2, 0x43, 0xE6, 0xBC, 0xA3, 0x43,
	0xE6, 0xBD, 0xAE, 0x43, 0xE6, 0xBF, 0x86, 0x43,
	0xE6, 0xBF, 0xAB, 0x43, 0xE6, 0xBF, 0xBE, 0x43,
	// Bytes e80 - ebf
	0xE7, 0x80, 0x9B, 0x43, 0xE7, 0x80, 0x9E, 0x43,
	0xE7, 0x80, 0xB9, 0x43, 0xE7, 0x81, 0x8A, 0x43,
	0xE7, 0x81, 0xAB, 0x43, 0xE7, 0x81, 0xB0, 0x43,
	0xE7, 0x81, 0xB7, 0x43, 0xE7, 0x81, 0xBD, 0x43,
	0xE7, 0x82, 0x99, 0x43, 0xE7, 0x82, 0xAD, 0x43,
	0xE7, 0x83, 0x88, 0x43, 0xE7, 0x83, 0x99, 0x43,
	0xE7, 0x84, 0xA1, 0x43, 0xE7, 0x85, 0x85, 0x43,
	0xE7, 0x85, 0x89, 0x43, 0xE7, 0x85, 0xAE, 0x43,
	// Bytes ec0 - eff
	0xE7, 0x86, 0x9C, 0x43, 0xE7, 0x87, 0x8E, 0x43,
	0xE7, 0x87, 0x90, 0x43, 0xE7, 0x88, 0x90, 0x43,
	0xE7, 0x88, 0x9B, 0x43, 0xE7, 0x88, 0xA8, 0x43,
	0xE7, 0x88, 0xAA, 0x43, 0xE7, 0x88, 0xAB, 0x43,
	0xE7, 0x88, 0xB5, 0x43, 0xE7, 0x88, 0xB6, 0x43,
	0xE7, 0x88, 0xBB, 0x43, 0xE7, 0x88, 0xBF, 0x43,
	0xE7, 0x89, 0x87, 0x43, 0xE7, 0x89, 0x90, 0x43,
	0xE7, 0x89, 0x99, 0x43, 0xE7, 0x89, 0x9B, 0x43,
	// Bytes f00 - f3f
	0xE7, 0x89, 0xA2, 0x43, 0xE7, 0x89, 0xB9, 0x43,
	0xE7, 0x8A, 0x80, 0x43, 0xE7, 0x8A, 0x95, 0x43,
	0xE7, 0x8A, 0xAC, 0x43, 0xE7, 0x8A, 0xAF, 0x43,
	0xE7, 0x8B, 0x80, 0x43, 0xE7, 0x8B, 0xBC, 0x43,
	0xE7, 0x8C, 0xAA, 0x43, 0xE7, 0x8D, 0xB5, 0x43,
	0xE7, 0x8D, 0xBA, 0x43, 0xE7, 0x8E, 0x84, 0x43,
	0xE7, 0x8E, 0x87, 0x43, 0xE7, 0x8E, 0x89, 0x43,
	0xE7, 0x8E, 0x8B, 0x43, 0xE7, 0x8E, 0xA5, 0x43,
	// Bytes f40 - f7f
	0xE7, 0x8E, 0xB2, 0x43, 0xE7, 0x8F, 0x9E, 0x43,
	0xE7, 0x90, 0x86, 0x43, 0xE7, 0x90, 0x89, 0x43,
	0xE7, 0x90, 0xA2, 0x43, 0xE7, 0x91, 0x87, 0x43,
	0xE7, 0x91, 0x9C, 0x43, 0xE7, 0x91, 0xA9, 0x43,
	0xE7, 0x91, 0xB1, 0x43, 0xE7, 0x92, 0x85, 0x43,
	0xE7, 0x92, 0x89, 0x43, 0xE7, 0x92, 0x98, 0x43,
	0xE7, 0x93, 0x8A, 0x43, 0xE7, 0x93, 0x9C, 0x43,
	0xE7, 0x93, 0xA6, 0x43, 0xE7, 0x94, 0x86, 0x43,
	// Bytes f80 - fbf
	0xE7, 0x94, 0x98, 0x43, 0xE7, 0x94, 0x9F, 0x43,
	0xE7, 0x94, 0xA4, 0x43, 0xE7, 0x94, 0xA8, 0x43,
	0xE7, 0x94, 0xB0, 0x43, 0xE7, 0x94, 0xB2, 0x43,
	0xE7, 0x94, 0xB3, 0x43, 0xE7, 0x94, 0xB7, 0x43,
	0xE7, 0x94, 0xBB, 0x43, 0xE7, 0x94, 0xBE, 0x43,
	0xE7, 0x95, 0x99, 0x43, 0xE7, 0x95, 0xA5, 0x43,
	0xE7, 0x95, 0xB0, 0x43, 0xE7, 0x96, 0x8B, 0x43,
	0xE7, 0x96, 0x92, 0x43, 0xE7, 0x97, 0xA2, 0x43,
	// Bytes fc0 - fff
	0xE7, 0x98, 0x90, 0x43, 0xE7, 0x98, 0x9D, 0x43,
	0xE7, 0x98, 0x9F, 0x43, 0xE7, 0x99, 0x82, 0x43,
	0xE7, 0x99, 0xA9, 0x43, 0xE7, 0x99, 0xB6, 0x43,
	0xE7, 0x99, 0xBD, 0x43, 0xE7, 0x9A, 0xAE, 0x43,
	0xE7, 0x9A, 0xBF, 0x43, 0xE7, 0x9B, 0x8A, 0x43,
	0xE7, 0x9B, 0x9B, 0x43, 0xE7, 0x9B, 0xA3, 0x43,
	0xE7, 0x9B, 0xA7, 0x43, 0xE7, 0x9B, 0xAE, 0x43,
	0xE7, 0x9B, 0xB4, 0x43, 0xE7, 0x9C, 0x81, 0x43,
	// Bytes 1000 - 103f
	0xE7, 0x9C, 0x9E, 0x43, 0xE7, 0x9C, 0x9F, 0x43,
	0xE7, 0x9D, 0x80, 0x43, 0xE7, 0x9D, 0x8A, 0x43,
	0xE7, 0x9E, 0x8B, 0x43, 0xE7, 0x9E, 0xA7, 0x43,
	0xE7, 0x9F, 0x9B, 0x43, 0xE7, 0x9F, 0xA2, 0x43,
	0xE7, 0x9F, 0xB3, 0x43, 0xE7, 0xA1, 0x8E, 0x43,
	0xE7, 0xA1, 0xAB, 0x43, 0xE7, 0xA2, 0x8C, 0x43,
	0xE7, 0xA2, 0x91, 0x43, 0xE7, 0xA3, 0x8A, 0x43,
	0xE7, 0xA3, 0x8C, 0x43, 0xE7, 0xA3, 0xBB, 0x43,
	// Bytes 1040 - 107f
	0xE7, 0xA4, 0xAA, 0x43, 0xE7, 0xA4, 0xBA, 0x43,
	0xE7, 0xA4, 0xBC, 0x43, 0xE7, 0xA4, 0xBE, 0x43,
	0xE7, 0xA5, 0x88, 0x43, 0xE7, 0xA5, 0x89, 0x43,
	0xE7, 0xA5, 0x90, 0x43, 0xE7, 0xA5, 0x96, 0x43,
	0xE7, 0xA5, 0x9D, 0x43, 0xE7, 0xA5, 0x9E, 0x43,
	0xE7, 0xA5, 0xA5, 0x43, 0xE7, 0xA5, 0xBF, 0x43,
	0xE7, 0xA6, 0x81, 0x43, 0xE7, 0xA6, 0x8D, 0x43,
	0xE7, 0xA6, 0x8E, 0x43, 0xE7, 0xA6, 0x8F, 0x43,
	// Bytes 1080 - 10bf
	0xE7, 0xA6, 0xAE, 0x43, 0xE7, 0xA6, 0xB8, 0x43,
	0xE7, 0xA6, 0xBE, 0x43, 0xE7, 0xA7, 0x8A, 0x43,
	0xE7, 0xA7, 0x98, 0x43, 0xE7, 0xA7, 0xAB, 0x43,
	0xE7, 0xA8, 0x9C, 0x43, 0xE7, 0xA9, 0x80, 0x43,
	0xE7, 0xA9, 0x8A, 0x43, 0xE7, 0xA9, 0x8F, 0x43,
	0xE7, 0xA9, 0xB4, 0x43, 0xE7, 0xA9, 0xBA, 0x43,
	0xE7, 0xAA, 0x81, 0x43, 0xE7, 0xAA, 0xB1, 0x43,
	0xE7, 0xAB, 0x8B, 0x43, 0xE7, 0xAB, 0xAE, 0x43,
	// Bytes 10c0 - 10ff
	0xE7, 0xAB, 0xB9, 0x43, 0xE7, 0xAC, 0xA0, 0x43,
	0xE7, 0xAE, 0x8F, 0x43, 0xE7, 0xAF, 0x80, 0x43,
	0xE7, 0xAF, 0x86, 0x43, 0xE7, 0xAF, 0x89, 0x43,
	0xE7, 0xB0, 0xBE, 0x43, 0xE7, 0xB1, 0xA0, 0x43,
	0xE7, 0xB1, 0xB3, 0x43, 0xE7, 0xB1, 0xBB, 0x43,
	0xE7, 0xB2, 0x92, 0x43, 0xE7, 0xB2, 0xBE, 0x43,
	0xE7, 0xB3, 0x92, 0x43, 0xE7, 0xB3, 0x96, 0x43,
	0xE7, 0xB3, 0xA3, 0x43, 0xE7, 0xB3, 0xA7, 0x43,
	// Bytes 1100 - 113f
	0xE7, 0xB3, 0xA8, 0x43, 0xE7, 0xB3, 0xB8, 0x43,
	0xE7, 0xB4, 0x80, 0x43, 0xE7, 0xB4, 0x90, 0x43,
	0xE7, 0xB4, 0xA2, 0x43, 0xE7, 0xB4, 0xAF, 0x43,
	0xE7, 0xB5, 0x82, 0x43, 0xE7, 0xB5, 0x9B, 0x43,
	0xE7, 0xB5, 0xA3, 0x43, 0xE7, 0xB6, 0xA0, 0x43,
	0xE7, 0xB6, 0xBE, 0x43, 0xE7, 0xB7, 0x87, 0x43,
	0xE7, 0xB7, 0xB4, 0x43, 0xE7, 0xB8, 0x82, 0x43,
	0xE7, 0xB8, 0x89, 0x43, 0xE7, 0xB8, 0xB7, 0x43,
	// Bytes 1140 - 117f
	0xE7, 0xB9, 0x81, 0x43, 0xE7, 0xB9, 0x85, 0x43,
	0xE7, 0xBC, 0xB6, 0x43, 0xE7, 0xBC, 0xBE, 0x43,
	0xE7, 0xBD, 0x91, 0x43, 0xE7, 0xBD, 0xB2, 0x43,
	0xE7, 0xBD, 0xB9, 0x43, 0xE7, 0xBD, 0xBA, 0x43,
	0xE7, 0xBE, 0x85, 0x43, 0xE7, 0xBE, 0x8A, 0x43,
	0xE7, 0xBE, 0x95, 0x43, 0xE7, 0xBE, 0x9A, 0x43,
	0xE7, 0xBE, 0xBD, 0x43, 0xE7, 0xBF, 0xBA, 0x43,
	0xE8, 0x80, 0x81, 0x43, 0xE8, 0x80, 0x85, 0x43,
	// Bytes 1180 - 11bf
	0xE8, 0x80, 0x8C, 0x43, 0xE8, 0x80, 0x92, 0x43,
	0xE8, 0x80, 0xB3, 0x43, 0xE8, 0x81, 0x86, 0x43,
	0xE8, 0x81, 0xA0, 0x43, 0xE8, 0x81, 0xAF, 0x43,
	0xE8, 0x81, 0xB0, 0x43, 0xE8, 0x81, 0xBE, 0x43,
	0xE8, 0x81, 0xBF, 0x43, 0xE8, 0x82, 0x89, 0x43,
	0xE8, 0x82, 0x8B, 0x43, 0xE8, 0x82, 0xAD, 0x43,
	0xE8, 0x82, 0xB2, 0x43, 0xE8, 0x84, 0x83, 0x43,
	0xE8, 0x84, 0xBE, 0x43, 0xE8, 0x87, 0x98, 0x43,
	// Bytes 11c0 - 11ff
	0xE8, 0x87, 0xA3, 0x43, 0xE8, 0x87, 0xA8, 0x43,
	0xE8, 0x87, 0xAA, 0x43, 0xE8, 0x87, 0xAD, 0x43,
	0xE8, 0x87, 0xB3, 0x43, 0xE8, 0x87, 0xBC, 0x43,
	0xE8, 0x88, 0x81, 0x43, 0xE8, 0x88, 0x84, 0x43,
	0xE8, 0x88, 0x8C, 0x43, 0xE8, 0x88, 0x98, 0x43,
	0xE8, 0x88, 0x9B, 0x43, 0xE8, 0x88, 0x9F, 0x43,
	0xE8, 0x89, 0xAE, 0x43, 0xE8, 0x89, 0xAF, 0x43,
	0xE8, 0x89, 0xB2, 0x43, 0xE8, 0x89, 0xB8, 0x43,
	// Bytes 1200 - 123f
	0xE8, 0x89, 0xB9, 0x43, 0xE8, 0x8A, 0x8B, 0x43,
	0xE8, 0x8A, 0x91, 0x43, 0xE8, 0x8A, 0x9D, 0x43,
	0xE8, 0x8A, 0xB1, 0x43, 0xE8, 0x8A, 0xB3, 0x43,
	0xE8, 0x8A, 0xBD, 0x43, 0xE8, 0x8B, 0xA5, 0x43,
	0xE8, 0x8B, 0xA6, 0x43, 0xE8, 0x8C, 0x9D, 0x43,
	0xE8, 0x8C, 0xA3, 0x43, 0xE8, 0x8C, 0xB6, 0x43,
	0xE8, 0x8D, 0x92, 0x43, 0xE8, 0x8D, 0x93, 0x43,
	0xE8, 0x8D, 0xA3, 0x43, 0xE8, 0x8E, 0xAD, 0x43,
	// Bytes 1240 - 127f
	0xE8, 0x8E, 0xBD, 0x43, 0xE8, 0x8F, 0x89, 0x43,
	0xE8, 0x8F, 0x8A, 0x43, 0xE8, 0x8F, 0x8C, 0x43,
	0xE8, 0x8F, 0x9C, 0x43, 0xE8, 0x8F, 0xA7, 0x43,
	0xE8, 0x8F, 0xAF, 0x43, 0xE8, 0x8F, 0xB1, 0x43,
	0xE8, 0x90, 0xBD, 0x43, 0xE8, 0x91, 0x89, 0x43,
	0xE8, 0x91, 0x97, 0x43, 0xE8, 0x93, 0xAE, 0x43,
	0xE8, 0x93, 0xB1, 0x43, 0xE8, 0x93, 0xB3, 0x43,
	0xE8, 0x93, 0xBC, 0x43, 0xE8, 0x94, 0x96, 0x43,
	// Bytes 1280 - 12bf
	0xE8, 0x95, 0xA4, 0x43, 0xE8, 0x97, 0x8D, 0x43,
	0xE8, 0x97, 0xBA, 0x43, 0xE8, 0x98, 0x86, 0x43,
	0xE8, 0x98, 0x92, 0x43, 0xE8, 0x98, 0xAD, 0x43,
	0xE8, 0x98, 0xBF, 0x43, 0xE8, 0x99, 0x8D, 0x43,
	0xE8, 0x99, 0x90, 0x43, 0xE8, 0x99, 0x9C, 0x43,
	0xE8, 0x99, 0xA7, 0x43, 0xE8, 0x99, 0xA9, 0x43,
	0xE8, 0x99, 0xAB, 0x43, 0xE8, 0x9A, 0x88, 0x43,
	0xE8, 0x9A, 0xA9, 0x43, 0xE8, 0x9B, 0xA2, 0x43,
	// Bytes 12c0 - 12ff
	0xE8, 0x9C, 0x8E, 0x43, 0xE8, 0x9C, 0xA8, 0x43,
	0xE8, 0x9D, 0xAB, 0x43, 0xE8, 0x9D, 0xB9, 0x43,
	0xE8, 0x9E, 0x86, 0x43, 0xE8, 0x9E, 0xBA, 0x43,
	0xE8, 0x9F, 0xA1, 0x43, 0xE8, 0xA0, 0x81, 0x43,
	0xE8, 0xA0, 0x9F, 0x43, 0xE8, 0xA1, 0x80, 0x43,
	0xE8, 0xA1, 0x8C, 0x43, 0xE8, 0xA1, 0xA0, 0x43,
	0xE8, 0xA1, 0xA3, 0x43, 0xE8, 0xA3, 0x82, 0x43,
	0xE8, 0xA3, 0x8F, 0x43, 0xE8, 0xA3, 0x97, 0x43,
	// Bytes 1300 - 133f
	0xE8, 0xA3, 0x9E, 0x43, 0xE8, 0xA3, 0xA1, 0x43,
	0xE8, 0xA3, 0xB8, 0x43, 0xE8, 0xA3, 0xBA, 0x43,
	0xE8, 0xA4, 0x90, 0x43, 0xE8, 0xA5, 0x81, 0x43,
	0xE8, 0xA5, 0xA4, 0x43, 0xE8, 0xA5, 0xBE, 0x43,
	0xE8, 0xA6, 0x86, 0x43, 0xE8, 0xA6, 0x8B, 0x43,
	0xE8, 0xA6, 0x96, 0x43, 0xE8, 0xA7, 0x92, 0x43,
	0xE8, 0xA7, 0xA3, 0x43, 0xE8, 0xA8, 0x80, 0x43,
	0xE8, 0xAA, 0xA0, 0x43, 0xE8, 0xAA, 0xAA, 0x43,
	// Bytes 1340 - 137f
	0xE8, 0xAA, 0xBF, 0x43, 0xE8, 0xAB, 0x8B, 0x43,
	0xE8, 0xAB, 0x92, 0x43, 0xE8, 0xAB, 0x96, 0x43,
	0xE8, 0xAB, 0xAD, 0x43, 0xE8, 0xAB, 0xB8, 0x43,
	0xE8, 0xAB, 0xBE, 0x43, 0xE8, 0xAC, 0x81, 0x43,
	0xE8, 0xAC, 0xB9, 0x43, 0xE8, 0xAD, 0x98, 0x43,
	0xE8, 0xAE, 0x80, 0x43, 0xE8, 0xAE, 0x8A, 0x43,
	0xE8, 0xB0, 0xB7, 0x43, 0xE8, 0xB1, 0x86, 0x43,
	0xE8, 0xB1, 0x88, 0x43, 0xE8, 0xB1, 0x95, 0x43,
	// Bytes 1380 - 13bf
	0xE8, 0xB1, 0xB8, 0x43, 0xE8, 0xB2, 0x9D, 0x43,
	0xE8, 0xB2, 0xA1, 0x43, 0xE8, 0xB2, 0xA9, 0x43,
	0xE8, 0xB2, 0xAB, 0x43, 0xE8, 0xB3, 0x81, 0x43,
	0xE8, 0xB3, 0x82, 0x43, 0xE8, 0xB3, 0x87, 0x43,
	0xE8, 0xB3, 0x88, 0x43, 0xE8, 0xB3, 0x93, 0x43,
	0xE8, 0xB4, 0x88, 0x43, 0xE8, 0xB4, 0x9B, 0x43,
	0xE8, 0xB5, 0xA4, 0x43, 0xE8, 0xB5, 0xB0, 0x43,
	0xE8, 0xB5, 0xB7, 0x43, 0xE8, 0xB6, 0xB3, 0x43,
	// Bytes 13c0 - 13ff
	0xE8, 0xB6, 0xBC, 0x43, 0xE8, 0xB7, 0x8B, 0x43,
	0xE8, 0xB7, 0xAF, 0x43, 0xE8, 0xB7, 0xB0, 0x43,
	0xE8, 0xBA, 0xAB, 0x43, 0xE8, 0xBB, 0x8A, 0x43,
	0xE8, 0xBB, 0x94, 0x43, 0xE8, 0xBC, 0xA6, 0x43,
	0xE8, 0xBC, 0xAA, 0x43, 0xE8, 0xBC, 0xB8, 0x43,
	0xE8, 0xBC, 0xBB, 0x43, 0xE8, 0xBD, 0xA2, 0x43,
	0xE8, 0xBE, 0x9B, 0x43, 0xE8, 0xBE, 0x9E, 0x43,
	0xE8, 0xBE, 0xB0, 0x43, 0xE8, 0xBE, 0xB5, 0x43,
	// Bytes 1400 - 143f
	0xE8, 0xBE, 0xB6, 0x43, 0xE9, 0x80, 0xA3, 0x43,
	0xE9, 0x80, 0xB8, 0x43, 0xE9, 0x81, 0x8A, 0x43,
	0xE9, 0x81, 0xA9, 0x43, 0xE9, 0x81, 0xB2, 0x43,
	0xE9, 0x81, 0xBC, 0x43, 0xE9, 0x82, 0x8F, 0x43,
	0xE9, 0x82, 0x91, 0x43, 0xE9, 0x82, 0x94, 0x43,
	0xE9, 0x83, 0x8E, 0x43, 0xE9, 0x83, 0x9E, 0x43,
	0xE9, 0x83, 0xB1, 0x43, 0xE9, 0x83, 0xBD, 0x43,
	0xE9, 0x84, 0x91, 0x43, 0xE9, 0x84, 0x9B, 0x43,
	// Bytes 1440 - 147f
	0xE9, 0x85, 0x89, 0x43, 0xE9, 0x85, 0x8D, 0x43,
	0xE9, 0x85, 0xAA, 0x43, 0xE9, 0x86, 0x99, 0x43,
	0xE9, 0x86, 0xB4, 0x43, 0xE9, 0x87, 0x86, 0x43,
	0xE9, 0x87, 0x8C, 0x43, 0xE9, 0x87, 0x8F, 0x43,
	0xE9, 0x87, 0x91, 0x43, 0xE9, 0x88, 0xB4, 0x43,
	0xE9, 0x88, 0xB8, 0x43, 0xE9, 0x89, 0xB6, 0x43,
	0xE9, 0x89, 0xBC, 0x43, 0xE9, 0x8B, 0x97, 0x43,
	0xE9, 0x8B, 0x98, 0x43, 0xE9, 0x8C, 0x84, 0x43,
	// Bytes 1480 - 14bf
	0xE9, 0x8D, 0x8A, 0x43, 0xE9, 0x8F, 0xB9, 0x43,
	0xE9, 0x90, 0x95, 0x43, 0xE9, 0x95, 0xB7, 0x43,
	0xE9, 0x96, 0x80, 0x43, 0xE9, 0x96, 0x8B, 0x43,
	0xE9, 0x96, 0xAD, 0x43, 0xE9, 0x96, 0xB7, 0x43,
	0xE9, 0x98, 0x9C, 0x43, 0xE9, 0x98, 0xAE, 0x43,
	0xE9, 0x99, 0x8B, 0x43, 0xE9, 0x99, 0x8D, 0x43,
	0xE9, 0x99, 0xB5, 0x43, 0xE9, 0x99, 0xB8, 0x43,
	0xE9, 0x99, 0xBC, 0x43, 0xE9, 0x9A, 0x86, 0x43,
	// Bytes 14c0 - 14ff
	0xE9, 0x9A, 0xA3, 0x43, 0xE9, 0x9A, 0xB6, 0x43,
	0xE9, 0x9A, 0xB7, 0x43, 0xE9, 0x9A, 0xB8, 0x43,
	0xE9, 0x9A, 0xB9, 0x43, 0xE9, 0x9B, 0x83, 0x43,
	0xE9, 0x9B, 0xA2, 0x43, 0xE9, 0x9B, 0xA3, 0x43,
	0xE9, 0x9B, 0xA8, 0x43, 0xE9, 0x9B, 0xB6, 0x43,
	0xE9, 0x9B, 0xB7, 0x43, 0xE9, 0x9C, 0xA3, 0x43,
	0xE9, 0x9C, 0xB2, 0x43, 0xE9, 0x9D, 0x88, 0x43,
	0xE9, 0x9D, 0x91, 0x43, 0xE9, 0x9D, 0x96, 0x43,
	// Bytes 1500 - 153f
	0xE9, 0x9D, 0x9E, 0x43, 0xE9, 0x9D, 0xA2, 0x43,
	0xE9, 0x9D, 0xA9, 0x43, 0xE9, 0x9F, 0x8B, 0x43,
	0xE9, 0x9F, 0x9B, 0x43, 0xE9, 0x9F, 0xA0, 0x43,
	0xE9, 0x9F, 0xAD, 0x43, 0xE9, 0x9F, 0xB3, 0x43,
	0xE9, 0x9F, 0xBF, 0x43, 0xE9, 0xA0, 0x81, 0x43,
	0xE9, 0xA0, 0x85, 0x43, 0xE9, 0xA0, 0x8B, 0x43,
	0xE9, 0xA0, 0x98, 0x43, 0xE9, 0xA0, 0xA9, 0x43,
	0xE9, 0xA0, 0xBB, 0x43, 0xE9, 0xA1, 0x9E, 0x43,
	// Bytes 1540 - 157f
	0xE9, 0xA2, 0xA8, 0x43, 0xE9, 0xA3, 0x9B, 0x43,
	0xE9, 0xA3, 0x9F, 0x43, 0xE9, 0xA3, 0xA2, 0x43,
	0xE9, 0xA3, 0xAF, 0x43, 0xE9, 0xA3, 0xBC, 0x43,
	0xE9, 0xA4, 0xA8, 0x43, 0xE9, 0xA4, 0xA9, 0x43,
	0xE9, 0xA6, 0x96, 0x43, 0xE9, 0xA6, 0x99, 0x43,
	0xE9, 0xA6, 0xA7, 0x43, 0xE9, 0xA6, 0xAC, 0x43,
	0xE9, 0xA7, 0x82, 0x43, 0xE9, 0xA7, 0xB1, 0x43,
	0xE9, 0xA7, 0xBE, 0x43, 0xE9, 0xA9, 0xAA, 0x43,
	// Bytes 1580 - 15bf
	0xE9, 0xAA, 0xA8, 0x43, 0xE9, 0xAB, 0x98, 0x43,
	0xE9, 0xAB, 0x9F, 0x43, 0xE9, 0xAC, 0x92, 0x43,
	0xE9, 0xAC, 0xA5, 0x43, 0xE9, 0xAC, 0xAF, 0x43,
	0xE9, 0xAC, 0xB2, 0x43, 0xE9, 0xAC, 0xBC, 0x43,
	0xE9, 0xAD, 0x9A, 0x43, 0xE9, 0xAD, 0xAF, 0x43,
	0xE9, 0xB1, 0x80, 0x43, 0xE9, 0xB1, 0x97, 0x43,
	0xE9, 0xB3, 0xA5, 0x43, 0xE9, 0xB3, 0xBD, 0x43,
	0xE9, 0xB5, 0xA7, 0x43, 0xE9, 0xB6, 0xB4, 0x43,
	// Bytes 15c0 - 15ff
	0xE9, 0xB7, 0xBA, 0x43, 0xE9, 0xB8, 0x9E, 0x43,
	0xE9, 0xB9, 0xB5, 0x43, 0xE9, 0xB9, 0xBF, 0x43,
	0xE9, 0xBA, 0x97, 0x43, 0xE9, 0xBA, 0x9F, 0x43,
	0xE9, 0xBA, 0xA5, 0x43, 0xE9, 0xBA, 0xBB, 0x43,
	0xE9, 0xBB, 0x83, 0x43, 0xE9, 0xBB, 0x8D, 0x43,
	0xE9, 0xBB, 0x8E, 0x43, 0xE9, 0xBB, 0x91, 0x43,
	0xE9, 0xBB, 0xB9, 0x43, 0xE9, 0xBB, 0xBD, 0x43,
	0xE9, 0xBB, 0xBE, 0x43, 0xE9, 0xBC, 0x85, 0x43,
	// Bytes 1600 - 163f
	0xE9, 0xBC, 0x8E, 0x43, 0xE9, 0xBC, 0x8F, 0x43,
	0xE9, 0xBC, 0x93, 0x43, 0xE9, 0xBC, 0x96, 0x43,
	0xE9, 0xBC, 0xA0, 0x43, 0xE9, 0xBC, 0xBB, 0x43,
	0xE9, 0xBD, 0x83, 0x43, 0xE9, 0xBD, 0x8A, 0x43,
	0xE9, 0xBD, 0x92, 0x43, 0xE9, 0xBE, 0x8D, 0x43,
	0xE9, 0xBE, 0x8E, 0x43, 0xE9, 0xBE, 0x9C, 0x43,
	0xE9, 0xBE, 0x9F, 0x43, 0xE9, 0xBE, 0xA0, 0x43,
	0xEA, 0x9C, 0xA7, 0x43, 0xEA, 0x9D, 0xAF, 0x43,
	// Bytes 1640 - 167f
	0xEA, 0xAC, 0xB7, 0x43, 0xEA, 0xAD, 0x92, 0x44,
	0xF0, 0xA0, 0x84, 0xA2, 0x44, 0xF0, 0xA0, 0x94,
	0x9C, 0x44, 0xF0, 0xA0, 0x94, 0xA5, 0x44, 0xF0,
	0xA0, 0x95, 0x8B, 0x44, 0xF0, 0xA0, 0x98, 0xBA,
	0x44, 0xF0, 0xA0, 0xA0, 0x84, 0x44, 0xF0, 0xA0,
	0xA3, 0x9E, 0x44, 0xF0, 0xA0, 0xA8, 0xAC, 0x44,
	0xF0, 0xA0, 0xAD, 0xA3, 0x44, 0xF0, 0xA1, 0x93,
	0xA4, 0x44, 0xF0, 0xA1, 0x9A, 0xA8, 0x44, 0xF0,
	// Bytes 1680 - 16bf
	0xA1, 0x9B, 0xAA, 0x44, 0xF0, 0xA1, 0xA7, 0x88,
	0x44, 0xF0, 0xA1, 0xAC, 0x98, 0x44, 0xF0, 0xA1,
	0xB4, 0x8B, 0x44, 0xF0, 0xA1, 0xB7, 0xA4, 0x44,
	0xF0, 0xA1, 0xB7, 0xA6, 0x44, 0xF0, 0xA2, 0x86,
	0x83, 0x44, 0xF0, 0xA2, 0x86, 0x9F, 0x44, 0xF0,
	0xA2, 0x8C, 0xB1, 0x44, 0xF0, 0xA2, 0x9B, 0x94,
	0x44, 0xF0, 0xA2, 0xA1, 0x84, 0x44, 0xF0, 0xA2,
	0xA1, 0x8A, 0x44, 0xF0, 0xA2, 0xAC, 0x8C, 0x44,
	// Bytes 16c0 - 16ff
	0xF0, 0xA2, 0xAF, 0xB1, 0x44, 0xF0, 0xA3, 0x80,
	0x8A, 0x44, 0xF0, 0xA3, 0x8A, 0xB8, 0x44, 0xF0,
	0xA3, 0x8D, 0x9F, 0x44, 0xF0, 0xA3, 0x8E, 0x93,
	0x44, 0xF0, 0xA3, 0x8E, 0x9C, 0x44, 0xF0, 0xA3,
	0x8F, 0x83, 0x44, 0xF0, 0xA3, 0x8F, 0x95, 0x44,
	0xF0, 0xA3, 0x91, 0xAD, 0x44, 0xF0, 0xA3, 0x9A,
	0xA3, 0x44, 0xF0, 0xA3, 0xA2, 0xA7, 0x44, 0xF0,
	0xA3, 0xAA, 0x8D, 0x44, 0xF0, 0xA3, 0xAB, 0xBA,
	// Bytes 1700 - 173f
	0x44, 0xF0, 0xA3, 0xB2, 0xBC, 0x44, 0xF0, 0xA3,
	0xB4, 0x9E, 0x44, 0xF0, 0xA3, 0xBB, 0x91, 0x44,
	0xF0, 0xA3, 0xBD, 0x9E, 0x44, 0xF0, 0xA3, 0xBE,
	0x8E, 0x44, 0xF0, 0xA4, 0x89, 0xA3, 0x44, 0xF0,
	0xA4, 0x8B, 0xAE, 0x44, 0xF0, 0xA4, 0x8E, 0xAB,
	0x44, 0xF0, 0xA4, 0x98, 0x88, 0x44, 0xF0, 0xA4,
	0x9C, 0xB5, 0x44, 0xF0, 0xA4, 0xA0, 0x94, 0x44,
	0xF0, 0xA4, 0xB0, 0xB6, 0x44, 0xF0, 0xA4, 0xB2,
	// Bytes 1740 - 177f
	0x92, 0x44, 0xF0, 0xA4, 0xBE, 0xA1, 0x44, 0xF0,
	0xA4, 0xBE, 0xB8, 0x44, 0xF0, 0xA5, 0x81, 0x84,
	0x44, 0xF0, 0xA5, 0x83, 0xB2, 0x44, 0xF0, 0xA5,
	0x83, 0xB3, 0x44, 0xF0, 0xA5, 0x84, 0x99, 0x44,
	0xF0, 0xA5, 0x84, 0xB3, 0x44, 0xF0, 0xA5, 0x89,
	0x89, 0x44, 0xF0, 0xA5, 0x90, 0x9D, 0x44, 0xF0,
	0xA5, 0x98, 0xA6, 0x44, 0xF0, 0xA5, 0x9A, 0x9A,
	0x44, 0xF0, 0xA5, 0x9B, 0x85, 0x44, 0xF0, 0xA5,
	// Bytes 1780 - 17bf
	0xA5, 0xBC, 0x44, 0xF0, 0xA5, 0xAA, 0xA7, 0x44,
	0xF0, 0xA5, 0xAE, 0xAB, 0x44, 0xF0, 0xA5, 0xB2,
	0x80, 0x44, 0xF0, 0xA5, 0xB3, 0x90, 0x44, 0xF0,
	0xA5, 0xBE, 0x86, 0x44, 0xF0, 0xA6, 0x87, 0x9A,
	0x44, 0xF0, 0xA6, 0x88, 0xA8, 0x44, 0xF0, 0xA6,
	0x89, 0x87, 0x44, 0xF0, 0xA6, 0x8B, 0x99, 0x44,
	0xF0, 0xA6, 0x8C, 0xBE, 0x44, 0xF0, 0xA6, 0x93,
	0x9A, 0x44, 0xF0, 0xA6, 0x94, 0xA3, 0x44, 0xF0,
	// Bytes 17c0 - 17ff
	0xA6, 0x96, 0xA8, 0x44, 0xF0, 0xA6, 0x9E, 0xA7,
	0x44, 0xF0, 0xA6, 0x9E, 0xB5, 0x44, 0xF0, 0xA6,
	0xAC, 0xBC, 0x44, 0xF0, 0xA6, 0xB0, 0xB6, 0x44,
	0xF0, 0xA6, 0xB3, 0x95, 0x44, 0xF0, 0xA6, 0xB5,
	0xAB, 0x44, 0xF0, 0xA6, 0xBC, 0xAC, 0x44, 0xF0,
	0xA6, 0xBE, 0xB1, 0x44, 0xF0, 0xA7, 0x83, 0x92,
	0x44, 0xF0, 0xA7, 0x8F, 0x8A, 0x44, 0xF0, 0xA7,
	0x99, 0xA7, 0x44, 0xF0, 0xA7, 0xA2, 0xAE, 0x44,
	// Bytes 1800 - 183f
	0xF0, 0xA7, 0xA5, 0xA6, 0x44, 0xF0, 0xA7, 0xB2,
	0xA8, 0x44, 0xF0, 0xA7, 0xBB, 0x93, 0x44, 0xF0,
	0xA7, 0xBC, 0xAF, 0x44, 0xF0, 0xA8, 0x97, 0x92,
	0x44, 0xF0, 0xA8, 0x97, 0xAD, 0x44, 0xF0, 0xA8,
	0x9C, 0xAE, 0x44, 0xF0, 0xA8, 0xAF, 0xBA, 0x44,
	0xF0, 0xA8, 0xB5, 0xB7, 0x44, 0xF0, 0xA9, 0x85,
	0x85, 0x44, 0xF0, 0xA9, 0x87, 0x9F, 0x44, 0xF0,
	0xA9, 0x88, 0x9A, 0x44, 0xF0, 0xA9, 0x90, 0x8A,
	// Bytes 1840 - 187f
	0x44, 0xF0, 0xA9, 0x92, 0x96, 0x44, 0xF0, 0xA9,
	0x96, 0xB6, 0x44, 0xF0, 0xA9, 0xAC, 0xB0, 0x44,
	0xF0, 0xAA, 0x83, 0x8E, 0x44, 0xF0, 0xAA, 0x84,
	0x85, 0x44, 0xF0, 0xAA, 0x88, 0x8E, 0x44, 0xF0,
	0xAA, 0x8A, 0x91, 0x44, 0xF0, 0xAA, 0x8E, 0x92,
	0x44, 0xF0, 0xAA, 0x98, 0x80, 0x42, 0x21, 0x21,
	0x42, 0x21, 0x3F, 0x42, 0x2E, 0x2E, 0x42, 0x30,
	0x2C, 0x42, 0x30, 0x2E, 0x42, 0x31, 0x2C, 0x42,
	// Bytes 1880 - 18bf
	0x31, 0x2E, 0x42, 0x31, 0x30, 0x42, 0x31, 0x31,
	0x42, 0x31, 0x32, 0x42, 0x31, 0x33, 0x42, 0x31,
	0x34, 0x42, 0x31, 0x35, 0x42, 0x31, 0x36, 0x42,
	0x31, 0x37, 0x42, 0x31, 0x38, 0x42, 0x31, 0x39,
	0x42, 0x32, 0x2C, 0x42, 0x32, 0x2E, 0x42, 0x32,
	0x30, 0x42, 0x32, 0x31, 0x42, 0x32, 0x32, 0x42,
	0x32, 0x33, 0x42, 0x32, 0x34, 0x42, 0x32, 0x35,
	0x42, 0x32, 0x36, 0x42, 0x32, 0x37, 0x42, 0x32,
	// Bytes 18c0 - 18ff
	0x38, 0x42, 0x32, 0x39, 0x42, 0x33, 0x2C, 0x42,
	0x33, 0x2E, 0x42, 0x33, 0x30, 0x42, 0x33, 0x31,
	0x42, 0x33, 0x32, 0x42, 0x33, 0x33, 0x42, 0x33,
	0x34, 0x42, 0x33, 0x35, 0x42, 0x33, 0x36, 0x42,
	0x33, 0x37, 0x42, 0x33, 0x38, 0x42, 0x33, 0x39,
	0x42, 0x34, 0x2C, 0x42, 0x34, 0x2E, 0x42, 0x34,
	0x30, 0x42, 0x34, 0x31, 0x42, 0x34, 0x32, 0x42,
	0x34, 0x33, 0x42, 0x34, 0x34, 0x42, 0x34, 0x35,
	// Bytes 1900 - 193f
	0x42, 0x34, 0x36, 0x42, 0x34, 0x37, 0x42, 0x34,
	0x38, 0x42, 0x34, 0x39, 0x42, 0x35, 0x2C, 0x42,
	0x35, 0x2E, 0x42, 0x35, 0x30, 0x42, 0x36, 0x2C,
	0x42, 0x36, 0x2E, 0x42, 0x37, 0x2C, 0x42, 0x37,
	0x2E, 0x42, 0x38, 0x2C, 0x42, 0x38, 0x2E, 0x42,
	0x39, 0x2C, 0x42, 0x39, 0x2E, 0x42, 0x3D, 0x3D,
	0x42, 0x3F, 0x21, 0x42, 0x3F, 0x3F, 0x42, 0x41,
	0x55, 0x42, 0x42, 0x71, 0x42, 0x43, 0x44, 0x42,
	// Bytes 1940 - 197f
	0x44, 0x4A, 0x42, 0x44, 0x5A, 0x42, 0x44, 0x7A,
	0x42, 0x47, 0x42, 0x42, 0x47, 0x79, 0x42, 0x48,
	0x50, 0x42, 0x48, 0x56, 0x42, 0x48, 0x67, 0x42,
	0x48, 0x7A, 0x42, 0x49, 0x49, 0x42, 0x49, 0x4A,
	0x42, 0x49, 0x55, 0x42, 0x49, 0x56, 0x42, 0x49,
	0x58, 0x42, 0x4B, 0x42, 0x42, 0x4B, 0x4B, 0x42,
	0x4B, 0x4D, 0x42, 0x4C, 0x4A, 0x42, 0x4C, 0x6A,
	0x42, 0x4D, 0x42, 0x42, 0x4D, 0x43, 0x42, 0x4D,
	// Bytes 1980 - 19bf
	0x44, 0x42, 0x4D, 0x52, 0x42, 0x4D, 0x56, 0x42,
	0x4D, 0x57, 0x42, 0x4E, 0x4A, 0x42, 0x4E, 0x6A,
	0x42, 0x4E, 0x6F, 0x42, 0x50, 0x48, 0x42, 0x50,
	0x52, 0x42, 0x50, 0x61, 0x42, 0x52, 0x73, 0x42,
	0x53, 0x44, 0x42, 0x53, 0x4D, 0x42, 0x53, 0x53,
	0x42, 0x53, 0x76, 0x42, 0x54, 0x4D, 0x42, 0x56,
	0x49, 0x42, 0x57, 0x43, 0x42, 0x57, 0x5A, 0x42,
	0x57, 0x62, 0x42, 0x58, 0x49, 0x42, 0x63, 0x63,
	// Bytes 19c0 - 19ff
	0x42, 0x63, 0x64, 0x42, 0x63, 0x6D, 0x42, 0x64,
	0x42, 0x42, 0x64, 0x61, 0x42, 0x64, 0x6C, 0x42,
	0x64, 0x6D, 0x42, 0x64, 0x7A, 0x42, 0x65, 0x56,
	0x42, 0x66, 0x66, 0x42, 0x66, 0x69, 0x42, 0x66,
	0x6C, 0x42, 0x66, 0x6D, 0x42, 0x68, 0x61, 0x42,
	0x69, 0x69, 0x42, 0x69, 0x6A, 0x42, 0x69, 0x6E,
	0x42, 0x69, 0x76, 0x42, 0x69, 0x78, 0x42, 0x6B,
	0x41, 0x42, 0x6B, 0x56, 0x42, 0x6B, 0x57, 0x42,
	// Bytes 1a00 - 1a3f
	0x6B, 0x67, 0x42, 0x6B, 0x6C, 0x42, 0x6B, 0x6D,
	0x42, 0x6B, 0x74, 0x42, 0x6C, 0x6A, 0x42, 0x6C,
	0x6D, 0x42, 0x6C, 0x6E, 0x42, 0x6C, 0x78, 0x42,
	0x6D, 0x32, 0x42, 0x6D, 0x33, 0x42, 0x6D, 0x41,
	0x42, 0x6D, 0x56, 0x42, 0x6D, 0x57, 0x42, 0x6D,
	0x62, 0x42, 0x6D, 0x67, 0x42, 0x6D, 0x6C, 0x42,
	0x6D, 0x6D, 0x42, 0x6D, 0x73, 0x42, 0x6E, 0x41,
	0x42, 0x6E, 0x46, 0x42, 0x6E, 0x56, 0x42, 0x6E,
	// Bytes 1a40 - 1a7f
	0x57, 0x42, 0x6E, 0x6A, 0x42, 0x6E, 0x6D, 0x42,
	0x6E, 0x73, 0x42, 0x6F, 0x56, 0x42, 0x70, 0x41,
	0x42, 0x70, 0x46, 0x42, 0x70, 0x56, 0x42, 0x70,
	0x57, 0x42, 0x70, 0x63, 0x42, 0x70, 0x73, 0x42,
	0x73, 0x72, 0x42, 0x73, 0x74, 0x42, 0x76, 0x69,
	0x42, 0x78, 0x69, 0x43, 0x28, 0x31, 0x29, 0x43,
	0x28, 0x32, 0x29, 0x43, 0x28, 0x33, 0x29, 0x43,
	0x28, 0x34, 0x29, 0x43, 0x28, 0x35, 0x29, 0x43,
	// Bytes 1a80 - 1abf
	0x28, 0x36, 0x29, 0x43, 0x28, 0x37, 0x29, 0x43,
	0x28, 0x38, 0x29, 0x43, 0x28, 0x39, 0x29, 0x43,
	0x28, 0x41, 0x29, 0x43, 0x28, 0x42, 0x29, 0x43,
	0x28, 0x43, 0x29, 0x43, 0x28, 0x44, 0x29, 0x43,
	0x28, 0x45, 0x29, 0x43, 0x28, 0x46, 0x29, 0x43,
	0x28, 0x47, 0x29, 0x43, 0x28, 0x48, 0x29, 0x43,
	0x28, 0x49, 0x29, 0x43, 0x28, 0x4A, 0x29, 0x43,
	0x28, 0x4B, 0x29, 0x43, 0x28, 0x4C, 0x29, 0x43,
	// Bytes 1ac0 - 1aff
	0x28, 0x4D, 0x29, 0x43, 0x28, 0x4E, 0x29, 0x43,
	0x28, 0x4F, 0x29, 0x43, 0x28, 0x50, 0x29, 0x43,
	0x28, 0x51, 0x29, 0x43, 0x28, 0x52, 0x29, 0x43,
	0x28, 0x53, 0x29, 0x43, 0x28, 0x54, 0x29, 0x43,
	0x28, 0x55, 0x29, 0x43, 0x28, 0x56, 0x29, 0x43,
	0x28, 0x57, 0x29, 0x43, 0x28, 0x58, 0x29, 0x43,
	0x28, 0x59, 0x29, 0x43, 0x28, 0x5A, 0x29, 0x43,
	0x28, 0x61, 0x29, 0x43, 0x28, 0x62, 0x29, 0x43,
	// Bytes 1b00 - 1b3f
	0x28, 0x63, 0x29, 0x43, 0x28, 0x64, 0x29, 0x43,
	0x28, 0x65, 0x29, 0x43, 0x28, 0x66, 0x29, 0x43,
	0x28, 0x67, 0x29, 0x43, 0x28, 0x68, 0x29, 0x43,
	0x28, 0x69, 0x29, 0x43, 0x28, 0x6A, 0x29, 0x43,
	0x28, 0x6B, 0x29, 0x43, 0x28, 0x6C, 0x29, 0x43,
	0x28, 0x6D, 0x29, 0x43, 0x28, 0x6E, 0x29, 0x43,
	0x28, 0x6F, 0x29, 0x43, 0x28, 0x70, 0x29, 0x43,
	0x28, 0x71, 0x29, 0x43, 0x28, 0x72, 0x29, 0x43,
	// Bytes 1b40 - 1b7f
	0x28, 0x73, 0x29, 0x43, 0x28, 0x74, 0x29, 0x43,
	0x28, 0x75, 0x29, 0x43, 0x28, 0x76, 0x29, 0x43,
	0x28, 0x77, 0x29, 0x43, 0x28, 0x78, 0x29, 0x43,
	0x28, 0x79, 0x29, 0x43, 0x28, 0x7A, 0x29, 0x43,
	0x2E, 0x2E, 0x2E, 0x43, 0x31, 0x30, 0x2E, 0x43,
	0x31, 0x31, 0x2E, 0x43, 0x31, 0x32, 0x2E, 0x43,
	0x31, 0x33, 0x2E, 0x43, 0x31, 0x34, 0x2E, 0x43,
	0x31, 0x35, 0x2E, 0x43, 0x31, 0x36, 0x2E, 0x43,
	// Bytes 1b80 - 1bbf
	0x31, 0x37, 0x2E, 0x43, 0x31, 0x38, 0x2E, 0x43,
	0x31, 0x39, 0x2E, 0x43, 0x32, 0x30, 0x2E, 0x43,
	0x3A, 0x3A, 0x3D, 0x43, 0x3D, 0x3D, 0x3D, 0x43,
	0x43, 0x6F, 0x2E, 0x43, 0x46, 0x41, 0x58, 0x43,
	0x47, 0x48, 0x7A, 0x43, 0x47, 0x50, 0x61, 0x43,
	0x49, 0x49, 0x49, 0x43, 0x4C, 0x54, 0x44, 0x43,
	0x4C, 0xC2, 0xB7, 0x43, 0x4D, 0x48, 0x7A, 0x43,
	0x4D, 0x50, 0x61, 0x43, 0x4D, 0xCE, 0xA9, 0x43,
	// Bytes 1bc0 - 1bff
	0x50, 0x50, 0x4D, 0x43, 0x50, 0x50, 0x56, 0x43,
	0x50, 0x54, 0x45, 0x43, 0x54, 0x45, 0x4C, 0x43,
	0x54, 0x48, 0x7A, 0x43, 0x56, 0x49, 0x49, 0x43,
	0x58, 0x49, 0x49, 0x43, 0x61, 0x2F, 0x63, 0x43,
	0x61, 0x2F, 0x73, 0x43, 0x61, 0xCA, 0xBE, 0x43,
	0x62, 0x61, 0x72, 0x43, 0x63, 0x2F, 0x6F, 0x43,
	0x63, 0x2F, 0x75, 0x43, 0x63, 0x61, 0x6C, 0x43,
	0x63, 0x6D, 0x32, 0x43, 0x63, 0x6D, 0x33, 0x43,
	// Bytes 1c00 - 1c3f
	0x64, 0x6D, 0x32, 0x43, 0x64, 0x6D, 0x33, 0x43,
	0x65, 0x72, 0x67, 0x43, 0x66, 0x66, 0x69, 0x43,
	0x66, 0x66, 0x6C, 0x43, 0x67, 0x61, 0x6C, 0x43,
	0x68, 0x50, 0x61, 0x43, 0x69, 0x69, 0x69, 0x43,
	0x6B, 0x48, 0x7A, 0x43, 0x6B, 0x50, 0x61, 0x43,
	0x6B, 0x6D, 0x32, 0x43, 0x6B, 0x6D, 0x33, 0x43,
	0x6B, 0xCE, 0xA9, 0x43, 0x6C, 0x6F, 0x67, 0x43,
	0x6C, 0xC2, 0xB7, 0x43, 0x6D, 0x69, 0x6C, 0x43,
	// Bytes 1c40 - 1c7f
	0x6D, 0x6D, 0x32, 0x43, 0x6D, 0x6D, 0x33, 0x43,
	0x6D, 0x6F, 0x6C, 0x43, 0x72, 0x61, 0x64, 0x43,
	0x76, 0x69, 0x69, 0x43, 0x78, 0x69, 0x69, 0x43,
	0xC2, 0xB0, 0x43, 0x43, 0xC2, 0xB0, 0x46, 0x43,
	0xCA, 0xBC, 0x6E, 0x43, 0xCE, 0xBC, 0x41, 0x43,
	0xCE, 0xBC, 0x46, 0x43, 0xCE, 0xBC, 0x56, 0x43,
	0xCE, 0xBC, 0x57, 0x43, 0xCE, 0xBC, 0x67, 0x43,
	0xCE, 0xBC, 0x6C, 0x43, 0xCE, 0xBC, 0x6D, 0x43,
	// Bytes 1c80 - 1cbf
	0xCE, 0xBC, 0x73, 0x44, 0x28, 0x31, 0x30, 0x29,
	0x44, 0x28, 0x31, 0x31, 0x29, 0x44, 0x28, 0x31,
	0x32, 0x29, 0x44, 0x28, 0x31, 0x33, 0x29, 0x44,
	0x28, 0x31, 0x34, 0x29, 0x44, 0x28, 0x31, 0x35,
	0x29, 0x44, 0x28, 0x31, 0x36, 0x29, 0x44, 0x28,
	0x31, 0x37, 0x29, 0x44, 0x28, 0x31, 0x38, 0x29,
	0x44, 0x28, 0x31, 0x39, 0x29, 0x44, 0x28, 0x32,
	0x30, 0x29, 0x44, 0x30, 0xE7, 0x82, 0xB9, 0x44,
	// Bytes 1cc0 - 1cff
	0x31, 0xE2, 0x81, 0x84, 0x44, 0x31, 0xE6, 0x97,
	0xA5, 0x44, 0x31, 0xE6, 0x9C, 0x88, 0x44, 0x31,
	0xE7, 0x82, 0xB9, 0x44, 0x32, 0xE6, 0x97, 0xA5,
	0x44, 0x32, 0xE6, 0x9C, 0x88, 0x44, 0x32, 0xE7,
	0x82, 0xB9, 0x44, 0x33, 0xE6, 0x97, 0xA5, 0x44,
	0x33, 0xE6, 0x9C, 0x88, 0x44, 0x33, 0xE7, 0x82,
	0xB9, 0x44, 0x34, 0xE6, 0x97, 0xA5, 0x44, 0x34,
	0xE6, 0x9C, 0x88, 0x44, 0x34, 0xE7, 0x82, 0xB9,
	// Bytes 1d00 - 1d3f
	0x44, 0x35, 0xE6, 0x97, 0xA5, 0x44, 0x35, 0xE6,
	0x9C, 0x88, 0x44, 0x35, 0xE7, 0x82, 0xB9, 0x44,
	0x36, 0xE6, 0x97, 0xA5, 0x44, 0x36, 0xE6, 0x9C,
	0x88, 0x44, 0x36, 0xE7, 0x82, 0xB9, 0x44, 0x37,
	0xE6, 0x97, 0xA5, 0x44, 0x37, 0xE6, 0x9C, 0x88,
	0x44, 0x37, 0xE7, 0x82, 0xB9, 0x44, 0x38, 0xE6,
	0x97, 0xA5, 0x44, 0x38, 0xE6, 0x9C, 0x88, 0x44,
	0x38, 0xE7, 0x82, 0xB9, 0x44, 0x39, 0xE6, 0x97,
	// Bytes 1d40 - 1d7f
	0xA5, 0x44, 0x39, 0xE6, 0x9C, 0x88, 0x44, 0x39,
	0xE7, 0x82, 0xB9, 0x44, 0x56, 0x49, 0x49, 0x49,
	0x44, 0x61, 0x2E, 0x6D, 0x2E, 0x44, 0x6B, 0x63,
	0x61, 0x6C, 0x44, 0x70, 0x2E, 0x6D, 0x2E, 0x44,
	0x76, 0x69, 0x69, 0x69, 0x44, 0xD5, 0xA5, 0xD6,
	0x82, 0x44, 0xD5, 0xB4, 0xD5, 0xA5, 0x44, 0xD5,
	0xB4, 0xD5, 0xAB, 0x44, 0xD5, 0xB4, 0xD5, 0xAD,
	0x44, 0xD5, 0xB4, 0xD5, 0xB6, 0x44, 0xD5, 0xBE,
	// Bytes 1d80 - 1dbf
	0xD5, 0xB6, 0x44, 0xD7, 0x90, 0xD7, 0x9C, 0x44,
	0xD8, 0xA7, 0xD9, 0xB4, 0x44, 0xD8, 0xA8, 0xD8,
	0xAC, 0x44, 0xD8, 0xA8, 0xD8, 0xAD, 0x44, 0xD8,
	0xA8, 0xD8, 0xAE, 0x44, 0xD8, 0xA8, 0xD8, 0xB1,
	0x44, 0xD8, 0xA8, 0xD8, 0xB2, 0x44, 0xD8, 0xA8,
	0xD9, 0x85, 0x44, 0xD8, 0xA8, 0xD9, 0x86, 0x44,
	0xD8, 0xA8, 0xD9, 0x87, 0x44, 0xD8, 0xA8, 0xD9,
	0x89, 0x44, 0xD8, 0xA8, 0xD9, 0x8A, 0x44, 0xD8,
	// Bytes 1dc0 - 1dff
	0xAA, 0xD8, 0xAC, 0x44, 0xD8, 0xAA, 0xD8, 0xAD,
	0x44, 0xD8, 0xAA, 0xD8, 0xAE, 0x44, 0xD8, 0xAA,
	0xD8, 0xB1, 0x44, 0xD8, 0xAA, 0xD8, 0xB2, 0x44,
	0xD8, 0xAA, 0xD9, 0x85, 0x44, 0xD8, 0xAA, 0xD9,
	0x86, 0x44, 0xD8, 0xAA, 0xD9, 0x87, 0x44, 0xD8,
	0xAA, 0xD9, 0x89, 0x44, 0xD8, 0xAA, 0xD9, 0x8A,
	0x44, 0xD8, 0xAB, 0xD8, 0xAC, 0x44, 0xD8, 0xAB,
	0xD8, 0xB1, 0x44, 0xD8, 0xAB, 0xD8, 0xB2, 0x44,
	// Bytes 1e00 - 1e3f
	0xD8, 0xAB, 0xD9, 0x85, 0x44, 0xD8, 0xAB, 0xD9,
	0x86, 0x44, 0xD8, 0xAB, 0xD9, 0x87, 0x44, 0xD8,
	0xAB, 0xD9, 0x89, 0x44, 0xD8, 0xAB, 0xD9, 0x8A,
	0x44, 0xD8, 0xAC, 0xD8, 0xAD, 0x44, 0xD8, 0xAC,
	0xD9, 0x85, 0x44, 0xD8, 0xAC, 0xD9, 0x89, 0x44,
	0xD8, 0xAC, 0xD9, 0x8A, 0x44, 0xD8, 0xAD, 0xD8,
	0xAC, 0x44, 0xD8, 0xAD, 0xD9, 0x85, 0x44, 0xD8,
	0xAD, 0xD9, 0x89, 0x44, 0xD8, 0xAD, 0xD9, 0x8A,
	// Bytes 1e40 - 1e7f
	0x44, 0xD8, 0xAE, 0xD8, 0xAC, 0x44, 0xD8, 0xAE,
	0xD8, 0xAD, 0x44, 0xD8, 0xAE, 0xD9, 0x85, 0x44,
	0xD8, 0xAE, 0xD9, 0x89, 0x44, 0xD8, 0xAE, 0xD9,
	0x8A, 0x44, 0xD8, 0xB3, 0xD8, 0xAC, 0x44, 0xD8,
	0xB3, 0xD8, 0xAD, 0x44, 0xD8, 0xB3, 0xD8, 0xAE,
	0x44, 0xD8, 0xB3, 0xD8, 0xB1, 0x44, 0xD8, 0xB3,
	0xD9, 0x85, 0x44, 0xD8, 0xB3, 0xD9, 0x87, 0x44,
	0xD8, 0xB3, 0xD9, 0x89, 0x44, 0xD8, 0xB3, 0xD9,
	// Bytes 1e80 - 1ebf
	0x8A, 0x44, 0xD8, 0xB4, 0xD8, 0xAC, 0x44, 0xD8,
	0xB4, 0xD8, 0xAD, 0x44, 0xD8, 0xB4, 0xD8, 0xAE,
	0x44, 0xD8, 0xB4, 0xD8, 0xB1, 0x44, 0xD8, 0xB4,
	0xD9, 0x85, 0x44, 0xD8, 0xB4, 0xD9, 0x87, 0x44,
	0xD8, 0xB4, 0xD9, 0x89, 0x44, 0xD8, 0xB4, 0xD9,
	0x8A, 0x44, 0xD8, 0xB5, 0xD8, 0xAD, 0x44, 0xD8,
	0xB5, 0xD8, 0xAE, 0x44, 0xD8, 0xB5, 0xD8, 0xB1,
	0x44, 0xD8, 0xB5, 0xD9, 0x85, 0x44, 0xD8, 0xB5,
	// Bytes 1ec0 - 1eff
	0xD9, 0x89, 0x44, 0xD8, 0xB5, 0xD9, 0x8A, 0x44,
	0xD8, 0xB6, 0xD8, 0xAC, 0x44, 0xD8, 0xB6, 0xD8,
	0xAD, 0x44, 0xD8, 0xB6, 0xD8, 0xAE, 0x44, 0xD8,
	0xB6, 0xD8, 0xB1, 0x44, 0xD8, 0xB6, 0xD9, 0x85,
	0x44, 0xD8, 0xB6, 0xD9, 0x89, 0x44, 0xD8, 0xB6,
	0xD9, 0x8A, 0x44, 0xD8, 0xB7, 0xD8, 0xAD, 0x44,
	0xD8, 0xB7, 0xD9, 0x85, 0x44, 0xD8, 0xB7, 0xD9,
	0x89, 0x44, 0xD8, 0xB7, 0xD9, 0x8A, 0x44, 0xD8,
	// Bytes 1f00 - 1f3f
	0xB8, 0xD9, 0x85, 0x44, 0xD8, 0xB9, 0xD8, 0xAC,
	0x44, 0xD8, 0xB9, 0xD9, 0x85, 0x44, 0xD8, 0xB9,
	0xD9, 0x89, 0x44, 0xD8, 0xB9, 0xD9, 0x8A, 0x44,
	0xD8, 0xBA, 0xD8, 0xAC, 0x44, 0xD8, 0xBA, 0xD9,
	0x85, 0x44, 0xD8, 0xBA, 0xD9, 0x89, 0x44, 0xD8,
	0xBA, 0xD9, 0x8A, 0x44, 0xD9, 0x81, 0xD8, 0xAC,
	0x44, 0xD9, 0x81, 0xD8, 0xAD, 0x44, 0xD9, 0x81,
	0xD8, 0xAE, 0x44, 0xD9, 0x81, 0xD9, 0x85, 0x44,
	// Bytes 1f40 - 1f7f
	0xD9, 0x81, 0xD9, 0x89, 0x44, 0xD9, 0x81, 0xD9,
	0x8A, 0x44, 0xD9, 0x82, 0xD8, 0xAD, 0x44, 0xD9,
	0x82, 0xD9, 0x85, 0x44, 0xD9, 0x82, 0xD9, 0x89,
	0x44, 0xD9, 0x82, 0xD9, 0x8A, 0x44, 0xD9, 0x83,
	0xD8, 0xA7, 0x44, 0xD9, 0x83, 0xD8, 0xAC, 0x44,
	0xD9, 0x83, 0xD8, 0xAD, 0x44, 0xD9, 0x83, 0xD8,
	0xAE, 0x44, 0xD9, 0x83, 0xD9, 0x84, 0x44, 0xD9,
	0x83, 0xD9, 0x85, 0x44, 0xD9, 0x83, 0xD9, 0x89,
	// Bytes 1f80 - 1fbf
	0x44, 0xD9, 0x83, 0xD9, 0x8A, 0x44, 0xD9, 0x84,
	0xD8, 0xA7, 0x44, 0xD9, 0x84, 0xD8, 0xAC, 0x44,
	0xD9, 0x84, 0xD8, 0xAD, 0x44, 0xD9, 0x84, 0xD8,
	0xAE, 0x44, 0xD9, 0x84, 0xD9, 0x85, 0x44, 0xD9,
	0x84, 0xD9, 0x87, 0x44, 0xD9, 0x84, 0xD9, 0x89,
	0x44, 0xD9, 0x84, 0xD9, 0x8A, 0x44, 0xD9, 0x85,
	0xD8, 0xA7, 0x44, 0xD9, 0x85, 0xD8, 0xAC, 0x44,
	0xD9, 0x85, 0xD8, 0xAD, 0x44, 0xD9, 0x85, 0xD8,
	// Bytes 1fc0 - 1fff
	0xAE, 0x44, 0xD9, 0x85, 0xD9, 0x85, 0x44, 0xD9,
	0x85, 0xD9, 0x89, 0x44, 0xD9, 0x85, 0xD9, 0x8A,
	0x44, 0xD9, 0x86, 0xD8, 0xAC, 0x44, 0xD9, 0x86,
	0xD8, 0xAD, 0x44, 0xD9, 0x86, 0xD8, 0xAE, 0x44,
	0xD9, 0x86, 0xD8, 0xB1, 0x44, 0xD9, 0x86, 0xD8,
	0xB2, 0x44, 0xD9, 0x86, 0xD9, 0x85, 0x44, 0xD9,
	0x86, 0xD9, 0x86, 0x44, 0xD9, 0x86, 0xD9, 0x87,
	0x44, 0xD9, 0x86, 0xD9, 0x89, 0x44, 0xD9, 0x86,
	// Bytes 2000 - 203f
	0xD9, 0x8A, 0x44, 0xD9, 0x87, 0xD8, 0xAC, 0x44,
	0xD9, 0x87, 0xD9, 0x85, 0x44, 0xD9, 0x87, 0xD9,
	0x89, 0x44, 0xD9, 0x87, 0xD9, 0x8A, 0x44, 0xD9,
	0x88, 0xD9, 0xB4, 0x44, 0xD9, 0x8A, 0xD8, 0xAC,
	0x44, 0xD9, 0x8A, 0xD8, 0xAD, 0x44, 0xD9, 0x8A,
	0xD8, 0xAE, 0x44, 0xD9, 0x8A, 0xD8, 0xB1, 0x44,
	0xD9, 0x8A, 0xD8, 0xB2, 0x44, 0xD9, 0x8A, 0xD9,
	0x85, 0x44, 0xD9, 0x8A, 0xD9, 0x86, 0x44, 0xD9,
	// Bytes 2040 - 207f
	0x8A, 0xD9, 0x87, 0x44, 0xD9, 0x8A, 0xD9, 0x89,
	0x44, 0xD9, 0x8A, 0xD9, 0x8A, 0x44, 0xD9, 0x8A,
	0xD9, 0xB4, 0x44, 0xDB, 0x87, 0xD9, 0xB4, 0x45,
	0x28, 0xE1, 0x84, 0x80, 0x29, 0x45, 0x28, 0xE1,
	0x84, 0x82, 0x29, 0x45, 0x28, 0xE1, 0x84, 0x83,
	0x29, 0x45, 0x28, 0xE1, 0x84, 0x85, 0x29, 0x45,
	0x28, 0xE1, 0x84, 0x86, 0x29, 0x45, 0x28, 0xE1,
	0x84, 0x87, 0x29, 0x45, 0x28, 0xE1, 0x84, 0x89,
	// Bytes 2080 - 20bf
	0x29, 0x45, 0x28, 0xE1, 0x84, 0x8B, 0x29, 0x45,
	0x28, 0xE1, 0x84, 0x8C, 0x29, 0x45, 0x28, 0xE1,
	0x84, 0x8E, 0x29, 0x45, 0x28, 0xE1, 0x84, 0x8F,
	0x29, 0x45, 0x28, 0xE1, 0x84, 0x90, 0x29, 0x45,
	0x28, 0xE1, 0x84, 0x91, 0x29, 0x45, 0x28, 0xE1,
	0x84, 0x92, 0x29, 0x45, 0x28, 0xE4, 0xB8, 0x80,
	0x29, 0x45, 0x28, 0xE4, 0xB8, 0x83, 0x29, 0x45,
	0x28, 0xE4, 0xB8, 0x89, 0x29, 0x45, 0x28, 0xE4,
	// Bytes 20c0 - 20ff
	0xB9, 0x9D, 0x29, 0x45, 0x28, 0xE4, 0xBA, 0x8C,
	0x29, 0x45, 0x28, 0xE4, 0xBA, 0x94, 0x29, 0x45,
	0x28, 0xE4, 0xBB, 0xA3, 0x29, 0x45, 0x28, 0xE4,
	0xBC, 0x81, 0x29, 0x45, 0x28, 0xE4, 0xBC, 0x91,
	0x29, 0x45, 0x28, 0xE5, 0x85, 0xAB, 0x29, 0x45,
	0x28, 0xE5, 0x85, 0xAD, 0x29, 0x45, 0x28, 0xE5,
	0x8A, 0xB4, 0x29, 0x45, 0x28, 0xE5, 0x8D, 0x81,
	0x29, 0x45, 0x28, 0xE5, 0x8D, 0x94, 0x29, 0x45,
	// Bytes 2100 - 213f
	0x28, 0xE5, 0x90, 0x8D, 0x29, 0x45, 0x28, 0xE5,
	0x91, 0xBC, 0x29, 0x45, 0x28, 0xE5, 0x9B, 0x9B,
	0x29, 0x45, 0x28, 0xE5, 0x9C, 0x9F, 0x29, 0x45,
	0x28, 0xE5, 0xAD, 0xA6, 0x29, 0x45, 0x28, 0xE6,
	0x97, 0xA5, 0x29, 0x45, 0x28, 0xE6, 0x9C, 0x88,
	0x29, 0x45, 0x28, 0xE6, 0x9C, 0x89, 0x29, 0x45,
	0x28, 0xE6, 0x9C, 0xA8, 0x29, 0x45, 0x28, 0xE6,
	0xA0, 0xAA, 0x29, 0x45, 0x28, 0xE6, 0xB0, 0xB4,
	// Bytes 2140 - 217f
	0x29, 0x45, 0x28, 0xE7, 0x81, 0xAB, 0x29, 0x45,
	0x28, 0xE7, 0x89, 0xB9, 0x29, 0x45, 0x28, 0xE7,
	0x9B, 0xA3, 0x29, 0x45, 0x28, 0xE7, 0xA4, 0xBE,
	0x29, 0x45, 0x28, 0xE7, 0xA5, 0x9D, 0x29, 0x45,
	0x28, 0xE7, 0xA5, 0xAD, 0x29, 0x45, 0x28, 0xE8,
	0x87, 0xAA, 0x29, 0x45, 0x28, 0xE8, 0x87, 0xB3,
	0x29, 0x45, 0x28, 0xE8, 0xB2, 0xA1, 0x29, 0x45,
	0x28, 0xE8, 0xB3, 0x87, 0x29, 0x45, 0x28, 0xE9,
	// Bytes 2180 - 21bf
	0x87, 0x91, 0x29, 0x45, 0x30, 0xE2, 0x81, 0x84,
	0x33, 0x45, 0x31, 0x30, 0xE6, 0x97, 0xA5, 0x45,
	0x31, 0x30, 0xE6, 0x9C, 0x88, 0x45, 0x31, 0x30,
	0xE7, 0x82, 0xB9, 0x45, 0x31, 0x31, 0xE6, 0x97,
	0xA5, 0x45, 0x31, 0x31, 0xE6, 0x9C, 0x88, 0x45,
	0x31, 0x31, 0xE7, 0x82, 0xB9, 0x45, 0x31, 0x32,
	0xE6, 0x97, 0xA5, 0x45, 0x31, 0x32, 0xE6, 0x9C,
	0x88, 0x45, 0x31, 0x32, 0xE7, 0x82, 0xB9, 0x45,
	// Bytes 21c0 - 21ff
	0x31, 0x33, 0xE6, 0x97, 0xA5, 0x45, 0x31, 0x33,
	0xE7, 0x82, 0xB9, 0x45, 0x31, 0x34, 0xE6, 0x97,
	0xA5, 0x45, 0x31, 0x34, 0xE7, 0x82, 0xB9, 0x45,
	0x31, 0x35, 0xE6, 0x97, 0xA5, 0x45, 0x31, 0x35,
	0xE7, 0x82, 0xB9, 0x45, 0x31, 0x36, 0xE6, 0x97,
	0xA5, 0x45, 0x31, 0x36, 0xE7, 0x82, 0xB9, 0x45,
	0x31, 0x37, 0xE6, 0x97, 0xA5, 0x45, 0x31, 0x37,
	0xE7, 0x82, 0xB9, 0x45, 0x31, 0x38, 0xE6, 0x97,
	// Bytes 2200 - 223f
	0xA5, 0x45, 0x31, 0x38, 0xE7, 0x82, 0xB9, 0x45,
	0x31, 0x39, 0xE6, 0x97, 0xA5, 0x45, 0x31, 0x39,
	0xE7, 0x82, 0xB9, 0x45, 0x31, 0xE2, 0x81, 0x84,
	0x32, 0x45, 0x31, 0xE2, 0x81, 0x84, 0x33, 0x45,
	0x31, 0xE2, 0x81, 0x84, 0x34, 0x45, 0x31, 0xE2,
	0x81, 0x84, 0x35, 0x45, 0x31, 0xE2, 0x81, 0x84,
	0x36, 0x45, 0x31, 0xE2, 0x81, 0x84, 0x37, 0x45,
	0x31, 0xE2, 0x81, 0x84, 0x38, 0x45, 0x31, 0xE2,
	// Bytes 2240 - 227f
	0x81, 0x84, 0x39, 0x45, 0x32, 0x30, 0xE6, 0x97,
	0xA5, 0x45, 0x32, 0x30, 0xE7, 0x82, 0xB9, 0x45,
	0x32, 0x31, 0xE6, 0x97, 0xA5, 0x45, 0x32, 0x31,
	0xE7, 0x82, 0xB9, 0x45, 0x32, 0x32, 0xE6, 0x97,
	0xA5, 0x45, 0x32, 0x32, 0xE7, 0x82, 0xB9, 0x45,
	0x32, 0x33, 0xE6, 0x97, 0xA5, 0x45, 0x32, 0x33,
	0xE7, 0x82, 0xB9, 0x45, 0x32, 0x34, 0xE6, 0x97,
	0xA5, 0x45, 0x32, 0x34, 0xE7, 0x82, 0xB9, 0x45,
	// Bytes 2280 - 22bf
	0x32, 0x35, 0xE6, 0x97, 0xA5, 0x45, 0x32, 0x36,
	0xE6, 0x97, 0xA5, 0x45, 0x32, 0x37, 0xE6, 0x97,
	0xA5, 0x45, 0x32, 0x38, 0xE6, 0x97, 0xA5, 0x45,
	0x32, 0x39, 0xE6, 0x97, 0xA5, 0x45, 0x32, 0xE2,
	0x81, 0x84, 0x33, 0x45, 0x32, 0xE2, 0x81, 0x84,
	0x35, 0x45, 0x33, 0x30, 0xE6, 0x97, 0xA5, 0x45,
	0x33, 0x31, 0xE6, 0x97, 0xA5, 0x45, 0x33, 0xE2,
	0x81, 0x84, 0x34, 0x45, 0x33, 0xE2, 0x81, 0x84,
	// Bytes 22c0 - 22ff
	0x35, 0x45, 0x33, 0xE2, 0x81, 0x84, 0x38, 0x45,
	0x34, 0xE2, 0x81, 0x84, 0x35, 0x45, 0x35, 0xE2,
	0x81, 0x84, 0x36, 0x45, 0x35, 0xE2, 0x81, 0x84,
	0x38, 0x45, 0x37, 0xE2, 0x81, 0x84, 0x38, 0x45,
	0x41, 0xE2, 0x88, 0x95, 0x6D, 0x45, 0x56, 0xE2,
	0x88, 0x95, 0x6D, 0x45, 0x6D, 0xE2, 0x88, 0x95,
	0x73, 0x46, 0x31, 0xE2, 0x81, 0x84, 0x31, 0x30,
	0x46, 0x43, 0xE2, 0x88, 0x95, 0x6B, 0x67, 0x46,
	// Bytes 2300 - 233f
	0x6D, 0xE2, 0x88, 0x95, 0x73, 0x32, 0x46, 0xD8,
	0xA8, 0xD8, 0xAD, 0xD9, 0x8A, 0x46, 0xD8, 0xA8,
	0xD8, 0xAE, 0xD9, 0x8A, 0x46, 0xD8, 0xAA, 0xD8,
	0xAC, 0xD9, 0x85, 0x46, 0xD8, 0xAA, 0xD8, 0xAC,
	0xD9, 0x89, 0x46, 0xD8, 0xAA, 0xD8, 0xAC, 0xD9,
	0x8A, 0x46, 0xD8, 0xAA, 0xD8, 0xAD, 0xD8, 0xAC,
	0x46, 0xD8, 0xAA, 0xD8, 0xAD, 0xD9, 0x85, 0x46,
	0xD8, 0xAA, 0xD8, 0xAE, 0xD9, 0x85, 0x46, 0xD8,
	// Bytes 2340 - 237f
	0xAA, 0xD8, 0xAE, 0xD9, 0x89, 0x46, 0xD8, 0xAA,
	0xD8, 0xAE, 0xD9, 0x8A, 0x46, 0xD8, 0xAA, 0xD9,
	0x85, 0xD8, 0xAC, 0x46, 0xD8, 0xAA, 0xD9, 0x85,
	0xD8, 0xAD, 0x46, 0xD8, 0xAA, 0xD9, 0x85, 0xD8,
	0xAE, 0x46, 0xD8, 0xAA, 0xD9, 0x85, 0xD9, 0x89,
	0x46, 0xD8, 0xAA, 0xD9, 0x85, 0xD9, 0x8A, 0x46,
	0xD8, 0xAC, 0xD8, 0xAD, 0xD9, 0x89, 0x46, 0xD8,
	0xAC, 0xD8, 0xAD, 0xD9, 0x8A, 0x46, 0xD8, 0xAC,
	// Bytes 2380 - 23bf
	0xD9, 0x85, 0xD8, 0xAD, 0x46, 0xD8, 0xAC, 0xD9,
	0x85, 0xD9, 0x89, 0x46, 0xD8, 0xAC, 0xD9, 0x85,
	0xD9, 0x8A, 0x46, 0xD8, 0xAD, 0xD8, 0xAC, 0xD9,
	0x8A, 0x46, 0xD8, 0xAD, 0xD9, 0x85, 0xD9, 0x89,
	0x46, 0xD8, 0xAD, 0xD9, 0x85, 0xD9, 0x8A, 0x46,
	0xD8, 0xB3, 0xD8, 0xAC, 0xD8, 0xAD, 0x46, 0xD8,
	0xB3, 0xD8, 0xAC, 0xD9, 0x89, 0x46, 0xD8, 0xB3,
	0xD8, 0xAD, 0xD8, 0xAC, 0x46, 0xD8, 0xB3, 0xD8,
	// Bytes 23c0 - 23ff
	0xAE, 0xD9, 0x89, 0x46, 0xD8, 0xB3, 0xD8, 0xAE,
	0xD9, 0x8A, 0x46, 0xD8, 0xB3, 0xD9, 0x85, 0xD8,
	0xAC, 0x46, 0xD8, 0xB3, 0xD9, 0x85, 0xD8, 0xAD,
	0x46, 0xD8, 0xB3, 0xD9, 0x85, 0xD9, 0x85, 0x46,
	0xD8, 0xB4, 0xD8, 0xAC, 0xD9, 0x8A, 0x46, 0xD8,
	0xB4, 0xD8, 0xAD, 0xD9, 0x85, 0x46, 0xD8, 0xB4,
	0xD8, 0xAD, 0xD9, 0x8A, 0x46, 0xD8, 0xB4, 0xD9,
	0x85, 0xD8, 0xAE, 0x46, 0xD8, 0xB4, 0xD9, 0x85,
	// Bytes 2400 - 243f
	0xD9, 0x85, 0x46, 0xD8, 0xB5, 0xD8, 0xAD, 0xD8,
	0xAD, 0x46, 0xD8, 0xB5, 0xD8, 0xAD, 0xD9, 0x8A,
	0x46, 0xD8, 0xB5, 0xD9, 0x84, 0xD9, 0x89, 0x46,
	0xD8, 0xB5, 0xD9, 0x84, 0xDB, 0x92, 0x46, 0xD8,
	0xB5, 0xD9, 0x85, 0xD9, 0x85, 0x46, 0xD8, 0xB6,
	0xD8, 0xAD, 0xD9, 0x89, 0x46, 0xD8, 0xB6, 0xD8,
	0xAD, 0xD9, 0x8A, 0x46, 0xD8, 0xB6, 0xD8, 0xAE,
	0xD9, 0x85, 0x46, 0xD8, 0xB7, 0xD9, 0x85, 0xD8,
	// Bytes 2440 - 247f
	0xAD, 0x46, 0xD8, 0xB7, 0xD9, 0x85, 0xD9, 0x85,
	0x46, 0xD8, 0xB7, 0xD9, 0x85, 0xD9, 0x8A, 0x46,
	0xD8, 0xB9, 0xD8, 0xAC, 0xD9, 0x85, 0x46, 0xD8,
	0xB9, 0xD9, 0x85, 0xD9, 0x85, 0x46, 0xD8, 0xB9,
	0xD9, 0x85, 0xD9, 0x89, 0x46, 0xD8, 0xB9, 0xD9,
	0x85, 0xD9, 0x8A, 0x46, 0xD8, 0xBA, 0xD9, 0x85,
	0xD9, 0x85, 0x46, 0xD8, 0xBA, 0xD9, 0x85, 0xD9,
	0x89, 0x46, 0xD8, 0xBA, 0xD9, 0x85, 0xD9, 0x8A,
	// Bytes 2480 - 24bf
	0x46, 0xD9, 0x81, 0xD8, 0xAE, 0xD9, 0x85, 0x46,
	0xD9, 0x81, 0xD9, 0x85, 0xD9, 0x8A, 0x46, 0xD9,
	0x82, 0xD9, 0x84, 0xDB, 0x92, 0x46, 0xD9, 0x82,
	0xD9, 0x85, 0xD8, 0xAD, 0x46, 0xD9, 0x82, 0xD9,
	0x85, 0xD9, 0x85, 0x46, 0xD9, 0x82, 0xD9, 0x85,
	0xD9, 0x8A, 0x46, 0xD9, 0x83, 0xD9, 0x85, 0xD9,
	0x85, 0x46, 0xD9, 0x83, 0xD9, 0x85, 0xD9, 0x8A,
	0x46, 0xD9, 0x84, 0xD8, 0xAC, 0xD8, 0xAC, 0x46,
	// Bytes 24c0 - 24ff
	0xD9, 0x84, 0xD8, 0xAC, 0xD9, 0x85, 0x46, 0xD9,
	0x84, 0xD8, 0xAC, 0xD9, 0x8A, 0x46, 0xD9, 0x84,
	0xD8, 0xAD, 0xD9, 0x85, 0x46, 0xD9, 0x84, 0xD8,
	0xAD, 0xD9, 0x89, 0x46, 0xD9, 0x84, 0xD8, 0xAD,
	0xD9, 0x8A, 0x46, 0xD9, 0x84, 0xD8, 0xAE, 0xD9,
	0x85, 0x46, 0xD9, 0x84, 0xD9, 0x85, 0xD8, 0xAD,
	0x46, 0xD9, 0x84, 0xD9, 0x85, 0xD9, 0x8A, 0x46,
	0xD9, 0x85, 0xD8, 0xAC, 0xD8, 0xAD, 0x46, 0xD9,
	// Bytes 2500 - 253f
	0x85, 0xD8, 0xAC, 0xD8, 0xAE, 0x46, 0xD9, 0x85,
	0xD8, 0xAC, 0xD9, 0x85, 0x46, 0xD9, 0x85, 0xD8,
	0xAC, 0xD9, 0x8A, 0x46, 0xD9, 0x85, 0xD8, 0xAD,
	0xD8, 0xAC, 0x46, 0xD9, 0x85, 0xD8, 0xAD, 0xD9,
	0x85, 0x46, 0xD9, 0x85, 0xD8, 0xAD, 0xD9, 0x8A,
	0x46, 0xD9, 0x85, 0xD8, 0xAE, 0xD8, 0xAC, 0x46,
	0xD9, 0x85, 0xD8, 0xAE, 0xD9, 0x85, 0x46, 0xD9,
	0x85, 0xD8, 0xAE, 0xD9, 0x8A, 0x46, 0xD9, 0x85,
	// Bytes 2540 - 257f
	0xD9, 0x85, 0xD9, 0x8A, 0x46, 0xD9, 0x86, 0xD8,
	0xAC, 0xD8, 0xAD, 0x46, 0xD9, 0x86, 0xD8, 0xAC,
	0xD9, 0x85, 0x46, 0xD9, 0x86, 0xD8, 0xAC, 0xD9,
	0x89, 0x46, 0xD9, 0x86, 0xD8, 0xAC, 0xD9, 0x8A,
	0x46, 0xD9, 0x86, 0xD8, 0xAD, 0xD9, 0x85, 0x46,
	0xD9, 0x86, 0xD8, 0xAD, 0xD9, 0x89, 0x46, 0xD9,
	0x86, 0xD8, 0xAD, 0xD9, 0x8A, 0x46, 0xD9, 0x86,
	0xD9, 0x85, 0xD9, 0x89, 0x46, 0xD9, 0x86, 0xD9,
	// Bytes 2580 - 25bf
	0x85, 0xD9, 0x8A, 0x46, 0xD9, 0x87, 0xD9, 0x85,
	0xD8, 0xAC, 0x46, 0xD9, 0x87, 0xD9, 0x85, 0xD9,
	0x85, 0x46, 0xD9, 0x8A, 0xD8, 0xAC, 0xD9, 0x8A,
	0x46, 0xD9, 0x8A, 0xD8, 0xAD, 0xD9, 0x8A, 0x46,
	0xD9, 0x8A, 0xD9, 0x85, 0xD9, 0x85, 0x46, 0xD9,
	0x8A, 0xD9, 0x85, 0xD9, 0x8A, 0x46, 0xD9, 0x8A,
	0xD9, 0x94, 0xD8, 0xA7, 0x46, 0xD9, 0x8A, 0xD9,
	0x94, 0xD8, 0xAC, 0x46, 0xD9, 0x8A, 0xD9, 0x94,
	// Bytes 25c0 - 25ff
	0xD8, 0xAD, 0x46, 0xD9, 0x8A, 0xD9, 0x94, 0xD8,
	0xAE, 0x46, 0xD9, 0x8A, 0xD9, 0x94, 0xD8, 0xB1,
	0x46, 0xD9, 0x8A, 0xD9, 0x94, 0xD8, 0xB2, 0x46,
	0xD9, 0x8A, 0xD9, 0x94, 0xD9, 0x85, 0x46, 0xD9,
	0x8A, 0xD9, 0x94, 0xD9, 0x86, 0x46, 0xD9, 0x8A,
	0xD9, 0x94, 0xD9, 0x87, 0x46, 0xD9, 0x8A, 0xD9,
	0x94, 0xD9, 0x88, 0x46, 0xD9, 0x8A, 0xD9, 0x94,
	0xD9, 0x89, 0x46, 0xD9, 0x8A, 0xD9, 0x94, 0xD9,
	// Bytes 2600 - 263f
	0x8A, 0x46, 0xD9, 0x8A, 0xD9, 0x94, 0xDB, 0x86,
	0x46, 0xD9, 0x8A, 0xD9, 0x94, 0xDB, 0x87, 0x46,
	0xD9, 0x8A, 0xD9, 0x94, 0xDB, 0x88, 0x46, 0xD9,
	0x8A, 0xD9, 0x94, 0xDB, 0x90, 0x46, 0xD9, 0x8A,
	0xD9, 0x94, 0xDB, 0x95, 0x46, 0xE0, 0xB9, 0x8D,
	0xE0, 0xB8, 0xB2, 0x46, 0xE0, 0xBA, 0xAB, 0xE0,
	0xBA, 0x99, 0x46, 0xE0, 0xBA, 0xAB, 0xE0, 0xBA,
	0xA1, 0x46, 0xE0, 0xBB, 0x8D, 0xE0, 0xBA, 0xB2,
	// Bytes 2640 - 267f
	0x46, 0xE0, 0xBD, 0x80, 0xE0, 0xBE, 0xB5, 0x46,
	0xE0, 0xBD, 0x82, 0xE0, 0xBE, 0xB7, 0x46, 0xE0,
	0xBD, 0x8C, 0xE0, 0xBE, 0xB7, 0x46, 0xE0, 0xBD,
	0x91, 0xE0, 0xBE, 0xB7, 0x46, 0xE0, 0xBD, 0x96,
	0xE0, 0xBE, 0xB7, 0x46, 0xE0, 0xBD, 0x9B, 0xE0,
	0xBE, 0xB7, 0x46, 0xE0, 0xBE, 0x90, 0xE0, 0xBE,
	0xB5, 0x46, 0xE0, 0xBE, 0x92, 0xE0, 0xBE, 0xB7,
	0x46, 0xE0, 0xBE, 0x9C, 0xE0, 0xBE, 0xB7, 0x46,
	// Bytes 2680 - 26bf
	0xE0, 0xBE, 0xA1, 0xE0, 0xBE, 0xB7, 0x46, 0xE0,
	0xBE, 0xA6, 0xE0, 0xBE, 0xB7, 0x46, 0xE0, 0xBE,
	0xAB, 0xE0, 0xBE, 0xB7, 0x46, 0xE2, 0x80, 0xB2,
	0xE2, 0x80, 0xB2, 0x46, 0xE2, 0x80, 0xB5, 0xE2,
	0x80, 0xB5, 0x46, 0xE2, 0x88, 0xAB, 0xE2, 0x88,
	0xAB, 0x46, 0xE2, 0x88, 0xAE, 0xE2, 0x88, 0xAE,
	0x46, 0xE3, 0x81, 0xBB, 0xE3, 0x81, 0x8B, 0x46,
	0xE3, 0x82, 0x88, 0xE3, 0x82, 0x8A, 0x46, 0xE3,
	// Bytes 26c0 - 26ff
	0x82, 0xAD, 0xE3, 0x83, 0xAD, 0x46, 0xE3, 0x82,
	0xB3, 0xE3, 0x82, 0xB3, 0x46, 0xE3, 0x82, 0xB3,
	0xE3, 0x83, 0x88, 0x46, 0xE3, 0x83, 0x88, 0xE3,
	0x83, 0xB3, 0x46, 0xE3, 0x83, 0x8A, 0xE3, 0x83,
	0x8E, 0x46, 0xE3, 0x83, 0x9B, 0xE3, 0x83, 0xB3,
	0x46, 0xE3, 0x83, 0x9F, 0xE3, 0x83, 0xAA, 0x46,
	0xE3, 0x83, 0xAA, 0xE3, 0x83, 0xA9, 0x46, 0xE3,
	0x83, 0xAC, 0xE3, 0x83, 0xA0, 0x46, 0xE5, 0xA4,
	// Bytes 2700 - 273f
	0xA7, 0xE6, 0xAD, 0xA3, 0x46, 0xE5, 0xB9, 0xB3,
	0xE6, 0x88, 0x90, 0x46, 0xE6, 0x98, 0x8E, 0xE6,
	0xB2, 0xBB, 0x46, 0xE6, 0x98, 0xAD, 0xE5, 0x92,
	0x8C, 0x47, 0x72, 0x61, 0x64, 0xE2, 0x88, 0x95,
	0x73, 0x47, 0xE3, 0x80, 0x94, 0x53, 0xE3, 0x80,
	0x95, 0x48, 0x28, 0xE1, 0x84, 0x80, 0xE1, 0x85,
	0xA1, 0x29, 0x48, 0x28, 0xE1, 0x84, 0x82, 0xE1,
	0x85, 0xA1, 0x29, 0x48, 0x28, 0xE1, 0x84, 0x83,
	// Bytes 2740 - 277f
	0xE1, 0x85, 0xA1, 0x29, 0x48, 0x28, 0xE1, 0x84,
	0x85, 0xE1, 0x85, 0xA1, 0x29, 0x48, 0x28, 0xE1,
	0x84, 0x86, 0xE1, 0x85, 0xA1, 0x29, 0x48, 0x28,
	0xE1, 0x84, 0x87, 0xE1, 0x85, 0xA1, 0x29, 0x48,
	0x28, 0xE1, 0x84, 0x89, 0xE1, 0x85, 0xA1, 0x29,
	0x48, 0x28, 0xE1, 0x84, 0x8B, 0xE1, 0x85, 0xA1,
	0x29, 0x48, 0x28, 0xE1, 0x84, 0x8C, 0xE1, 0x85,
	0xA1, 0x29, 0x48, 0x28, 0xE1, 0x84, 0x8C, 0xE1,
	// Bytes 2780 - 27bf
	0x85, 0xAE, 0x29, 0x48, 0x28, 0xE1, 0x84, 0x8E,
	0xE1, 0x85, 0xA1, 0x29, 0x48, 0x28, 0xE1, 0x84,
	0x8F, 0xE1, 0x85, 0xA1, 0x29, 0x48, 0x28, 0xE1,
	0x84, 0x90, 0xE1, 0x85, 0xA1, 0x29, 0x48, 0x28,
	0xE1, 0x84, 0x91, 0xE1, 0x85, 0xA1, 0x29, 0x48,
	0x28, 0xE1, 0x84, 0x92, 0xE1, 0x85, 0xA1, 0x29,
	0x48, 0x72, 0x61, 0x64, 0xE2, 0x88, 0x95, 0x73,
	0x32, 0x48, 0xD8, 0xA7, 0xD9, 0x83, 0xD8, 0xA8,
	// Bytes 27c0 - 27ff
	0xD8, 0xB1, 0x48, 0xD8, 0xA7, 0xD9, 0x84, 0xD9,
	0x84, 0xD9, 0x87, 0x48, 0xD8, 0xB1, 0xD8, 0xB3,
	0xD9, 0x88, 0xD9, 0x84, 0x48, 0xD8, 0xB1, 0xDB,
	0x8C, 0xD8, 0xA7, 0xD9, 0x84, 0x48, 0xD8, 0xB5,
	0xD9, 0x84, 0xD8, 0xB9, 0xD9, 0x85, 0x48, 0xD8,
	0xB9, 0xD9, 0x84, 0xD9, 0x8A, 0xD9, 0x87, 0x48,
	0xD9, 0x85, 0xD8, 0xAD, 0xD9, 0x85, 0xD8, 0xAF,
	0x48, 0xD9, 0x88, 0xD8, 0xB3, 0xD9, 0x84, 0xD9,
	// Bytes 2800 - 283f
	0x85, 0x49, 0xE2, 0x80, 0xB2, 0xE2, 0x80, 0xB2,
	0xE2, 0x80, 0xB2, 0x49, 0xE2, 0x80, 0xB5, 0xE2,
	0x80, 0xB5, 0xE2, 0x80, 0xB5, 0x49, 0xE2, 0x88,
	0xAB, 0xE2, 0x88, 0xAB, 0xE2, 0x88, 0xAB, 0x49,
	0xE2, 0x88, 0xAE, 0xE2, 0x88, 0xAE, 0xE2, 0x88,
	0xAE, 0x49, 0xE3, 0x80, 0x94, 0xE4, 0xB8, 0x89,
	0xE3, 0x80, 0x95, 0x49, 0xE3, 0x80, 0x94, 0xE4,
	0xBA, 0x8C, 0xE3, 0x80, 0x95, 0x49, 0xE3, 0x80,
	// Bytes 2840 - 287f
	0x94, 0xE5, 0x8B, 0x9D, 0xE3, 0x80, 0x95, 0x49,
	0xE3, 0x80, 0x94, 0xE5, 0xAE, 0x89, 0xE3, 0x80,
	0x95, 0x49, 0xE3, 0x80, 0x94, 0xE6, 0x89, 0x93,
	0xE3, 0x80, 0x95, 0x49, 0xE3, 0x80, 0x94, 0xE6,
	0x95, 0x97, 0xE3, 0x80, 0x95, 0x49, 0xE3, 0x80,
	0x94, 0xE6, 0x9C, 0xAC, 0xE3, 0x80, 0x95, 0x49,
	0xE3, 0x80, 0x94, 0xE7, 0x82, 0xB9, 0xE3, 0x80,
	0x95, 0x49, 0xE3, 0x80, 0x94, 0xE7, 0x9B, 0x97,
	// Bytes 2880 - 28bf
	0xE3, 0x80, 0x95, 0x49, 0xE3, 0x82, 0xA2, 0xE3,
	0x83, 0xBC, 0xE3, 0x83, 0xAB, 0x49, 0xE3, 0x82,
	0xA4, 0xE3, 0x83, 0xB3, 0xE3, 0x83, 0x81, 0x49,
	0xE3, 0x82, 0xA6, 0xE3, 0x82, 0xA9, 0xE3, 0x83,
	0xB3, 0x49, 0xE3, 0x82, 0xAA, 0xE3, 0x83, 0xB3,
	0xE3, 0x82, 0xB9, 0x49, 0xE3, 0x82, 0xAA, 0xE3,
	0x83, 0xBC, 0xE3, 0x83, 0xA0, 0x49, 0xE3, 0x82,
	0xAB, 0xE3, 0x82, 0xA4, 0xE3, 0x83, 0xAA, 0x49,
	// Bytes 28c0 - 28ff
	0xE3, 0x82, 0xB1, 0xE3, 0x83, 0xBC, 0xE3, 0x82,
	0xB9, 0x49, 0xE3, 0x82, 0xB3, 0xE3, 0x83, 0xAB,
	0xE3, 0x83, 0x8A, 0x49, 0xE3, 0x82, 0xBB, 0xE3,
	0x83, 0xB3, 0xE3, 0x83, 0x81, 0x49, 0xE3, 0x82,
	0xBB, 0xE3, 0x83, 0xB3, 0xE3, 0x83, 0x88, 0x49,
	0xE3, 0x83, 0x86, 0xE3, 0x82, 0x99, 0xE3, 0x82,
	0xB7, 0x49, 0xE3, 0x83, 0x88, 0xE3, 0x82, 0x99,
	0xE3, 0x83, 0xAB, 0x49, 0xE3, 0x83, 0x8E, 0xE3,
	// Bytes 2900 - 293f
	0x83, 0x83, 0xE3, 0x83, 0x88, 0x49, 0xE3, 0x83,
	0x8F, 0xE3, 0x82, 0xA4, 0xE3, 0x83, 0x84, 0x49,
	0xE3, 0x83, 0x92, 0xE3, 0x82, 0x99, 0xE3, 0x83,
	0xAB, 0x49, 0xE3, 0x83, 0x92, 0xE3, 0x82, 0x9A,
	0xE3, 0x82, 0xB3, 0x49, 0xE3, 0x83, 0x95, 0xE3,
	0x83, 0xA9, 0xE3, 0x83, 0xB3, 0x49, 0xE3, 0x83,
	0x98, 0xE3, 0x82, 0x9A, 0xE3, 0x82, 0xBD, 0x49,
	0xE3, 0x83, 0x98, 0xE3, 0x83, 0xAB, 0xE3, 0x83,
	// Bytes 2940 - 297f
	0x84, 0x49, 0xE3, 0x83, 0x9B, 0xE3, 0x83, 0xBC,
	0xE3, 0x83, 0xAB, 0x49, 0xE3, 0x83, 0x9B, 0xE3,
	0x83, 0xBC, 0xE3, 0x83, 0xB3, 0x49, 0xE3, 0x83,
	0x9E, 0xE3, 0x82, 0xA4, 0xE3, 0x83, 0xAB, 0x49,
	0xE3, 0x83, 0x9E, 0xE3, 0x83, 0x83, 0xE3, 0x83,
	0x8F, 0x49, 0xE3, 0x83, 0x9E, 0xE3, 0x83, 0xAB,
	0xE3, 0x82, 0xAF, 0x49, 0xE3, 0x83, 0xA4, 0xE3,
	0x83, 0xBC, 0xE3, 0x83, 0xAB, 0x49, 0xE3, 0x83,
	// Bytes 2980 - 29bf
	0xA6, 0xE3, 0x82, 0xA2, 0xE3, 0x83, 0xB3, 0x49,
	0xE3, 0x83, 0xAF, 0xE3, 0x83, 0x83, 0xE3, 0x83,
	0x88, 0x4C, 0xE2, 0x80, 0xB2, 0xE2, 0x80, 0xB2,
	0xE2, 0x80, 0xB2, 0xE2, 0x80, 0xB2, 0x4C, 0xE2,
	0x88, 0xAB, 0xE2, 0x88, 0xAB, 0xE2, 0x88, 0xAB,
	0xE2, 0x88, 0xAB, 0x4C, 0xE3, 0x82, 0xA2, 0xE3,
	0x83, 0xAB, 0xE3, 0x83, 0x95, 0xE3, 0x82, 0xA1,
	0x4C, 0xE3, 0x82, 0xA8, 0xE3, 0x83, 0xBC, 0xE3,
	// Bytes 29c0 - 29ff
	0x82, 0xAB, 0xE3, 0x83, 0xBC, 0x4C, 0xE3, 0x82,
	0xAB, 0xE3, 0x82, 0x99, 0xE3, 0x83, 0xAD, 0xE3,
	0x83, 0xB3, 0x4C, 0xE3, 0x82, 0xAB, 0xE3, 0x82,
	0x99, 0xE3, 0x83, 0xB3, 0xE3, 0x83, 0x9E, 0x4C,
	0xE3, 0x82, 0xAB, 0xE3, 0x83, 0xA9, 0xE3, 0x83,
	0x83, 0xE3, 0x83, 0x88, 0x4C, 0xE3, 0x82, 0xAB,
	0xE3, 0x83, 0xAD, 0xE3, 0x83, 0xAA, 0xE3, 0x83,
	0xBC, 0x4C, 0xE3, 0x82, 0xAD, 0xE3, 0x82, 0x99,
	// Bytes 2a00 - 2a3f
	0xE3, 0x83, 0x8B, 0xE3, 0x83, 0xBC, 0x4C, 0xE3,
	0x82, 0xAD, 0xE3, 0x83, 0xA5, 0xE3, 0x83, 0xAA,
	0xE3, 0x83, 0xBC, 0x4C, 0xE3, 0x82, 0xAF, 0xE3,
	0x82, 0x99, 0xE3, 0x83, 0xA9, 0xE3, 0x83, 0xA0,
	0x4C, 0xE3, 0x82, 0xAF, 0xE3, 0x83, 0xAD, 0xE3,
	0x83, 0xBC, 0xE3, 0x83, 0x8D, 0x4C, 0xE3, 0x82,
	0xB5, 0xE3, 0x82, 0xA4, 0xE3, 0x82, 0xAF, 0xE3,
	0x83, 0xAB, 0x4C, 0xE3, 0x82, 0xBF, 0xE3, 0x82,
	// Bytes 2a40 - 2a7f
	0x99, 0xE3, 0x83, 0xBC, 0xE3, 0x82, 0xB9, 0x4C,
	0xE3, 0x83, 0x8F, 0xE3, 0x82, 0x9A, 0xE3, 0x83,
	0xBC, 0xE3, 0x83, 0x84, 0x4C, 0xE3, 0x83, 0x92,
	0xE3, 0x82, 0x9A, 0xE3, 0x82, 0xAF, 0xE3, 0x83,
	0xAB, 0x4C, 0xE3, 0x83, 0x95, 0xE3, 0x82, 0xA3,
	0xE3, 0x83, 0xBC, 0xE3, 0x83, 0x88, 0x4C, 0xE3,
	0x83, 0x98, 0xE3, 0x82, 0x99, 0xE3, 0x83, 0xBC,
	0xE3, 0x82, 0xBF, 0x4C, 0xE3, 0x83, 0x98, 0xE3,
	// Bytes 2a80 - 2abf
	0x82, 0x9A, 0xE3, 0x83, 0x8B, 0xE3, 0x83, 0x92,
	0x4C, 0xE3, 0x83, 0x98, 0xE3, 0x82, 0x9A, 0xE3,
	0x83, 0xB3, 0xE3, 0x82, 0xB9, 0x4C, 0xE3, 0x83,
	0x9B, 0xE3, 0x82, 0x99, 0xE3, 0x83, 0xAB, 0xE3,
	0x83, 0x88, 0x4C, 0xE3, 0x83, 0x9E, 0xE3, 0x82,
	0xA4, 0xE3, 0x82, 0xAF, 0xE3, 0x83, 0xAD, 0x4C,
	0xE3, 0x83, 0x9F, 0xE3, 0x82, 0xAF, 0xE3, 0x83,
	0xAD, 0xE3, 0x83, 0xB3, 0x4C, 0xE3, 0x83, 0xA1,
	// Bytes 2ac0 - 2aff
	0xE3, 0x83, 0xBC, 0xE3, 0x83, 0x88, 0xE3, 0x83,
	0xAB, 0x4C, 0xE3, 0x83, 0xAA, 0xE3, 0x83, 0x83,
	0xE3, 0x83, 0x88, 0xE3, 0x83, 0xAB, 0x4C, 0xE3,
	0x83, 0xAB, 0xE3, 0x83, 0x92, 0xE3, 0x82, 0x9A,
	0xE3, 0x83, 0xBC, 0x4C, 0xE6, 0xA0, 0xAA, 0xE5,
	0xBC, 0x8F, 0xE4, 0xBC, 0x9A, 0xE7, 0xA4, 0xBE,
	0x4E, 0x28, 0xE1, 0x84, 0x8B, 0xE1, 0x85, 0xA9,
	0xE1, 0x84, 0x92, 0xE1, 0x85, 0xAE, 0x29, 0x4F,
	// Bytes 2b00 - 2b3f
	0xD8, 0xAC, 0xD9, 0x84, 0x20, 0xD8, 0xAC, 0xD9,
	0x84, 0xD8, 0xA7, 0xD9, 0x84, 0xD9, 0x87, 0x4F,
	0xE3, 0x82, 0xA2, 0xE3, 0x83, 0x8F, 0xE3, 0x82,
	0x9A, 0xE3, 0x83, 0xBC, 0xE3, 0x83, 0x88, 0x4F,
	0xE3, 0x82, 0xA2, 0xE3, 0x83, 0xB3, 0xE3, 0x83,
	0x98, 0xE3, 0x82, 0x9A, 0xE3, 0x82, 0xA2, 0x4F,
	0xE3, 0x82, 0xAD, 0xE3, 0x83, 0xAD, 0xE3, 0x83,
	0xAF, 0xE3, 0x83, 0x83, 0xE3, 0x83, 0x88, 0x4F,
	// Bytes 2b40 - 2b7f
	0xE3, 0x82, 0xB5, 0xE3, 0x83, 0xB3, 0xE3, 0x83,
	0x81, 0xE3, 0x83, 0xBC, 0xE3, 0x83, 0xA0, 0x4F,
	0xE3, 0x83, 0x8F, 0xE3, 0x82, 0x99, 0xE3, 0x83,
	0xBC, 0xE3, 0x83, 0xAC, 0xE3, 0x83, 0xAB, 0x4F,
	0xE3, 0x83, 0x98, 0xE3, 0x82, 0xAF, 0xE3, 0x82,
	0xBF, 0xE3, 0x83, 0xBC, 0xE3, 0x83, 0xAB, 0x4F,
	0xE3, 0x83, 0x9B, 0xE3, 0x82, 0x9A, 0xE3, 0x82,
	0xA4, 0xE3, 0x83, 0xB3, 0xE3, 0x83, 0x88, 0x4F,
	// Bytes 2b80 - 2bbf
	0xE3, 0x83, 0x9E, 0xE3, 0x83, 0xB3, 0xE3, 0x82,
	0xB7, 0xE3, 0x83, 0xA7, 0xE3, 0x83, 0xB3, 0x4F,
	0xE3, 0x83, 0xA1, 0xE3, 0x82, 0xAB, 0xE3, 0x82,
	0x99, 0xE3, 0x83, 0x88, 0xE3, 0x83, 0xB3, 0x4F,
	0xE3, 0x83, 0xAB, 0xE3, 0x83, 0xBC, 0xE3, 0x83,
	0x95, 0xE3, 0x82, 0x99, 0xE3, 0x83, 0xAB, 0x51,
	0x28, 0xE1, 0x84, 0x8B, 0xE1, 0x85, 0xA9, 0xE1,
	0x84, 0x8C, 0xE1, 0x85, 0xA5, 0xE1, 0x86, 0xAB,
	// Bytes 2bc0 - 2bff
	0x29, 0x52, 0xE3, 0x82, 0xAD, 0xE3, 0x82, 0x99,
	0xE3, 0x83, 0xAB, 0xE3, 0x82, 0xBF, 0xE3, 0x82,
	0x99, 0xE3, 0x83, 0xBC, 0x52, 0xE3, 0x82, 0xAD,
	0xE3, 0x83, 0xAD, 0xE3, 0x82, 0xAF, 0xE3, 0x82,
	0x99, 0xE3, 0x83, 0xA9, 0xE3, 0x83, 0xA0, 0x52,
	0xE3, 0x82, 0xAD, 0xE3, 0x83, 0xAD, 0xE3, 0x83,
	0xA1, 0xE3, 0x83, 0xBC, 0xE3, 0x83, 0x88, 0xE3,
	0x83, 0xAB, 0x52, 0xE3, 0x82, 0xAF, 0xE3, 0x82,
	// Bytes 2c00 - 2c3f
	0x99, 0xE3, 0x83, 0xA9, 0xE3, 0x83, 0xA0, 0xE3,
	0x83, 0x88, 0xE3, 0x83, 0xB3, 0x52, 0xE3, 0x82,
	0xAF, 0xE3, 0x83, 0xAB, 0xE3, 0x82, 0xBB, 0xE3,
	0x82, 0x99, 0xE3, 0x82, 0xA4, 0xE3, 0x83, 0xAD,
	0x52, 0xE3, 0x83, 0x8F, 0xE3, 0x82, 0x9A, 0xE3,
	0x83, 0xBC, 0xE3, 0x82, 0xBB, 0xE3, 0x83, 0xB3,
	0xE3, 0x83, 0x88, 0x52, 0xE3, 0x83, 0x92, 0xE3,
	0x82, 0x9A, 0xE3, 0x82, 0xA2, 0xE3, 0x82, 0xB9,
	// Bytes 2c40 - 2c7f
	0xE3, 0x83, 0x88, 0xE3, 0x83, 0xAB, 0x52, 0xE3,
	0x83, 0x95, 0xE3, 0x82, 0x99, 0xE3, 0x83, 0x83,
	0xE3, 0x82, 0xB7, 0xE3, 0x82, 0xA7, 0xE3, 0x83,
	0xAB, 0x52, 0xE3, 0x83, 0x9F, 0xE3, 0x83, 0xAA,
	0xE3, 0x83, 0x8F, 0xE3, 0x82, 0x99, 0xE3, 0x83,
	0xBC, 0xE3, 0x83, 0xAB, 0x52, 0xE3, 0x83, 0xAC,
	0xE3, 0x83, 0xB3, 0xE3, 0x83, 0x88, 0xE3, 0x82,
	0xB1, 0xE3, 0x82, 0x99, 0xE3, 0x83, 0xB3, 0x61,
	// Bytes 2c80 - 2cbf
	0xD8, 0xB5, 0xD9, 0x84, 0xD9, 0x89, 0x20, 0xD8,
	0xA7, 0xD9, 0x84, 0xD9, 0x84, 0xD9, 0x87, 0x20,
	0xD8, 0xB9, 0xD9, 0x84, 0xD9, 0x8A, 0xD9, 0x87,
	0x20, 0xD9, 0x88, 0xD8, 0xB3, 0xD9, 0x84, 0xD9,
	0x85, 0x06, 0xE0, 0xA7, 0x87, 0xE0, 0xA6, 0xBE,
	0x01, 0x06, 0xE0, 0xA7, 0x87, 0xE0, 0xA7, 0x97,
	0x01, 0x06, 0xE0, 0xAD, 0x87, 0xE0, 0xAC, 0xBE,
	0x01, 0x06, 0xE0, 0xAD, 0x87, 0xE0, 0xAD, 0x96,
	// Bytes 2cc0 - 2cff
	0x01, 0x06, 0xE0, 0xAD, 0x87, 0xE0, 0xAD, 0x97,
	0x01, 0x06, 0xE0, 0xAE, 0x92, 0xE0, 0xAF, 0x97,
	0x01, 0x06, 0xE0, 0xAF, 0x86, 0xE0, 0xAE, 0xBE,
	0x01, 0x06, 0xE0, 0xAF, 0x86, 0xE0, 0xAF, 0x97,
	0x01, 0x06, 0xE0, 0xAF, 0x87, 0xE0, 0xAE, 0xBE,
	0x01, 0x06, 0xE0, 0xB2, 0xBF, 0xE0, 0xB3, 0x95,
	0x01, 0x06, 0xE0, 0xB3, 0x86, 0xE0, 0xB3, 0x95,
	0x01, 0x06, 0xE0, 0xB3, 0x86, 0xE0, 0xB3, 0x96,
	// Bytes 2d00 - 2d3f
	0x01, 0x06, 0xE0, 0xB5, 0x86, 0xE0, 0xB4, 0xBE,
	0x01, 0x06, 0xE0, 0xB5, 0x86, 0xE0, 0xB5, 0x97,
	0x01, 0x06, 0xE0, 0xB5, 0x87, 0xE0, 0xB4, 0xBE,
	0x01, 0x06, 0xE0, 0xB7, 0x99, 0xE0, 0xB7, 0x9F,
	0x01, 0x06, 0xE1, 0x80, 0xA5, 0xE1, 0x80, 0xAE,
	0x01, 0x06, 0xE1, 0xAC, 0x85, 0xE1, 0xAC, 0xB5,
	0x01, 0x06, 0xE1, 0xAC, 0x87, 0xE1, 0xAC, 0xB5,
	0x01, 0x06, 0xE1, 0xAC, 0x89, 0xE1, 0xAC, 0xB5,
	// Bytes 2d40 - 2d7f
	0x01, 0x06, 0xE1, 0xAC, 0x8B, 0xE1, 0xAC, 0xB5,
	0x01, 0x06, 0xE1, 0xAC, 0x8D, 0xE1, 0xAC, 0xB5,
	0x01, 0x06, 0xE1, 0xAC, 0x91, 0xE1, 0xAC, 0xB5,
	0x01, 0x06, 0xE1, 0xAC, 0xBA, 0xE1, 0xAC, 0xB5,
	0x01, 0x06, 0xE1, 0xAC, 0xBC, 0xE1, 0xAC, 0xB5,
	0x01, 0x06, 0xE1, 0xAC, 0xBE, 0xE1, 0xAC, 0xB5,
	0x01, 0x06, 0xE1, 0xAC, 0xBF, 0xE1, 0xAC, 0xB5,
	0x01, 0x06, 0xE1, 0xAD, 0x82, 0xE1, 0xAC, 0xB5,
	// Bytes 2d80 - 2dbf
	0x01, 0x08, 0xF0, 0x91, 0x84, 0xB1, 0xF0, 0x91,
	0x84, 0xA7, 0x01, 0x08, 0xF0, 0x91, 0x84, 0xB2,
	0xF0, 0x91, 0x84, 0xA7, 0x01, 0x08, 0xF0, 0x91,
	0x8D, 0x87, 0xF0, 0x91, 0x8C, 0xBE, 0x01, 0x08,
	0xF0, 0x91, 0x8D, 0x87, 0xF0, 0x91, 0x8D, 0x97,
	0x01, 0x08, 0xF0, 0x91, 0x92, 0xB9, 0xF0, 0x91,
	0x92, 0xB0, 0x01, 0x08, 0xF0, 0x91, 0x92, 0xB9,
	0xF0, 0x91, 0x92, 0xBA, 0x01, 0x08, 0xF0, 0x91,
	// Bytes 2dc0 - 2dff
	0x92, 0xB9, 0xF0, 0x91, 0x92, 0xBD, 0x01, 0x08,
	0xF0, 0x91, 0x96, 0xB8, 0xF0, 0x91, 0x96, 0xAF,
	0x01, 0x08, 0xF0, 0x91, 0x96, 0xB9, 0xF0, 0x91,
	0x96, 0xAF, 0x01, 0x09, 0xE0, 0xB3, 0x86, 0xE0,
	0xB3, 0x82, 0xE0, 0xB3, 0x95, 0x02, 0x09, 0xE0,
	0xB7, 0x99, 0xE0, 0xB7, 0x8F, 0xE0, 0xB7, 0x8A,
	0x12, 0x44, 0x44, 0x5A, 0xCC, 0x8C, 0xC9, 0x44,
	0x44, 0x7A, 0xCC, 0x8C, 0xC9, 0x44, 0x64, 0x7A,
	// Bytes 2e00 - 2e3f
	0xCC, 0x8C, 0xC9, 0x46, 0xD9, 0x84, 0xD8, 0xA7,
	0xD9, 0x93, 0xC9, 0x46, 0xD9, 0x84, 0xD8, 0xA7,
	0xD9, 0x94, 0xC9, 0x46, 0xD9, 0x84, 0xD8, 0xA7,
	0xD9, 0x95, 0xB5, 0x46, 0xE1, 0x84, 0x80, 0xE1,
	0x85, 0xA1, 0x01, 0x46, 0xE1, 0x84, 0x82, 0xE1,
	0x85, 0xA1, 0x01, 0x46, 0xE1, 0x84, 0x83, 0xE1,
	0x85, 0xA1, 0x01, 0x46, 0xE1, 0x84, 0x85, 0xE1,
	0x85, 0xA1, 0x01, 0x46, 0xE1, 0x84, 0x86, 0xE1,
	// Bytes 2e40 - 2e7f
	0x85, 0xA1, 0x01, 0x46, 0xE1, 0x84, 0x87, 0xE1,
	0x85, 0xA1, 0x01, 0x46, 0xE1, 0x84, 0x89, 0xE1,
	0x85, 0xA1, 0x01, 0x46, 0xE1, 0x84, 0x8B, 0xE1,
	0x85, 0xA1, 0x01, 0x46, 0xE1, 0x84, 0x8B, 0xE1,
	0x85, 0xAE, 0x01, 0x46, 0xE1, 0x84, 0x8C, 0xE1,
	0x85, 0xA1, 0x01, 0x46, 0xE1, 0x84, 0x8E, 0xE1,
	0x85, 0xA1, 0x01, 0x46, 0xE1, 0x84, 0x8F, 0xE1,
	0x85, 0xA1, 0x01, 0x46, 0xE1, 0x84, 0x90, 0xE1,
	// Bytes 2e80 - 2ebf
	0x85, 0xA1, 0x01, 0x46, 0xE1, 0x84, 0x91, 0xE1,
	0x85, 0xA1, 0x01, 0x46, 0xE1, 0x84, 0x92, 0xE1,
	0x85, 0xA1, 0x01, 0x49, 0xE3, 0x83, 0xA1, 0xE3,
	0x82, 0xAB, 0xE3, 0x82, 0x99, 0x0D, 0x4C, 0xE1,
	0x84, 0x8C, 0xE1, 0x85, 0xAE, 0xE1, 0x84, 0x8B,
	0xE1, 0x85, 0xB4, 0x01, 0x4C, 0xE3, 0x82, 0xAD,
	0xE3, 0x82, 0x99, 0xE3, 0x82, 0xAB, 0xE3, 0x82,
	0x99, 0x0D, 0x4C, 0xE3, 0x82, 0xB3, 0xE3, 0x83,
	// Bytes 2ec0 - 2eff
	0xBC, 0xE3, 0x83, 0x9B, 0xE3, 0x82, 0x9A, 0x0D,
	0x4C, 0xE3, 0x83, 0xA4, 0xE3, 0x83, 0xBC, 0xE3,
	0x83, 0x88, 0xE3, 0x82, 0x99, 0x0D, 0x4F, 0xE1,
	0x84, 0x8E, 0xE1, 0x85, 0xA1, 0xE1, 0x86, 0xB7,
	0xE1, 0x84, 0x80, 0xE1, 0x85, 0xA9, 0x01, 0x4F,
	0xE3, 0x82, 0xA4, 0xE3, 0x83, 0x8B, 0xE3, 0x83,
	0xB3, 0xE3, 0x82, 0xAF, 0xE3, 0x82, 0x99, 0x0D,
	0x4F, 0xE3, 0x82, 0xB7, 0xE3, 0x83, 0xAA, 0xE3,
	// Bytes 2f00 - 2f3f
	0x83, 0xB3, 0xE3, 0x82, 0xAF, 0xE3, 0x82, 0x99,
	0x0D, 0x4F, 0xE3, 0x83, 0x98, 0xE3, 0x82, 0x9A,
	0xE3, 0x83, 0xBC, 0xE3, 0x82, 0xB7, 0xE3, 0x82,
	0x99, 0x0D, 0x4F, 0xE3, 0x83, 0x9B, 0xE3, 0x82,
	0x9A, 0xE3, 0x83, 0xB3, 0xE3, 0x83, 0x88, 0xE3,
	0x82, 0x99, 0x0D, 0x52, 0xE3, 0x82, 0xA8, 0xE3,
	0x82, 0xB9, 0xE3, 0x82, 0xAF, 0xE3, 0x83, 0xBC,
	0xE3, 0x83, 0x88, 0xE3, 0x82, 0x99, 0x0D, 0x52,
	// Bytes 2f40 - 2f7f
	0xE3, 0x83, 0x95, 0xE3, 0x82, 0xA1, 0xE3, 0x83,
	0xA9, 0xE3, 0x83, 0x83, 0xE3, 0x83, 0x88, 0xE3,
	0x82, 0x99, 0x0D, 0x86, 0xE0, 0xB3, 0x86, 0xE0,
	0xB3, 0x82, 0x01, 0x86, 0xE0, 0xB7, 0x99, 0xE0,
	0xB7, 0x8F, 0x01, 0x03, 0x3C, 0xCC, 0xB8, 0x05,
	0x03, 0x3D, 0xCC, 0xB8, 0x05, 0x03, 0x3E, 0xCC,
	0xB8, 0x05, 0x03, 0x41, 0xCC, 0x80, 0xC9, 0x03,
	0x41, 0xCC, 0x81, 0xC9, 0x03, 0x41, 0xCC, 0x83,
	// Bytes 2f80 - 2fbf
	0xC9, 0x03, 0x41, 0xCC, 0x84, 0xC9, 0x03, 0x41,
	0xCC, 0x89, 0xC9, 0x03, 0x41, 0xCC, 0x8C, 0xC9,
	0x03, 0x41, 0xCC, 0x8F, 0xC9, 0x03, 0x41, 0xCC,
	0x91, 0xC9, 0x03, 0x41, 0xCC, 0xA5, 0xB5, 0x03,
	0x41, 0xCC, 0xA8, 0xA5, 0x03, 0x42, 0xCC, 0x87,
	0xC9, 0x03, 0x42, 0xCC, 0xA3, 0xB5, 0x03, 0x42,
	0xCC, 0xB1, 0xB5, 0x03, 0x43, 0xCC, 0x81, 0xC9,
	0x03, 0x43, 0xCC, 0x82, 0xC9, 0x03, 0x43, 0xCC,
	// Bytes 2fc0 - 2fff
	0x87, 0xC9, 0x03, 0x43, 0xCC, 0x8C, 0xC9, 0x03,
	0x44, 0xCC, 0x87, 0xC9, 0x03, 0x44, 0xCC, 0x8C,
	0xC9, 0x03, 0x44, 0xCC, 0xA3, 0xB5, 0x03, 0x44,
	0xCC, 0xA7, 0xA5, 0x03, 0x44, 0xCC, 0xAD, 0xB5,
	0x03, 0x44, 0xCC, 0xB1, 0xB5, 0x03, 0x45, 0xCC,
	0x80, 0xC9, 0x03, 0x45, 0xCC, 0x81, 0xC9, 0x03,
	0x45, 0xCC, 0x83, 0xC9, 0x03, 0x45, 0xCC, 0x86,
	0xC9, 0x03, 0x45, 0xCC, 0x87, 0xC9, 0x03, 0x45,
	// Bytes 3000 - 303f
	0xCC, 0x88, 0xC9, 0x03, 0x45, 0xCC, 0x89, 0xC9,
	0x03, 0x45, 0xCC, 0x8C, 0xC9, 0x03, 0x45, 0xCC,
	0x8F, 0xC9, 0x03, 0x45, 0xCC, 0x91, 0xC9, 0x03,
	0x45, 0xCC, 0xA8, 0xA5, 0x03, 0x45, 0xCC, 0xAD,
	0xB5, 0x03, 0x45, 0xCC, 0xB0, 0xB5, 0x03, 0x46,
	0xCC, 0x87, 0xC9, 0x03, 0x47, 0xCC, 0x81, 0xC9,
	0x03, 0x47, 0xCC, 0x82, 0xC9, 0x03, 0x47, 0xCC,
	0x84, 0xC9, 0x03, 0x47, 0xCC, 0x86, 0xC9, 0x03,
	// Bytes 3040 - 307f
	0x47, 0xCC, 0x87, 0xC9, 0x03, 0x47, 0xCC, 0x8C,
	0xC9, 0x03, 0x47, 0xCC, 0xA7, 0xA5, 0x03, 0x48,
	0xCC, 0x82, 0xC9, 0x03, 0x48, 0xCC, 0x87, 0xC9,
	0x03, 0x48, 0xCC, 0x88, 0xC9, 0x03, 0x48, 0xCC,
	0x8C, 0xC9, 0x03, 0x48, 0xCC, 0xA3, 0xB5, 0x03,
	0x48, 0xCC, 0xA7, 0xA5, 0x03, 0x48, 0xCC, 0xAE,
	0xB5, 0x03, 0x49, 0xCC, 0x80, 0xC9, 0x03, 0x49,
	0xCC, 0x81, 0xC9, 0x03, 0x49, 0xCC, 0x82, 0xC9,
	// Bytes 3080 - 30bf
	0x03, 0x49, 0xCC, 0x83, 0xC9, 0x03, 0x49, 0xCC,
	0x84, 0xC9, 0x03, 0x49, 0xCC, 0x86, 0xC9, 0x03,
	0x49, 0xCC, 0x87, 0xC9, 0x03, 0x49, 0xCC, 0x89,
	0xC9, 0x03, 0x49, 0xCC, 0x8C, 0xC9, 0x03, 0x49,
	0xCC, 0x8F, 0xC9, 0x03, 0x49, 0xCC, 0x91, 0xC9,
	0x03, 0x49, 0xCC, 0xA3, 0xB5, 0x03, 0x49, 0xCC,
	0xA8, 0xA5, 0x03, 0x49, 0xCC, 0xB0, 0xB5, 0x03,
	0x4A, 0xCC, 0x82, 0xC9, 0x03, 0x4B, 0xCC, 0x81,
	// Bytes 30c0 - 30ff
	0xC9, 0x03, 0x4B, 0xCC, 0x8C, 0xC9, 0x03, 0x4B,
	0xCC, 0xA3, 0xB5, 0x03, 0x4B, 0xCC, 0xA7, 0xA5,
	0x03, 0x4B, 0xCC, 0xB1, 0xB5, 0x03, 0x4C, 0xCC,
	0x81, 0xC9, 0x03, 0x4C, 0xCC, 0x8C, 0xC9, 0x03,
	0x4C, 0xCC, 0xA7, 0xA5, 0x03, 0x4C, 0xCC, 0xAD,
	0xB5, 0x03, 0x4C, 0xCC, 0xB1, 0xB5, 0x03, 0x4D,
	0xCC, 0x81, 0xC9, 0x03, 0x4D, 0xCC, 0x87, 0xC9,
	0x03, 0x4D, 0xCC, 0xA3, 0xB5, 0x03, 0x4E, 0xCC,
	// Bytes 3100 - 313f
	0x80, 0xC9, 0x03, 0x4E, 0xCC, 0x81, 0xC9, 0x03,
	0x4E, 0xCC, 0x83, 0xC9, 0x03, 0x4E, 0xCC, 0x87,
	0xC9, 0x03, 0x4E, 0xCC, 0x8C, 0xC9, 0x03, 0x4E,
	0xCC, 0xA3, 0xB5, 0x03, 0x4E, 0xCC, 0xA7, 0xA5,
	0x03, 0x4E, 0xCC, 0xAD, 0xB5, 0x03, 0x4E, 0xCC,
	0xB1, 0xB5, 0x03, 0x4F, 0xCC, 0x80, 0xC9, 0x03,
	0x4F, 0xCC, 0x81, 0xC9, 0x03, 0x4F, 0xCC, 0x86,
	0xC9, 0x03, 0x4F, 0xCC, 0x89, 0xC9, 0x03, 0x4F,
	// Bytes 3140 - 317f
	0xCC, 0x8B, 0xC9, 0x03, 0x4F, 0xCC, 0x8C, 0xC9,
	0x03, 0x4F, 0xCC, 0x8F, 0xC9, 0x03, 0x4F, 0xCC,
	0x91, 0xC9, 0x03, 0x50, 0xCC, 0x81, 0xC9, 0x03,
	0x50, 0xCC, 0x87, 0xC9, 0x03, 0x52, 0xCC, 0x81,
	0xC9, 0x03, 0x52, 0xCC, 0x87, 0xC9, 0x03, 0x52,
	0xCC, 0x8C, 0xC9, 0x03, 0x52, 0xCC, 0x8F, 0xC9,
	0x03, 0x52, 0xCC, 0x91, 0xC9, 0x03, 0x52, 0xCC,
	0xA7, 0xA5, 0x03, 0x52, 0xCC, 0xB1, 0xB5, 0x03,
	// Bytes 3180 - 31bf
	0x53, 0xCC, 0x82, 0xC9, 0x03, 0x53, 0xCC, 0x87,
	0xC9, 0x03, 0x53, 0xCC, 0xA6, 0xB5, 0x03, 0x53,
	0xCC, 0xA7, 0xA5, 0x03, 0x54, 0xCC, 0x87, 0xC9,
	0x03, 0x54, 0xCC, 0x8C, 0xC9, 0x03, 0x54, 0xCC,
	0xA3, 0xB5, 0x03, 0x54, 0xCC, 0xA6, 0xB5, 0x03,
	0x54, 0xCC, 0xA7, 0xA5, 0x03, 0x54, 0xCC, 0xAD,
	0xB5, 0x03, 0x54, 0xCC, 0xB1, 0xB5, 0x03, 0x55,
	0xCC, 0x80, 0xC9, 0x03, 0x55, 0xCC, 0x81, 0xC9,
	// Bytes 31c0 - 31ff
	0x03, 0x55, 0xCC, 0x82, 0xC9, 0x03, 0x55, 0xCC,
	0x86, 0xC9, 0x03, 0x55, 0xCC, 0x89, 0xC9, 0x03,
	0x55, 0xCC, 0x8A, 0xC9, 0x03, 0x55, 0xCC, 0x8B,
	0xC9, 0x03, 0x55, 0xCC, 0x8C, 0xC9, 0x03, 0x55,
	0xCC, 0x8F, 0xC9, 0x03, 0x55, 0xCC, 0x91, 0xC9,
	0x03, 0x55, 0xCC, 0xA3, 0xB5, 0x03, 0x55, 0xCC,
	0xA4, 0xB5, 0x03, 0x55, 0xCC, 0xA8, 0xA5, 0x03,
	0x55, 0xCC, 0xAD, 0xB5, 0x03, 0x55, 0xCC, 0xB0,
	// Bytes 3200 - 323f
	0xB5, 0x03, 0x56, 0xCC, 0x83, 0xC9, 0x03, 0x56,
	0xCC, 0xA3, 0xB5, 0x03, 0x57, 0xCC, 0x80, 0xC9,
	0x03, 0x57, 0xCC, 0x81, 0xC9, 0x03, 0x57, 0xCC,
	0x82, 0xC9, 0x03, 0x57, 0xCC, 0x87, 0xC9, 0x03,
	0x57, 0xCC, 0x88, 0xC9, 0x03, 0x57, 0xCC, 0xA3,
	0xB5, 0x03, 0x58, 0xCC, 0x87, 0xC9, 0x03, 0x58,
	0xCC, 0x88, 0xC9, 0x03, 0x59, 0xCC, 0x80, 0xC9,
	0x03, 0x59, 0xCC, 0x81, 0xC9, 0x03, 0x59, 0xCC,
	// Bytes 3240 - 327f
	0x82, 0xC9, 0x03, 0x59, 0xCC, 0x83, 0xC9, 0x03,
	0x59, 0xCC, 0x84, 0xC9, 0x03, 0x59, 0xCC, 0x87,
	0xC9, 0x03, 0x59, 0xCC, 0x88, 0xC9, 0x03, 0x59,
	0xCC, 0x89, 0xC9, 0x03, 0x59, 0xCC, 0xA3, 0xB5,
	0x03, 0x5A, 0xCC, 0x81, 0xC9, 0x03, 0x5A, 0xCC,
	0x82, 0xC9, 0x03, 0x5A, 0xCC, 0x87, 0xC9, 0x03,
	0x5A, 0xCC, 0x8C, 0xC9, 0x03, 0x5A, 0xCC, 0xA3,
	0xB5, 0x03, 0x5A, 0xCC, 0xB1, 0xB5, 0x03, 0x61,
	// Bytes 3280 - 32bf
	0xCC, 0x80, 0xC9, 0x03, 0x61, 0xCC, 0x81, 0xC9,
	0x03, 0x61, 0xCC, 0x83, 0xC9, 0x03, 0x61, 0xCC,
	0x84, 0xC9, 0x03, 0x61, 0xCC, 0x89, 0xC9, 0x03,
	0x61, 0xCC, 0x8C, 0xC9, 0x03, 0x61, 0xCC, 0x8F,
	0xC9, 0x03, 0x61, 0xCC, 0x91, 0xC9, 0x03, 0x61,
	0xCC, 0xA5, 0xB5, 0x03, 0x61, 0xCC, 0xA8, 0xA5,
	0x03, 0x62, 0xCC, 0x87, 0xC9, 0x03, 0x62, 0xCC,
	0xA3, 0xB5, 0x03, 0x62, 0xCC, 0xB1, 0xB5, 0x03,
	// Bytes 32c0 - 32ff
	0x63, 0xCC, 0x81, 0xC9, 0x03, 0x63, 0xCC, 0x82,
	0xC9, 0x03, 0x63, 0xCC, 0x87, 0xC9, 0x03, 0x63,
	0xCC, 0x8C, 0xC9, 0x03, 0x64, 0xCC, 0x87, 0xC9,
	0x03, 0x64, 0xCC, 0x8C, 0xC9, 0x03, 0x64, 0xCC,
	0xA3, 0xB5, 0x03, 0x64, 0xCC, 0xA7, 0xA5, 0x03,
	0x64, 0xCC, 0xAD, 0xB5, 0x03, 0x64, 0xCC, 0xB1,
	0xB5, 0x03, 0x65, 0xCC, 0x80, 0xC9, 0x03, 0x65,
	0xCC, 0x81, 0xC9, 0x03, 0x65, 0xCC, 0x83, 0xC9,
	// Bytes 3300 - 333f
	0x03, 0x65, 0xCC, 0x86, 0xC9, 0x03, 0x65, 0xCC,
	0x87, 0xC9, 0x03, 0x65, 0xCC, 0x88, 0xC9, 0x03,
	0x65, 0xCC, 0x89, 0xC9, 0x03, 0x65, 0xCC, 0x8C,
	0xC9, 0x03, 0x65, 0xCC, 0x8F, 0xC9, 0x03, 0x65,
	0xCC, 0x91, 0xC9, 0x03, 0x65, 0xCC, 0xA8, 0xA5,
	0x03, 0x65, 0xCC, 0xAD, 0xB5, 0x03, 0x65, 0xCC,
	0xB0, 0xB5, 0x03, 0x66, 0xCC, 0x87, 0xC9, 0x03,
	0x67, 0xCC, 0x81, 0xC9, 0x03, 0x67, 0xCC, 0x82,
	// Bytes 3340 - 337f
	0xC9, 0x03, 0x67, 0xCC, 0x84, 0xC9, 0x03, 0x67,
	0xCC, 0x86, 0xC9, 0x03, 0x67, 0xCC, 0x87, 0xC9,
	0x03, 0x67, 0xCC, 0x8C, 0xC9, 0x03, 0x67, 0xCC,
	0xA7, 0xA5, 0x03, 0x68, 0xCC, 0x82, 0xC9, 0x03,
	0x68, 0xCC, 0x87, 0xC9, 0x03, 0x68, 0xCC, 0x88,
	0xC9, 0x03, 0x68, 0xCC, 0x8C, 0xC9, 0x03, 0x68,
	0xCC, 0xA3, 0xB5, 0x03, 0x68, 0xCC, 0xA7, 0xA5,
	0x03, 0x68, 0xCC, 0xAE, 0xB5, 0x03, 0x68, 0xCC,
	// Bytes 3380 - 33bf
	0xB1, 0xB5, 0x03, 0x69, 0xCC, 0x80, 0xC9, 0x03,
	0x69, 0xCC, 0x81, 0xC9, 0x03, 0x69, 0xCC, 0x82,
	0xC9, 0x03, 0x69, 0xCC, 0x83, 0xC9, 0x03, 0x69,
	0xCC, 0x84, 0xC9, 0x03, 0x69, 0xCC, 0x86, 0xC9,
	0x03, 0x69, 0xCC, 0x89, 0xC9, 0x03, 0x69, 0xCC,
	0x8C, 0xC9, 0x03, 0x69, 0xCC, 0x8F, 0xC9, 0x03,
	0x69, 0xCC, 0x91, 0xC9, 0x03, 0x69, 0xCC, 0xA3,
	0xB5, 0x03, 0x69, 0xCC, 0xA8, 0xA5, 0x03, 0x69,
	// Bytes 33c0 - 33ff
	0xCC, 0xB0, 0xB5, 0x03, 0x6A, 0xCC, 0x82, 0xC9,
	0x03, 0x6A, 0xCC, 0x8C, 0xC9, 0x03, 0x6B, 0xCC,
	0x81, 0xC9, 0x03, 0x6B, 0xCC, 0x8C, 0xC9, 0x03,
	0x6B, 0xCC, 0xA3, 0xB5, 0x03, 0x6B, 0xCC, 0xA7,
	0xA5, 0x03, 0x6B, 0xCC, 0xB1, 0xB5, 0x03, 0x6C,
	0xCC, 0x81, 0xC9, 0x03, 0x6C, 0xCC, 0x8C, 0xC9,
	0x03, 0x6C, 0xCC, 0xA7, 0xA5, 0x03, 0x6C, 0xCC,
	0xAD, 0xB5, 0x03, 0x6C, 0xCC, 0xB1, 0xB5, 0x03,
	// Bytes 3400 - 343f
	0x6D, 0xCC, 0x81, 0xC9, 0x03, 0x6D, 0xCC, 0x87,
	0xC9, 0x03, 0x6D, 0xCC, 0xA3, 0xB5, 0x03, 0x6E,
	0xCC, 0x80, 0xC9, 0x03, 0x6E, 0xCC, 0x81, 0xC9,
	0x03, 0x6E, 0xCC, 0x83, 0xC9, 0x03, 0x6E, 0xCC,
	0x87, 0xC9, 0x03, 0x6E, 0xCC, 0x8C, 0xC9, 0x03,
	0x6E, 0xCC, 0xA3, 0xB5, 0x03, 0x6E, 0xCC, 0xA7,
	0xA5, 0x03, 0x6E, 0xCC, 0xAD, 0xB5, 0x03, 0x6E,
	0xCC, 0xB1, 0xB5, 0x03, 0x6F, 0xCC, 0x80, 0xC9,
	// Bytes 3440 - 347f
	0x03, 0x6F, 0xCC, 0x81, 0xC9, 0x03, 0x6F, 0xCC,
	0x86, 0xC9, 0x03, 0x6F, 0xCC, 0x89, 0xC9, 0x03,
	0x6F, 0xCC, 0x8B, 0xC9, 0x03, 0x6F, 0xCC, 0x8C,
	0xC9, 0x03, 0x6F, 0xCC, 0x8F, 0xC9, 0x03, 0x6F,
	0xCC, 0x91, 0xC9, 0x03, 0x70, 0xCC, 0x81, 0xC9,
	0x03, 0x70, 0xCC, 0x87, 0xC9, 0x03, 0x72, 0xCC,
	0x81, 0xC9, 0x03, 0x72, 0xCC, 0x87, 0xC9, 0x03,
	0x72, 0xCC, 0x8C, 0xC9, 0x03, 0x72, 0xCC, 0x8F,
	// Bytes 3480 - 34bf
	0xC9, 0x03, 0x72, 0xCC, 0x91, 0xC9, 0x03, 0x72,
	0xCC, 0xA7, 0xA5, 0x03, 0x72, 0xCC, 0xB1, 0xB5,
	0x03, 0x73, 0xCC, 0x82, 0xC9, 0x03, 0x73, 0xCC,
	0x87, 0xC9, 0x03, 0x73, 0xCC, 0xA6, 0xB5, 0x03,
	0x73, 0xCC, 0xA7, 0xA5, 0x03, 0x74, 0xCC, 0x87,
	0xC9, 0x03, 0x74, 0xCC, 0x88, 0xC9, 0x03, 0x74,
	0xCC, 0x8C, 0xC9, 0x03, 0x74, 0xCC, 0xA3, 0xB5,
	0x03, 0x74, 0xCC, 0xA6, 0xB5, 0x03, 0x74, 0xCC,
	// Bytes 34c0 - 34ff
	0xA7, 0xA5, 0x03, 0x74, 0xCC, 0xAD, 0xB5, 0x03,
	0x74, 0xCC, 0xB1, 0xB5, 0x03, 0x75, 0xCC, 0x80,
	0xC9, 0x03, 0x75, 0xCC, 0x81, 0xC9, 0x03, 0x75,
	0xCC, 0x82, 0xC9, 0x03, 0x75, 0xCC, 0x86, 0xC9,
	0x03, 0x75, 0xCC, 0x89, 0xC9, 0x03, 0x75, 0xCC,
	0x8A, 0xC9, 0x03, 0x75, 0xCC, 0x8B, 0xC9, 0x03,
	0x75, 0xCC, 0x8C, 0xC9, 0x03, 0x75, 0xCC, 0x8F,
	0xC9, 0x03, 0x75, 0xCC, 0x91, 0xC9, 0x03, 0x75,
	// Bytes 3500 - 353f
	0xCC, 0xA3, 0xB5, 0x03, 0x75, 0xCC, 0xA4, 0xB5,
	0x03, 0x75, 0xCC, 0xA8, 0xA5, 0x03, 0x75, 0xCC,
	0xAD, 0xB5, 0x03, 0x75, 0xCC, 0xB0, 0xB5, 0x03,
	0x76, 0xCC, 0x83, 0xC9, 0x03, 0x76, 0xCC, 0xA3,
	0xB5, 0x03, 0x77, 0xCC, 0x80, 0xC9, 0x03, 0x77,
	0xCC, 0x81, 0xC9, 0x03, 0x77, 0xCC, 0x82, 0xC9,
	0x03, 0x77, 0xCC, 0x87, 0xC9, 0x03, 0x77, 0xCC,
	0x88, 0xC9, 0x03, 0x77, 0xCC, 0x8A, 0xC9, 0x03,
	// Bytes 3540 - 357f
	0x77, 0xCC, 0xA3, 0xB5, 0x03, 0x78, 0xCC, 0x87,
	0xC9, 0x03, 0x78, 0xCC, 0x88, 0xC9, 0x03, 0x79,
	0xCC, 0x80, 0xC9, 0x03, 0x79, 0xCC, 0x81, 0xC9,
	0x03, 0x79, 0xCC, 0x82, 0xC9, 0x03, 0x79, 0xCC,
	0x83, 0xC9, 0x03, 0x79, 0xCC, 0x84, 0xC9, 0x03,
	0x79, 0xCC, 0x87, 0xC9, 0x03, 0x79, 0xCC, 0x88,
	0xC9, 0x03, 0x79, 0xCC, 0x89, 0xC9, 0x03, 0x79,
	0xCC, 0x8A, 0xC9, 0x03, 0x79, 0xCC, 0xA3, 0xB5,
	// Bytes 3580 - 35bf
	0x03, 0x7A, 0xCC, 0x81, 0xC9, 0x03, 0x7A, 0xCC,
	0x82, 0xC9, 0x03, 0x7A, 0xCC, 0x87, 0xC9, 0x03,
	0x7A, 0xCC, 0x8C, 0xC9, 0x03, 0x7A, 0xCC, 0xA3,
	0xB5, 0x03, 0x7A, 0xCC, 0xB1, 0xB5, 0x04, 0xC2,
	0xA8, 0xCC, 0x80, 0xCA, 0x04, 0xC2, 0xA8, 0xCC,
	0x81, 0xCA, 0x04, 0xC2, 0xA8, 0xCD, 0x82, 0xCA,
	0x04, 0xC3, 0x86, 0xCC, 0x81, 0xC9, 0x04, 0xC3,
	0x86, 0xCC, 0x84, 0xC9, 0x04, 0xC3, 0x98, 0xCC,
	// Bytes 35c0 - 35ff
	0x81, 0xC9, 0x04, 0xC3, 0xA6, 0xCC, 0x81, 0xC9,
	0x04, 0xC3, 0xA6, 0xCC, 0x84, 0xC9, 0x04, 0xC3,
	0xB8, 0xCC, 0x81, 0xC9, 0x04, 0xC5, 0xBF, 0xCC,
	0x87, 0xC9, 0x04, 0xC6, 0xB7, 0xCC, 0x8C, 0xC9,
	0x04, 0xCA, 0x92, 0xCC, 0x8C, 0xC9, 0x04, 0xCE,
	0x91, 0xCC, 0x80, 0xC9, 0x04, 0xCE, 0x91, 0xCC,
	0x81, 0xC9, 0x04, 0xCE, 0x91, 0xCC, 0x84, 0xC9,
	0x04, 0xCE, 0x91, 0xCC, 0x86, 0xC9, 0x04, 0xCE,
	// Bytes 3600 - 363f
	0x91, 0xCD, 0x85, 0xD9, 0x04, 0xCE, 0x95, 0xCC,
	0x80, 0xC9, 0x04, 0xCE, 0x95, 0xCC, 0x81, 0xC9,
	0x04, 0xCE, 0x97, 0xCC, 0x80, 0xC9, 0x04, 0xCE,
	0x97, 0xCC, 0x81, 0xC9, 0x04, 0xCE, 0x97, 0xCD,
	0x85, 0xD9, 0x04, 0xCE, 0x99, 0xCC, 0x80, 0xC9,
	0x04, 0xCE, 0x99, 0xCC, 0x81, 0xC9, 0x04, 0xCE,
	0x99, 0xCC, 0x84, 0xC9, 0x04, 0xCE, 0x99, 0xCC,
	0x86, 0xC9, 0x04, 0xCE, 0x99, 0xCC, 0x88, 0xC9,
	// Bytes 3640 - 367f
	0x04, 0xCE, 0x9F, 0xCC, 0x80, 0xC9, 0x04, 0xCE,
	0x9F, 0xCC, 0x81, 0xC9, 0x04, 0xCE, 0xA1, 0xCC,
	0x94, 0xC9, 0x04, 0xCE, 0xA5, 0xCC, 0x80, 0xC9,
	0x04, 0xCE, 0xA5, 0xCC, 0x81, 0xC9, 0x04, 0xCE,
	0xA5, 0xCC, 0x84, 0xC9, 0x04, 0xCE, 0xA5, 0xCC,
	0x86, 0xC9, 0x04, 0xCE, 0xA5, 0xCC, 0x88, 0xC9,
	0x04, 0xCE, 0xA9, 0xCC, 0x80, 0xC9, 0x04, 0xCE,
	0xA9, 0xCC, 0x81, 0xC9, 0x04, 0xCE, 0xA9, 0xCD,
	// Bytes 3680 - 36bf
	0x85, 0xD9, 0x04, 0xCE, 0xB1, 0xCC, 0x84, 0xC9,
	0x04, 0xCE, 0xB1, 0xCC, 0x86, 0xC9, 0x04, 0xCE,
	0xB1, 0xCD, 0x85, 0xD9, 0x04, 0xCE, 0xB5, 0xCC,
	0x80, 0xC9, 0x04, 0xCE, 0xB5, 0xCC, 0x81, 0xC9,
	0x04, 0xCE, 0xB7, 0xCD, 0x85, 0xD9, 0x04, 0xCE,
	0xB9, 0xCC, 0x80, 0xC9, 0x04, 0xCE, 0xB9, 0xCC,
	0x81, 0xC9, 0x04, 0xCE, 0xB9, 0xCC, 0x84, 0xC9,
	0x04, 0xCE, 0xB9, 0xCC, 0x86, 0xC9, 0x04, 0xCE,
	// Bytes 36c0 - 36ff
	0xB9, 0xCD, 0x82, 0xC9, 0x04, 0xCE, 0xBF, 0xCC,
	0x80, 0xC9, 0x04, 0xCE, 0xBF, 0xCC, 0x81, 0xC9,
	0x04, 0xCF, 0x81, 0xCC, 0x93, 0xC9, 0x04, 0xCF,
	0x81, 0xCC, 0x94, 0xC9, 0x04, 0xCF, 0x85, 0xCC,
	0x80, 0xC9, 0x04, 0xCF, 0x85, 0xCC, 0x81, 0xC9,
	0x04, 0xCF, 0x85, 0xCC, 0x84, 0xC9, 0x04, 0xCF,
	0x85, 0xCC, 0x86, 0xC9, 0x04, 0xCF, 0x85, 0xCD,
	0x82, 0xC9, 0x04, 0xCF, 0x89, 0xCD, 0x85, 0xD9,
	// Bytes 3700 - 373f
	0x04, 0xCF, 0x92, 0xCC, 0x81, 0xC9, 0x04, 0xCF,
	0x92, 0xCC, 0x88, 0xC9, 0x04, 0xD0, 0x86, 0xCC,
	0x88, 0xC9, 0x04, 0xD0, 0x90, 0xCC, 0x86, 0xC9,
	0x04, 0xD0, 0x90, 0xCC, 0x88, 0xC9, 0x04, 0xD0,
	0x93, 0xCC, 0x81, 0xC9, 0x04, 0xD0, 0x95, 0xCC,
	0x80, 0xC9, 0x04, 0xD0, 0x95, 0xCC, 0x86, 0xC9,
	0x04, 0xD0, 0x95, 0xCC, 0x88, 0xC9, 0x04, 0xD0,
	0x96, 0xCC, 0x86, 0xC9, 0x04, 0xD0, 0x96, 0xCC,
	// Bytes 3740 - 377f
	0x88, 0xC9, 0x04, 0xD0, 0x97, 0xCC, 0x88, 0xC9,
	0x04, 0xD0, 0x98, 0xCC, 0x80, 0xC9, 0x04, 0xD0,
	0x98, 0xCC, 0x84, 0xC9, 0x04, 0xD0, 0x98, 0xCC,
	0x86, 0xC9, 0x04, 0xD0, 0x98, 0xCC, 0x88, 0xC9,
	0x04, 0xD0, 0x9A, 0xCC, 0x81, 0xC9, 0x04, 0xD0,
	0x9E, 0xCC, 0x88, 0xC9, 0x04, 0xD0, 0xA3, 0xCC,
	0x84, 0xC9, 0x04, 0xD0, 0xA3, 0xCC, 0x86, 0xC9,
	0x04, 0xD0, 0xA3, 0xCC, 0x88, 0xC9, 0x04, 0xD0,
	// Bytes 3780 - 37bf
	0xA3, 0xCC, 0x8B, 0xC9, 0x04, 0xD0, 0xA7, 0xCC,
	0x88, 0xC9, 0x04, 0xD0, 0xAB, 0xCC, 0x88, 0xC9,
	0x04, 0xD0, 0xAD, 0xCC, 0x88, 0xC9, 0x04, 0xD0,
	0xB0, 0xCC, 0x86, 0xC9, 0x04, 0xD0, 0xB0, 0xCC,
	0x88, 0xC9, 0x04, 0xD0, 0xB3, 0xCC, 0x81, 0xC9,
	0x04, 0xD0, 0xB5, 0xCC, 0x80, 0xC9, 0x04, 0xD0,
	0xB5, 0xCC, 0x86, 0xC9, 0x04, 0xD0, 0xB5, 0xCC,
	0x88, 0xC9, 0x04, 0xD0, 0xB6, 0xCC, 0x86, 0xC9,
	// Bytes 37c0 - 37ff
	0x04, 0xD0, 0xB6, 0xCC, 0x88, 0xC9, 0x04, 0xD0,
	0xB7, 0xCC, 0x88, 0xC9, 0x04, 0xD0, 0xB8, 0xCC,
	0x80, 0xC9, 0x04, 0xD0, 0xB8, 0xCC, 0x84, 0xC9,
	0x04, 0xD0, 0xB8, 0xCC, 0x86, 0xC9, 0x04, 0xD0,
	0xB8, 0xCC, 0x88, 0xC9, 0x04, 0xD0, 0xBA, 0xCC,
	0x81, 0xC9, 0x04, 0xD0, 0xBE, 0xCC, 0x88, 0xC9,
	0x04, 0xD1, 0x83, 0xCC, 0x84, 0xC9, 0x04, 0xD1,
	0x83, 0xCC, 0x86, 0xC9, 0x04, 0xD1, 0x83, 0xCC,
	// Bytes 3800 - 383f
	0x88, 0xC9, 0x04, 0xD1, 0x83, 0xCC, 0x8B, 0xC9,
	0x04, 0xD1, 0x87, 0xCC, 0x88, 0xC9, 0x04, 0xD1,
	0x8B, 0xCC, 0x88, 0xC9, 0x04, 0xD1, 0x8D, 0xCC,
	0x88, 0xC9, 0x04, 0xD1, 0x96, 0xCC, 0x88, 0xC9,
	0x04, 0xD1, 0xB4, 0xCC, 0x8F, 0xC9, 0x04, 0xD1,
	0xB5, 0xCC, 0x8F, 0xC9, 0x04, 0xD3, 0x98, 0xCC,
	0x88, 0xC9, 0x04, 0xD3, 0x99, 0xCC, 0x88, 0xC9,
	0x04, 0xD3, 0xA8, 0xCC, 0x88, 0xC9, 0x04, 0xD3,
	// Bytes 3840 - 387f
	0xA9, 0xCC, 0x88, 0xC9, 0x04, 0xD8, 0xA7, 0xD9,
	0x93, 0xC9, 0x04, 0xD8, 0xA7, 0xD9, 0x94, 0xC9,
	0x04, 0xD8, 0xA7, 0xD9, 0x95, 0xB5, 0x04, 0xD9,
	0x88, 0xD9, 0x94, 0xC9, 0x04, 0xD9, 0x8A, 0xD9,
	0x94, 0xC9, 0x04, 0xDB, 0x81, 0xD9, 0x94, 0xC9,
	0x04, 0xDB, 0x92, 0xD9, 0x94, 0xC9, 0x04, 0xDB,
	0x95, 0xD9, 0x94, 0xC9, 0x05, 0x41, 0xCC, 0x82,
	0xCC, 0x80, 0xCA, 0x05, 0x41, 0xCC, 0x82, 0xCC,
	// Bytes 3880 - 38bf
	0x81, 0xCA, 0x05, 0x41, 0xCC, 0x82, 0xCC, 0x83,
	0xCA, 0x05, 0x41, 0xCC, 0x82, 0xCC, 0x89, 0xCA,
	0x05, 0x41, 0xCC, 0x86, 0xCC, 0x80, 0xCA, 0x05,
	0x41, 0xCC, 0x86, 0xCC, 0x81, 0xCA, 0x05, 0x41,
	0xCC, 0x86, 0xCC, 0x83, 0xCA, 0x05, 0x41, 0xCC,
	0x86, 0xCC, 0x89, 0xCA, 0x05, 0x41, 0xCC, 0x87,
	0xCC, 0x84, 0xCA, 0x05, 0x41, 0xCC, 0x88, 0xCC,
	0x84, 0xCA, 0x05, 0x41, 0xCC, 0x8A, 0xCC, 0x81,
	// Bytes 38c0 - 38ff
	0xCA, 0x05, 0x41, 0xCC, 0xA3, 0xCC, 0x82, 0xCA,
	0x05, 0x41, 0xCC, 0xA3, 0xCC, 0x86, 0xCA, 0x05,
	0x43, 0xCC, 0xA7, 0xCC, 0x81, 0xCA, 0x05, 0x45,
	0xCC, 0x82, 0xCC, 0x80, 0xCA, 0x05, 0x45, 0xCC,
	0x82, 0xCC, 0x81, 0xCA, 0x05, 0x45, 0xCC, 0x82,
	0xCC, 0x83, 0xCA, 0x05, 0x45, 0xCC, 0x82, 0xCC,
	0x89, 0xCA, 0x05, 0x45, 0xCC, 0x84, 0xCC, 0x80,
	0xCA, 0x05, 0x45, 0xCC, 0x84, 0xCC, 0x81, 0xCA,
	// Bytes 3900 - 393f
	0x05, 0x45, 0xCC, 0xA3, 0xCC, 0x82, 0xCA, 0x05,
	0x45, 0xCC, 0xA7, 0xCC, 0x86, 0xCA, 0x05, 0x49,
	0xCC, 0x88, 0xCC, 0x81, 0xCA, 0x05, 0x4C, 0xCC,
	0xA3, 0xCC, 0x84, 0xCA, 0x05, 0x4F, 0xCC, 0x82,
	0xCC, 0x80, 0xCA, 0x05, 0x4F, 0xCC, 0x82, 0xCC,
	0x81, 0xCA, 0x05, 0x4F, 0xCC, 0x82, 0xCC, 0x83,
	0xCA, 0x05, 0x4F, 0xCC, 0x82, 0xCC, 0x89, 0xCA,
	0x05, 0x4F, 0xCC, 0x83, 0xCC, 0x81, 0xCA, 0x05,
	// Bytes 3940 - 397f
	0x4F, 0xCC, 0x83, 0xCC, 0x84, 0xCA, 0x05, 0x4F,
	0xCC, 0x83, 0xCC, 0x88, 0xCA, 0x05, 0x4F, 0xCC,
	0x84, 0xCC, 0x80, 0xCA, 0x05, 0x4F, 0xCC, 0x84,
	0xCC, 0x81, 0xCA, 0x05, 0x4F, 0xCC, 0x87, 0xCC,
	0x84, 0xCA, 0x05, 0x4F, 0xCC, 0x88, 0xCC, 0x84,
	0xCA, 0x05, 0x4F, 0xCC, 0x9B, 0xCC, 0x80, 0xCA,
	0x05, 0x4F, 0xCC, 0x9B, 0xCC, 0x81, 0xCA, 0x05,
	0x4F, 0xCC, 0x9B, 0xCC, 0x83, 0xCA, 0x05, 0x4F,
	// Bytes 3980 - 39bf
	0xCC, 0x9B, 0xCC, 0x89, 0xCA, 0x05, 0x4F, 0xCC,
	0x9B, 0xCC, 0xA3, 0xB6, 0x05, 0x4F, 0xCC, 0xA3,
	0xCC, 0x82, 0xCA, 0x05, 0x4F, 0xCC, 0xA8, 0xCC,
	0x84, 0xCA, 0x05, 0x52, 0xCC, 0xA3, 0xCC, 0x84,
	0xCA, 0x05, 0x53, 0xCC, 0x81, 0xCC, 0x87, 0xCA,
	0x05, 0x53, 0xCC, 0x8C, 0xCC, 0x87, 0xCA, 0x05,
	0x53, 0xCC, 0xA3, 0xCC, 0x87, 0xCA, 0x05, 0x55,
	0xCC, 0x83, 0xCC, 0x81, 0xCA, 0x05, 0x55, 0xCC,
	// Bytes 39c0 - 39ff
	0x84, 0xCC, 0x88, 0xCA, 0x05, 0x55, 0xCC, 0x88,
	0xCC, 0x80, 0xCA, 0x05, 0x55, 0xCC, 0x88, 0xCC,
	0x81, 0xCA, 0x05, 0x55, 0xCC, 0x88, 0xCC, 0x84,
	0xCA, 0x05, 0x55, 0xCC, 0x88, 0xCC, 0x8C, 0xCA,
	0x05, 0x55, 0xCC, 0x9B, 0xCC, 0x80, 0xCA, 0x05,
	0x55, 0xCC, 0x9B, 0xCC, 0x81, 0xCA, 0x05, 0x55,
	0xCC, 0x9B, 0xCC, 0x83, 0xCA, 0x05, 0x55, 0xCC,
	0x9B, 0xCC, 0x89, 0xCA, 0x05, 0x55, 0xCC, 0x9B,
	// Bytes 3a00 - 3a3f
	0xCC, 0xA3, 0xB6, 0x05, 0x61, 0xCC, 0x82, 0xCC,
	0x80, 0xCA, 0x05, 0x61, 0xCC, 0x82, 0xCC, 0x81,
	0xCA, 0x05, 0x61, 0xCC, 0x82, 0xCC, 0x83, 0xCA,
	0x05, 0x61, 0xCC, 0x82, 0xCC, 0x89, 0xCA, 0x05,
	0x61, 0xCC, 0x86, 0xCC, 0x80, 0xCA, 0x05, 0x61,
	0xCC, 0x86, 0xCC, 0x81, 0xCA, 0x05, 0x61, 0xCC,
	0x86, 0xCC, 0x83, 0xCA, 0x05, 0x61, 0xCC, 0x86,
	0xCC, 0x89, 0xCA, 0x05, 0x61, 0xCC, 0x87, 0xCC,
	// Bytes 3a40 - 3a7f
	0x84, 0xCA, 0x05, 0x61, 0xCC, 0x88, 0xCC, 0x84,
	0xCA, 0x05, 0x61, 0xCC, 0x8A, 0xCC, 0x81, 0xCA,
	0x05, 0x61, 0xCC, 0xA3, 0xCC, 0x82, 0xCA, 0x05,
	0x61, 0xCC, 0xA3, 0xCC, 0x86, 0xCA, 0x05, 0x63,
	0xCC, 0xA7, 0xCC, 0x81, 0xCA, 0x05, 0x65, 0xCC,
	0x82, 0xCC, 0x80, 0xCA, 0x05, 0x65, 0xCC, 0x82,
	0xCC, 0x81, 0xCA, 0x05, 0x65, 0xCC, 0x82, 0xCC,
	0x83, 0xCA, 0x05, 0x65, 0xCC, 0x82, 0xCC, 0x89,
	// Bytes 3a80 - 3abf
	0xCA, 0x05, 0x65, 0xCC, 0x84, 0xCC, 0x80, 0xCA,
	0x05, 0x65, 0xCC, 0x84, 0xCC, 0x81, 0xCA, 0x05,
	0x65, 0xCC, 0xA3, 0xCC, 0x82, 0xCA, 0x05, 0x65,
	0xCC, 0xA7, 0xCC, 0x86, 0xCA, 0x05, 0x69, 0xCC,
	0x88, 0xCC, 0x81, 0xCA, 0x05, 0x6C, 0xCC, 0xA3,
	0xCC, 0x84, 0xCA, 0x05, 0x6F, 0xCC, 0x82, 0xCC,
	0x80, 0xCA, 0x05, 0x6F, 0xCC, 0x82, 0xCC, 0x81,
	0xCA, 0x05, 0x6F, 0xCC, 0x82, 0xCC, 0x83, 0xCA,
	// Bytes 3ac0 - 3aff
	0x05, 0x6F, 0xCC, 0x82, 0xCC, 0x89, 0xCA, 0x05,
	0x6F, 0xCC, 0x83, 0xCC, 0x81, 0xCA, 0x05, 0x6F,
	0xCC, 0x83, 0xCC, 0x84, 0xCA, 0x05, 0x6F, 0xCC,
	0x83, 0xCC, 0x88, 0xCA, 0x05, 0x6F, 0xCC, 0x84,
	0xCC, 0x80, 0xCA, 0x05, 0x6F, 0xCC, 0x84, 0xCC,
	0x81, 0xCA, 0x05, 0x6F, 0xCC, 0x87, 0xCC, 0x84,
	0xCA, 0x05, 0x6F, 0xCC, 0x88, 0xCC, 0x84, 0xCA,
	0x05, 0x6F, 0xCC, 0x9B, 0xCC, 0x80, 0xCA, 0x05,
	// Bytes 3b00 - 3b3f
	0x6F, 0xCC, 0x9B, 0xCC, 0x81, 0xCA, 0x05, 0x6F,
	0xCC, 0x9B, 0xCC, 0x83, 0xCA, 0x05, 0x6F, 0xCC,
	0x9B, 0xCC, 0x89, 0xCA, 0x05, 0x6F, 0xCC, 0x9B,
	0xCC, 0xA3, 0xB6, 0x05, 0x6F, 0xCC, 0xA3, 0xCC,
	0x82, 0xCA, 0x05, 0x6F, 0xCC, 0xA8, 0xCC, 0x84,
	0xCA, 0x05, 0x72, 0xCC, 0xA3, 0xCC, 0x84, 0xCA,
	0x05, 0x73, 0xCC, 0x81, 0xCC, 0x87, 0xCA, 0x05,
	0x73, 0xCC, 0x8C, 0xCC, 0x87, 0xCA, 0x05, 0x73,
	// Bytes 3b40 - 3b7f
	0xCC, 0xA3, 0xCC, 0x87, 0xCA, 0x05, 0x75, 0xCC,
	0x83, 0xCC, 0x81, 0xCA, 0x05, 0x75, 0xCC, 0x84,
	0xCC, 0x88, 0xCA, 0x05, 0x75, 0xCC, 0x88, 0xCC,
	0x80, 0xCA, 0x05, 0x75, 0xCC, 0x88, 0xCC, 0x81,
	0xCA, 0x05, 0x75, 0xCC, 0x88, 0xCC, 0x84, 0xCA,
	0x05, 0x75, 0xCC, 0x88, 0xCC, 0x8C, 0xCA, 0x05,
	0x75, 0xCC, 0x9B, 0xCC, 0x80, 0xCA, 0x05, 0x75,
	0xCC, 0x9B, 0xCC, 0x81, 0xCA, 0x05, 0x75, 0xCC,
	// Bytes 3b80 - 3bbf
	0x9B, 0xCC, 0x83, 0xCA, 0x05, 0x75, 0xCC, 0x9B,
	0xCC, 0x89, 0xCA, 0x05, 0x75, 0xCC, 0x9B, 0xCC,
	0xA3, 0xB6, 0x05, 0xE1, 0xBE, 0xBF, 0xCC, 0x80,
	0xCA, 0x05, 0xE1, 0xBE, 0xBF, 0xCC, 0x81, 0xCA,
	0x05, 0xE1, 0xBE, 0xBF, 0xCD, 0x82, 0xCA, 0x05,
	0xE1, 0xBF, 0xBE, 0xCC, 0x80, 0xCA, 0x05, 0xE1,
	0xBF, 0xBE, 0xCC, 0x81, 0xCA, 0x05, 0xE1, 0xBF,
	0xBE, 0xCD, 0x82, 0xCA, 0x05, 0xE2, 0x86, 0x90,
	// Bytes 3bc0 - 3bff
	0xCC, 0xB8, 0x05, 0x05, 0xE2, 0x86, 0x92, 0xCC,
	0xB8, 0x05, 0x05, 0xE2, 0x86, 0x94, 0xCC, 0xB8,
	0x05, 0x05, 0xE2, 0x87, 0x90, 0xCC, 0xB8, 0x05,
	0x05, 0xE2, 0x87, 0x92, 0xCC, 0xB8, 0x05, 0x05,
	0xE2, 0x87, 0x94, 0xCC, 0xB8, 0x05, 0x05, 0xE2,
	0x88, 0x83, 0xCC, 0xB8, 0x05, 0x05, 0xE2, 0x88,
	0x88, 0xCC, 0xB8, 0x05, 0x05, 0xE2, 0x88, 0x8B,
	0xCC, 0xB8, 0x05, 0x05, 0xE2, 0x88, 0xA3, 0xCC,
	// Bytes 3c00 - 3c3f
	0xB8, 0x05, 0x05, 0xE2, 0x88, 0xA5, 0xCC, 0xB8,
	0x05, 0x05, 0xE2, 0x88, 0xBC, 0xCC, 0xB8, 0x05,
	0x05, 0xE2, 0x89, 0x83, 0xCC, 0xB8, 0x05, 0x05,
	0xE2, 0x89, 0x85, 0xCC, 0xB8, 0x05, 0x05, 0xE2,
	0x89, 0x88, 0xCC, 0xB8, 0x05, 0x05, 0xE2, 0x89,
	0x8D, 0xCC, 0xB8, 0x05, 0x05, 0xE2, 0x89, 0xA1,
	0xCC, 0xB8, 0x05, 0x05, 0xE2, 0x89, 0xA4, 0xCC,
	0xB8, 0x05, 0x05, 0xE2, 0x89, 0xA5, 0xCC, 0xB8,
	// Bytes 3c40 - 3c7f
	0x05, 0x05, 0xE2, 0x89, 0xB2, 0xCC, 0xB8, 0x05,
	0x05, 0xE2, 0x89, 0xB3, 0xCC, 0xB8, 0x05, 0x05,
	0xE2, 0x89, 0xB6, 0xCC, 0xB8, 0x05, 0x05, 0xE2,
	0x89, 0xB7, 0xCC, 0xB8, 0x05, 0x05, 0xE2, 0x89,
	0xBA, 0xCC, 0xB8, 0x05, 0x05, 0xE2, 0x89, 0xBB,
	0xCC, 0xB8, 0x05, 0x05, 0xE2, 0x89, 0xBC, 0xCC,
	0xB8, 0x05, 0x05, 0xE2, 0x89, 0xBD, 0xCC, 0xB8,
	0x05, 0x05, 0xE2, 0x8A, 0x82, 0xCC, 0xB8, 0x05,
	// Bytes 3c80 - 3cbf
	0x05, 0xE2, 0x8A, 0x83, 0xCC, 0xB8, 0x05, 0x05,
	0xE2, 0x8A, 0x86, 0xCC, 0xB8, 0x05, 0x05, 0xE2,
	0x8A, 0x87, 0xCC, 0xB8, 0x05, 0x05, 0xE2, 0x8A,
	0x91, 0xCC, 0xB8, 0x05, 0x05, 0xE2, 0x8A, 0x92,
	0xCC, 0xB8, 0x05, 0x05, 0xE2, 0x8A, 0xA2, 0xCC,
	0xB8, 0x05, 0x05, 0xE2, 0x8A, 0xA8, 0xCC, 0xB8,
	0x05, 0x05, 0xE2, 0x8A, 0xA9, 0xCC, 0xB8, 0x05,
	0x05, 0xE2, 0x8A, 0xAB, 0xCC, 0xB8, 0x05, 0x05,
	// Bytes 3cc0 - 3cff
	0xE2, 0x8A, 0xB2, 0xCC, 0xB8, 0x05, 0x05, 0xE2,
	0x8A, 0xB3, 0xCC, 0xB8, 0x05, 0x05, 0xE2, 0x8A,
	0xB4, 0xCC, 0xB8, 0x05, 0x05, 0xE2, 0x8A, 0xB5,
	0xCC, 0xB8, 0x05, 0x06, 0xCE, 0x91, 0xCC, 0x93,
	0xCD, 0x85, 0xDA, 0x06, 0xCE, 0x91, 0xCC, 0x94,
	0xCD, 0x85, 0xDA, 0x06, 0xCE, 0x95, 0xCC, 0x93,
	0xCC, 0x80, 0xCA, 0x06, 0xCE, 0x95, 0xCC, 0x93,
	0xCC, 0x81, 0xCA, 0x06, 0xCE, 0x95, 0xCC, 0x94,
	// Bytes 3d00 - 3d3f
	0xCC, 0x80, 0xCA, 0x06, 0xCE, 0x95, 0xCC, 0x94,
	0xCC, 0x81, 0xCA, 0x06, 0xCE, 0x97, 0xCC, 0x93,
	0xCD, 0x85, 0xDA, 0x06, 0xCE, 0x97, 0xCC, 0x94,
	0xCD, 0x85, 0xDA, 0x06, 0xCE, 0x99, 0xCC, 0x93,
	0xCC, 0x80, 0xCA, 0x06, 0xCE, 0x99, 0xCC, 0x93,
	0xCC, 0x81, 0xCA, 0x06, 0xCE, 0x99, 0xCC, 0x93,
	0xCD, 0x82, 0xCA, 0x06, 0xCE, 0x99, 0xCC, 0x94,
	0xCC, 0x80, 0xCA, 0x06, 0xCE, 0x99, 0xCC, 0x94,
	// Bytes 3d40 - 3d7f
	0xCC, 0x81, 0xCA, 0x06, 0xCE, 0x99, 0xCC, 0x94,
	0xCD, 0x82, 0xCA, 0x06, 0xCE, 0x9F, 0xCC, 0x93,
	0xCC, 0x80, 0xCA, 0x06, 0xCE, 0x9F, 0xCC, 0x93,
	0xCC, 0x81, 0xCA, 0x06, 0xCE, 0x9F, 0xCC, 0x94,
	0xCC, 0x80, 0xCA, 0x06, 0xCE, 0x9F, 0xCC, 0x94,
	0xCC, 0x81, 0xCA, 0x06, 0xCE, 0xA5, 0xCC, 0x94,
	0xCC, 0x80, 0xCA, 0x06, 0xCE, 0xA5, 0xCC, 0x94,
	0xCC, 0x81, 0xCA, 0x06, 0xCE, 0xA5, 0xCC, 0x94,
	// Bytes 3d80 - 3dbf
	0xCD, 0x82, 0xCA, 0x06, 0xCE, 0xA9, 0xCC, 0x93,
	0xCD, 0x85, 0xDA, 0x06, 0xCE, 0xA9, 0xCC, 0x94,
	0xCD, 0x85, 0xDA, 0x06, 0xCE, 0xB1, 0xCC, 0x80,
	0xCD, 0x85, 0xDA, 0x06, 0xCE, 0xB1, 0xCC, 0x81,
	0xCD, 0x85, 0xDA, 0x06, 0xCE, 0xB1, 0xCC, 0x93,
	0xCD, 0x85, 0xDA, 0x06, 0xCE, 0xB1, 0xCC, 0x94,
	0xCD, 0x85, 0xDA, 0x06, 0xCE, 0xB1, 0xCD, 0x82,
	0xCD, 0x85, 0xDA, 0x06, 0xCE, 0xB5, 0xCC, 0x93,
	// Bytes 3dc0 - 3dff
	0xCC, 0x80, 0xCA, 0x06, 0xCE, 0xB5, 0xCC, 0x93,
	0xCC, 0x81, 0xCA, 0x06, 0xCE, 0xB5, 0xCC, 0x94,
	0xCC, 0x80, 0xCA, 0x06, 0xCE, 0xB5, 0xCC, 0x94,
	0xCC, 0x81, 0xCA, 0x06, 0xCE, 0xB7, 0xCC, 0x80,
	0xCD, 0x85, 0xDA, 0x06, 0xCE, 0xB7, 0xCC, 0x81,
	0xCD, 0x85, 0xDA, 0x06, 0xCE, 0xB7, 0xCC, 0x93,
	0xCD, 0x85, 0xDA, 0x06, 0xCE, 0xB7, 0xCC, 0x94,
	0xCD, 0x85, 0xDA, 0x06, 0xCE, 0xB7, 0xCD, 0x82,
	// Bytes 3e00 - 3e3f
	0xCD, 0x85, 0xDA, 0x06, 0xCE, 0xB9, 0xCC, 0x88,
	0xCC, 0x80, 0xCA, 0x06, 0xCE, 0xB9, 0xCC, 0x88,
	0xCC, 0x81, 0xCA, 0x06, 0xCE, 0xB9, 0xCC, 0x88,
	0xCD, 0x82, 0xCA, 0x06, 0xCE, 0xB9, 0xCC, 0x93,
	0xCC, 0x80, 0xCA, 0x06, 0xCE, 0xB9, 0xCC, 0x93,
	0xCC, 0x81, 0xCA, 0x06, 0xCE, 0xB9, 0xCC, 0x93,
	0xCD, 0x82, 0xCA, 0x06, 0xCE, 0xB9, 0xCC, 0x94,
	0xCC, 0x80, 0xCA, 0x06, 0xCE, 0xB9, 0xCC, 0x94,
	// Bytes 3e40 - 3e7f
	0xCC, 0x81, 0xCA, 0x06, 0xCE, 0xB9, 0xCC, 0x94,
	0xCD, 0x82, 0xCA, 0x06, 0xCE, 0xBF, 0xCC, 0x93,
	0xCC, 0x80, 0xCA, 0x06, 0xCE, 0xBF, 0xCC, 0x93,
	0xCC, 0x81, 0xCA, 0x06, 0xCE, 0xBF, 0xCC, 0x94,
	0xCC, 0x80, 0xCA, 0x06, 0xCE, 0xBF, 0xCC, 0x94,
	0xCC, 0x81, 0xCA, 0x06, 0xCF, 0x85, 0xCC, 0x88,
	0xCC, 0x80, 0xCA, 0x06, 0xCF, 0x85, 0xCC, 0x88,
	0xCC, 0x81, 0xCA, 0x06, 0xCF, 0x85, 0xCC, 0x88,
	// Bytes 3e80 - 3ebf
	0xCD, 0x82, 0xCA, 0x06, 0xCF, 0x85, 0xCC, 0x93,
	0xCC, 0x80, 0xCA, 0x06, 0xCF, 0x85, 0xCC, 0x93,
	0xCC, 0x81, 0xCA, 0x06, 0xCF, 0x85, 0xCC, 0x93,
	0xCD, 0x82, 0xCA, 0x06, 0xCF, 0x85, 0xCC, 0x94,
	0xCC, 0x80, 0xCA, 0x06, 0xCF, 0x85, 0xCC, 0x94,
	0xCC, 0x81, 0xCA, 0x06, 0xCF, 0x85, 0xCC, 0x94,
	0xCD, 0x82, 0xCA, 0x06, 0xCF, 0x89, 0xCC, 0x80,
	0xCD, 0x85, 0xDA, 0x06, 0xCF, 0x89, 0xCC, 0x81,
	// Bytes 3ec0 - 3eff
	0xCD, 0x85, 0xDA, 0x06, 0xCF, 0x89, 0xCC, 0x93,
	0xCD, 0x85, 0xDA, 0x06, 0xCF, 0x89, 0xCC, 0x94,
	0xCD, 0x85, 0xDA, 0x06, 0xCF, 0x89, 0xCD, 0x82,
	0xCD, 0x85, 0xDA, 0x06, 0xE0, 0xA4, 0xA8, 0xE0,
	0xA4, 0xBC, 0x09, 0x06, 0xE0, 0xA4, 0xB0, 0xE0,
	0xA4, 0xBC, 0x09, 0x06, 0xE0, 0xA4, 0xB3, 0xE0,
	0xA4, 0xBC, 0x09, 0x06, 0xE0, 0xB1, 0x86, 0xE0,
	0xB1, 0x96, 0x85, 0x06, 0xE0, 0xB7, 0x99, 0xE0,
	// Bytes 3f00 - 3f3f
	0xB7, 0x8A, 0x11, 0x06, 0xE3, 0x81, 0x86, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x81, 0x8B, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x81, 0x8D, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x81, 0x8F, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x81, 0x91, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x81, 0x93, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x81, 0x95, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x81, 0x97, 0xE3,
	// Bytes 3f40 - 3f7f
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x81, 0x99, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x81, 0x9B, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x81, 0x9D, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x81, 0x9F, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x81, 0xA1, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x81, 0xA4, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x81, 0xA6, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x81, 0xA8, 0xE3,
	// Bytes 3f80 - 3fbf
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x81, 0xAF, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x81, 0xAF, 0xE3,
	0x82, 0x9A, 0x0D, 0x06, 0xE3, 0x81, 0xB2, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x81, 0xB2, 0xE3,
	0x82, 0x9A, 0x0D, 0x06, 0xE3, 0x81, 0xB5, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x81, 0xB5, 0xE3,
	0x82, 0x9A, 0x0D, 0x06, 0xE3, 0x81, 0xB8, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x81, 0xB8, 0xE3,
	// Bytes 3fc0 - 3fff
	0x82, 0x9A, 0x0D, 0x06, 0xE3, 0x81, 0xBB, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x81, 0xBB, 0xE3,
	0x82, 0x9A, 0x0D, 0x06, 0xE3, 0x82, 0x9D, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x82, 0xA6, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x82, 0xAB, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x82, 0xAD, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x82, 0xAF, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x82, 0xB1, 0xE3,
	// Bytes 4000 - 403f
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x82, 0xB3, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x82, 0xB5, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x82, 0xB7, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x82, 0xB9, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x82, 0xBB, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x82, 0xBD, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x82, 0xBF, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x83, 0x81, 0xE3,
	// Bytes 4040 - 407f
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x83, 0x84, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x83, 0x86, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x83, 0x88, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x83, 0x8F, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x83, 0x8F, 0xE3,
	0x82, 0x9A, 0x0D, 0x06, 0xE3, 0x83, 0x92, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x83, 0x92, 0xE3,
	0x82, 0x9A, 0x0D, 0x06, 0xE3, 0x83, 0x95, 0xE3,
	// Bytes 4080 - 40bf
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x83, 0x95, 0xE3,
	0x82, 0x9A, 0x0D, 0x06, 0xE3, 0x83, 0x98, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x83, 0x98, 0xE3,
	0x82, 0x9A, 0x0D, 0x06, 0xE3, 0x83, 0x9B, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x83, 0x9B, 0xE3,
	0x82, 0x9A, 0x0D, 0x06, 0xE3, 0x83, 0xAF, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x83, 0xB0, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x83, 0xB1, 0xE3,
	// Bytes 40c0 - 40ff
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x83, 0xB2, 0xE3,
	0x82, 0x99, 0x0D, 0x06, 0xE3, 0x83, 0xBD, 0xE3,
	0x82, 0x99, 0x0D, 0x08, 0xCE, 0x91, 0xCC, 0x93,
	0xCC, 0x80, 0xCD, 0x85, 0xDB, 0x08, 0xCE, 0x91,
	0xCC, 0x93, 0xCC, 0x81, 0xCD, 0x85, 0xDB, 0x08,
	0xCE, 0x91, 0xCC, 0x93, 0xCD, 0x82, 0xCD, 0x85,
	0xDB, 0x08, 0xCE, 0x91, 0xCC, 0x94, 0xCC, 0x80,
	0xCD, 0x85, 0xDB, 0x08, 0xCE, 0x91, 0xCC, 0x94,
	// Bytes 4100 - 413f
	0xCC, 0x81, 0xCD, 0x85, 0xDB, 0x08, 0xCE, 0x91,
	0xCC, 0x94, 0xCD, 0x82, 0xCD, 0x85, 0xDB, 0x08,
	0xCE, 0x97, 0xCC, 0x93, 0xCC, 0x80, 0xCD, 0x85,
	0xDB, 0x08, 0xCE, 0x97, 0xCC, 0x93, 0xCC, 0x81,
	0xCD, 0x85, 0xDB, 0x08, 0xCE, 0x97, 0xCC, 0x93,
	0xCD, 0x82, 0xCD, 0x85, 0xDB, 0x08, 0xCE, 0x97,
	0xCC, 0x94, 0xCC, 0x80, 0xCD, 0x85, 0xDB, 0x08,
	0xCE, 0x97, 0xCC, 0x94, 0xCC, 0x81, 0xCD, 0x85,
	// Bytes 4140 - 417f
	0xDB, 0x08, 0xCE, 0x97, 0xCC, 0x94, 0xCD, 0x82,
	0xCD, 0x85, 0xDB, 0x08, 0xCE, 0xA9, 0xCC, 0x93,
	0xCC, 0x80, 0xCD, 0x85, 0xDB, 0x08, 0xCE, 0xA9,
	0xCC, 0x93, 0xCC, 0x81, 0xCD, 0x85, 0xDB, 0x08,
	0xCE, 0xA9, 0xCC, 0x93, 0xCD, 0x82, 0xCD, 0x85,
	0xDB, 0x08, 0xCE, 0xA9, 0xCC, 0x94, 0xCC, 0x80,
	0xCD, 0x85, 0xDB, 0x08, 0xCE, 0xA9, 0xCC, 0x94,
	0xCC, 0x81, 0xCD, 0x85, 0xDB, 0x08, 0xCE, 0xA9,
	// Bytes 4180 - 41bf
	0xCC, 0x94, 0xCD, 0x82, 0xCD, 0x85, 0xDB, 0x08,
	0xCE, 0xB1, 0xCC, 0x93, 0xCC, 0x80, 0xCD, 0x85,
	0xDB, 0x08, 0xCE, 0xB1, 0xCC, 0x93, 0xCC, 0x81,
	0xCD, 0x85, 0xDB, 0x08, 0xCE, 0xB1, 0xCC, 0x93,
	0xCD, 0x82, 0xCD, 0x85, 0xDB, 0x08, 0xCE, 0xB1,
	0xCC, 0x94, 0xCC, 0x80, 0xCD, 0x85, 0xDB, 0x08,
	0xCE, 0xB1, 0xCC, 0x94, 0xCC, 0x81, 0xCD, 0x85,
	0xDB, 0x08, 0xCE, 0xB1, 0xCC, 0x94, 0xCD, 0x82,
	// Bytes 41c0 - 41ff
	0xCD, 0x85, 0xDB, 0x08, 0xCE, 0xB7, 0xCC, 0x93,
	0xCC, 0x80, 0xCD, 0x85, 0xDB, 0x08, 0xCE, 0xB7,
	0xCC, 0x93, 0xCC, 0x81, 0xCD, 0x85, 0xDB, 0x08,
	0xCE, 0xB7, 0xCC, 0x93, 0xCD, 0x82, 0xCD, 0x85,
	0xDB, 0x08, 0xCE, 0xB7, 0xCC, 0x94, 0xCC, 0x80,
	0xCD, 0x85, 0xDB, 0x08, 0xCE, 0xB7, 0xCC, 0x94,
	0xCC, 0x81, 0xCD, 0x85, 0xDB, 0x08, 0xCE, 0xB7,
	0xCC, 0x94, 0xCD, 0x82, 0xCD, 0x85, 0xDB, 0x08,
	// Bytes 4200 - 423f
	0xCF, 0x89, 0xCC, 0x93, 0xCC, 0x80, 0xCD, 0x85,
	0xDB, 0x08, 0xCF, 0x89, 0xCC, 0x93, 0xCC, 0x81,
	0xCD, 0x85, 0xDB, 0x08, 0xCF, 0x89, 0xCC, 0x93,
	0xCD, 0x82, 0xCD, 0x85, 0xDB, 0x08, 0xCF, 0x89,
	0xCC, 0x94, 0xCC, 0x80, 0xCD, 0x85, 0xDB, 0x08,
	0xCF, 0x89, 0xCC, 0x94, 0xCC, 0x81, 0xCD, 0x85,
	0xDB, 0x08, 0xCF, 0x89, 0xCC, 0x94, 0xCD, 0x82,
	0xCD, 0x85, 0xDB, 0x08, 0xF0, 0x91, 0x82, 0x99,
	// Bytes 4240 - 427f
	0xF0, 0x91, 0x82, 0xBA, 0x09, 0x08, 0xF0, 0x91,
	0x82, 0x9B, 0xF0, 0x91, 0x82, 0xBA, 0x09, 0x08,
	0xF0, 0x91, 0x82, 0xA5, 0xF0, 0x91, 0x82, 0xBA,
	0x09, 0x42, 0xC2, 0xB4, 0x01, 0x43, 0x20, 0xCC,
	0x81, 0xC9, 0x43, 0x20, 0xCC, 0x83, 0xC9, 0x43,
	0x20, 0xCC, 0x84, 0xC9, 0x43, 0x20, 0xCC, 0x85,
	0xC9, 0x43, 0x20, 0xCC, 0x86, 0xC9, 0x43, 0x20,
	0xCC, 0x87, 0xC9, 0x43, 0x20, 0xCC, 0x88, 0xC9,
	// Bytes 4280 - 42bf
	0x43, 0x20, 0xCC, 0x8A, 0xC9, 0x43, 0x20, 0xCC,
	0x8B, 0xC9, 0x43, 0x20, 0xCC, 0x93, 0xC9, 0x43,
	0x20, 0xCC, 0x94, 0xC9, 0x43, 0x20, 0xCC, 0xA7,
	0xA5, 0x43, 0x20, 0xCC, 0xA8, 0xA5, 0x43, 0x20,
	0xCC, 0xB3, 0xB5, 0x43, 0x20, 0xCD, 0x82, 0xC9,
	0x43, 0x20, 0xCD, 0x85, 0xD9, 0x43, 0x20, 0xD9,
	0x8B, 0x59, 0x43, 0x20, 0xD9, 0x8C, 0x5D, 0x43,
	0x20, 0xD9, 0x8D, 0x61, 0x43, 0x20, 0xD9, 0x8E,
	// Bytes 42c0 - 42ff
	0x65, 0x43, 0x20, 0xD9, 0x8F, 0x69, 0x43, 0x20,
	0xD9, 0x90, 0x6D, 0x43, 0x20, 0xD9, 0x91, 0x71,
	0x43, 0x20, 0xD9, 0x92, 0x75, 0x43, 0x41, 0xCC,
	0x8A, 0xC9, 0x43, 0x73, 0xCC, 0x87, 0xC9, 0x44,
	0x20, 0xE3, 0x82, 0x99, 0x0D, 0x44, 0x20, 0xE3,
	0x82, 0x9A, 0x0D, 0x44, 0xC2, 0xA8, 0xCC, 0x81,
	0xCA, 0x44, 0xCE, 0x91, 0xCC, 0x81, 0xC9, 0x44,
	0xCE, 0x95, 0xCC, 0x81, 0xC9, 0x44, 0xCE, 0x97,
	// Bytes 4300 - 433f
	0xCC, 0x81, 0xC9, 0x44, 0xCE, 0x99, 0xCC, 0x81,
	0xC9, 0x44, 0xCE, 0x9F, 0xCC, 0x81, 0xC9, 0x44,
	0xCE, 0xA5, 0xCC, 0x81, 0xC9, 0x44, 0xCE, 0xA5,
	0xCC, 0x88, 0xC9, 0x44, 0xCE, 0xA9, 0xCC, 0x81,
	0xC9, 0x44, 0xCE, 0xB1, 0xCC, 0x81, 0xC9, 0x44,
	0xCE, 0xB5, 0xCC, 0x81, 0xC9, 0x44, 0xCE, 0xB7,
	0xCC, 0x81, 0xC9, 0x44, 0xCE, 0xB9, 0xCC, 0x81,
	0xC9, 0x44, 0xCE, 0xBF, 0xCC, 0x81, 0xC9, 0x44,
	// Bytes 4340 - 437f
	0xCF, 0x85, 0xCC, 0x81, 0xC9, 0x44, 0xCF, 0x89,
	0xCC, 0x81, 0xC9, 0x44, 0xD7, 0x90, 0xD6, 0xB7,
	0x31, 0x44, 0xD7, 0x90, 0xD6, 0xB8, 0x35, 0x44,
	0xD7, 0x90, 0xD6, 0xBC, 0x41, 0x44, 0xD7, 0x91,
	0xD6, 0xBC, 0x41, 0x44, 0xD7, 0x91, 0xD6, 0xBF,
	0x49, 0x44, 0xD7, 0x92, 0xD6, 0xBC, 0x41, 0x44,
	0xD7, 0x93, 0xD6, 0xBC, 0x41, 0x44, 0xD7, 0x94,
	0xD6, 0xBC, 0x41, 0x44, 0xD7, 0x95, 0xD6, 0xB9,
	// Bytes 4380 - 43bf
	0x39, 0x44, 0xD7, 0x95, 0xD6, 0xBC, 0x41, 0x44,
	0xD7, 0x96, 0xD6, 0xBC, 0x41, 0x44, 0xD7, 0x98,
	0xD6, 0xBC, 0x41, 0x44, 0xD7, 0x99, 0xD6, 0xB4,
	0x25, 0x44, 0xD7, 0x99, 0xD6, 0xBC, 0x41, 0x44,
	0xD7, 0x9A, 0xD6, 0xBC, 0x41, 0x44, 0xD7, 0x9B,
	0xD6, 0xBC, 0x41, 0x44, 0xD7, 0x9B, 0xD6, 0xBF,
	0x49, 0x44, 0xD7, 0x9C, 0xD6, 0xBC, 0x41, 0x44,
	0xD7, 0x9E, 0xD6, 0xBC, 0x41, 0x44, 0xD7, 0xA0,
	// Bytes 43c0 - 43ff
	0xD6, 0xBC, 0x41, 0x44, 0xD7, 0xA1, 0xD6, 0xBC,
	0x41, 0x44, 0xD7, 0xA3, 0xD6, 0xBC, 0x41, 0x44,
	0xD7, 0xA4, 0xD6, 0xBC, 0x41, 0x44, 0xD7, 0xA4,
	0xD6, 0xBF, 0x49, 0x44, 0xD7, 0xA6, 0xD6, 0xBC,
	0x41, 0x44, 0xD7, 0xA7, 0xD6, 0xBC, 0x41, 0x44,
	0xD7, 0xA8, 0xD6, 0xBC, 0x41, 0x44, 0xD7, 0xA9,
	0xD6, 0xBC, 0x41, 0x44, 0xD7, 0xA9, 0xD7, 0x81,
	0x4D, 0x44, 0xD7, 0xA9, 0xD7, 0x82, 0x51, 0x44,
	// Bytes 4400 - 443f
	0xD7, 0xAA, 0xD6, 0xBC, 0x41, 0x44, 0xD7, 0xB2,
	0xD6, 0xB7, 0x31, 0x44, 0xD8, 0xA7, 0xD9, 0x8B,
	0x59, 0x44, 0xD8, 0xA7, 0xD9, 0x93, 0xC9, 0x44,
	0xD8, 0xA7, 0xD9, 0x94, 0xC9, 0x44, 0xD8, 0xA7,
	0xD9, 0x95, 0xB5, 0x44, 0xD8, 0xB0, 0xD9, 0xB0,
	0x79, 0x44, 0xD8, 0xB1, 0xD9, 0xB0, 0x79, 0x44,
	0xD9, 0x80, 0xD9, 0x8B, 0x59, 0x44, 0xD9, 0x80,
	0xD9, 0x8E, 0x65, 0x44, 0xD9, 0x80, 0xD9, 0x8F,
	// Bytes 4440 - 447f
	0x69, 0x44, 0xD9, 0x80, 0xD9, 0x90, 0x6D, 0x44,
	0xD9, 0x80, 0xD9, 0x91, 0x71, 0x44, 0xD9, 0x80,
	0xD9, 0x92, 0x75, 0x44, 0xD9, 0x87, 0xD9, 0xB0,
	0x79, 0x44, 0xD9, 0x88, 0xD9, 0x94, 0xC9, 0x44,
	0xD9, 0x89, 0xD9, 0xB0, 0x79, 0x44, 0xD9, 0x8A,
	0xD9, 0x94, 0xC9, 0x44, 0xDB, 0x92, 0xD9, 0x94,
	0xC9, 0x44, 0xDB, 0x95, 0xD9, 0x94, 0xC9, 0x45,
	0x20, 0xCC, 0x88, 0xCC, 0x80, 0xCA, 0x45, 0x20,
	// Bytes 4480 - 44bf
	0xCC, 0x88, 0xCC, 0x81, 0xCA, 0x45, 0x20, 0xCC,
	0x88, 0xCD, 0x82, 0xCA, 0x45, 0x20, 0xCC, 0x93,
	0xCC, 0x80, 0xCA, 0x45, 0x20, 0xCC, 0x93, 0xCC,
	0x81, 0xCA, 0x45, 0x20, 0xCC, 0x93, 0xCD, 0x82,
	0xCA, 0x45, 0x20, 0xCC, 0x94, 0xCC, 0x80, 0xCA,
	0x45, 0x20, 0xCC, 0x94, 0xCC, 0x81, 0xCA, 0x45,
	0x20, 0xCC, 0x94, 0xCD, 0x82, 0xCA, 0x45, 0x20,
	0xD9, 0x8C, 0xD9, 0x91, 0x72, 0x45, 0x20, 0xD9,
	// Bytes 44c0 - 44ff
	0x8D, 0xD9, 0x91, 0x72, 0x45, 0x20, 0xD9, 0x8E,
	0xD9, 0x91, 0x72, 0x45, 0x20, 0xD9, 0x8F, 0xD9,
	0x91, 0x72, 0x45, 0x20, 0xD9, 0x90, 0xD9, 0x91,
	0x72, 0x45, 0x20, 0xD9, 0x91, 0xD9, 0xB0, 0x7A,
	0x45, 0xE2, 0xAB, 0x9D, 0xCC, 0xB8, 0x05, 0x46,
	0xCE, 0xB9, 0xCC, 0x88, 0xCC, 0x81, 0xCA, 0x46,
	0xCF, 0x85, 0xCC, 0x88, 0xCC, 0x81, 0xCA, 0x46,
	0xD7, 0xA9, 0xD6, 0xBC, 0xD7, 0x81, 0x4E, 0x46,
	// Bytes 4500 - 453f
	0xD7, 0xA9, 0xD6, 0xBC, 0xD7, 0x82, 0x52, 0x46,
	0xD9, 0x80, 0xD9, 0x8E, 0xD9, 0x91, 0x72, 0x46,
	0xD9, 0x80, 0xD9, 0x8F, 0xD9, 0x91, 0x72, 0x46,
	0xD9, 0x80, 0xD9, 0x90, 0xD9, 0x91, 0x72, 0x46,
	0xE0, 0xA4, 0x95, 0xE0, 0xA4, 0xBC, 0x09, 0x46,
	0xE0, 0xA4, 0x96, 0xE0, 0xA4, 0xBC, 0x09, 0x46,
	0xE0, 0xA4, 0x97, 0xE0, 0xA4, 0xBC, 0x09, 0x46,
	0xE0, 0xA4, 0x9C, 0xE0, 0xA4, 0xBC, 0x09, 0x46,
	// Bytes 4540 - 457f
	0xE0, 0xA4, 0xA1, 0xE0, 0xA4, 0xBC, 0x09, 0x46,
	0xE0, 0xA4, 0xA2, 0xE0, 0xA4, 0xBC, 0x09, 0x46,
	0xE0, 0xA4, 0xAB, 0xE0, 0xA4, 0xBC, 0x09, 0x46,
	0xE0, 0xA4, 0xAF, 0xE0, 0xA4, 0xBC, 0x09, 0x46,
	0xE0, 0xA6, 0xA1, 0xE0, 0xA6, 0xBC, 0x09, 0x46,
	0xE0, 0xA6, 0xA2, 0xE0, 0xA6, 0xBC, 0x09, 0x46,
	0xE0, 0xA6, 0xAF, 0xE0, 0xA6, 0xBC, 0x09, 0x46,
	0xE0, 0xA8, 0x96, 0xE0, 0xA8, 0xBC, 0x09, 0x46,
	// Bytes 4580 - 45bf
	0xE0, 0xA8, 0x97, 0xE0, 0xA8, 0xBC, 0x09, 0x46,
	0xE0, 0xA8, 0x9C, 0xE0, 0xA8, 0xBC, 0x09, 0x46,
	0xE0, 0xA8, 0xAB, 0xE0, 0xA8, 0xBC, 0x09, 0x46,
	0xE0, 0xA8, 0xB2, 0xE0, 0xA8, 0xBC, 0x09, 0x46,
	0xE0, 0xA8, 0xB8, 0xE0, 0xA8, 0xBC, 0x09, 0x46,
	0xE0, 0xAC, 0xA1, 0xE0, 0xAC, 0xBC, 0x09, 0x46,
	0xE0, 0xAC, 0xA2, 0xE0, 0xAC, 0xBC, 0x09, 0x46,
	0xE0, 0xBE, 0xB2, 0xE0, 0xBE, 0x80, 0x9D, 0x46,
	// Bytes 45c0 - 45ff
	0xE0, 0xBE, 0xB3, 0xE0, 0xBE, 0x80, 0x9D, 0x46,
	0xE3, 0x83, 0x86, 0xE3, 0x82, 0x99, 0x0D, 0x48,
	0xF0, 0x9D, 0x85, 0x97, 0xF0, 0x9D, 0x85, 0xA5,
	0xAD, 0x48, 0xF0, 0x9D, 0x85, 0x98, 0xF0, 0x9D,
	0x85, 0xA5, 0xAD, 0x48, 0xF0, 0x9D, 0x86, 0xB9,
	0xF0, 0x9D, 0x85, 0xA5, 0xAD, 0x48, 0xF0, 0x9D,
	0x86, 0xBA, 0xF0, 0x9D, 0x85, 0xA5, 0xAD, 0x49,
	0xE0, 0xBE, 0xB2, 0xE0, 0xBD, 0xB1, 0xE0, 0xBE,
	// Bytes 4600 - 463f
	0x80, 0x9E, 0x49, 0xE0, 0xBE, 0xB3, 0xE0, 0xBD,
	0xB1, 0xE0, 0xBE, 0x80, 0x9E, 0x4C, 0xF0, 0x9D,
	0x85, 0x98, 0xF0, 0x9D, 0x85, 0xA5, 0xF0, 0x9D,
	0x85, 0xAE, 0xAE, 0x4C, 0xF0, 0x9D, 0x85, 0x98,
	0xF0, 0x9D, 0x85, 0xA5, 0xF0, 0x9D, 0x85, 0xAF,
	0xAE, 0x4C, 0xF0, 0x9D, 0x85, 0x98, 0xF0, 0x9D,
	0x85, 0xA5, 0xF0, 0x9D, 0x85, 0xB0, 0xAE, 0x4C,
	0xF0, 0x9D, 0x85, 0x98, 0xF0, 0x9D, 0x85, 0xA5,
	// Bytes 4640 - 467f
	0xF0, 0x9D, 0x85, 0xB1, 0xAE, 0x4C, 0xF0, 0x9D,
	0x85, 0x98, 0xF0, 0x9D, 0x85, 0xA5, 0xF0, 0x9D,
	0x85, 0xB2, 0xAE, 0x4C, 0xF0, 0x9D, 0x86, 0xB9,
	0xF0, 0x9D, 0x85, 0xA5, 0xF0, 0x9D, 0x85, 0xAE,
	0xAE, 0x4C, 0xF0, 0x9D, 0x86, 0xB9, 0xF0, 0x9D,
	0x85, 0xA5, 0xF0, 0x9D, 0x85, 0xAF, 0xAE, 0x4C,
	0xF0, 0x9D, 0x86, 0xBA, 0xF0, 0x9D, 0x85, 0xA5,
	0xF0, 0x9D, 0x85, 0xAE, 0xAE, 0x4C, 0xF0, 0x9D,
	// Bytes 4680 - 46bf
	0x86, 0xBA, 0xF0, 0x9D, 0x85, 0xA5, 0xF0, 0x9D,
	0x85, 0xAF, 0xAE, 0x83, 0x41, 0xCC, 0x82, 0xC9,
	0x83, 0x41, 0xCC, 0x86, 0xC9, 0x83, 0x41, 0xCC,
	0x87, 0xC9, 0x83, 0x41, 0xCC, 0x88, 0xC9, 0x83,
	0x41, 0xCC, 0x8A, 0xC9, 0x83, 0x41, 0xCC, 0xA3,
	0xB5, 0x83, 0x43, 0xCC, 0xA7, 0xA5, 0x83, 0x45,
	0xCC, 0x82, 0xC9, 0x83, 0x45, 0xCC, 0x84, 0xC9,
	0x83, 0x45, 0xCC, 0xA3, 0xB5, 0x83, 0x45, 0xCC,
	// Bytes 46c0 - 46ff
	0xA7, 0xA5, 0x83, 0x49, 0xCC, 0x88, 0xC9, 0x83,
	0x4C, 0xCC, 0xA3, 0xB5, 0x83, 0x4F, 0xCC, 0x82,
	0xC9, 0x83, 0x4F, 0xCC, 0x83, 0xC9, 0x83, 0x4F,
	0xCC, 0x84, 0xC9, 0x83, 0x4F, 0xCC, 0x87, 0xC9,
	0x83, 0x4F, 0xCC, 0x88, 0xC9, 0x83, 0x4F, 0xCC,
	0x9B, 0xAD, 0x83, 0x4F, 0xCC, 0xA3, 0xB5, 0x83,
	0x4F, 0xCC, 0xA8, 0xA5, 0x83, 0x52, 0xCC, 0xA3,
	0xB5, 0x83, 0x53, 0xCC, 0x81, 0xC9, 0x83, 0x53,
	// Bytes 4700 - 473f
	0xCC, 0x8C, 0xC9, 0x83, 0x53, 0xCC, 0xA3, 0xB5,
	0x83, 0x55, 0xCC, 0x83, 0xC9, 0x83, 0x55, 0xCC,
	0x84, 0xC9, 0x83, 0x55, 0xCC, 0x88, 0xC9, 0x83,
	0x55, 0xCC, 0x9B, 0xAD, 0x83, 0x61, 0xCC, 0x82,
	0xC9, 0x83, 0x61, 0xCC, 0x86, 0xC9, 0x83, 0x61,
	0xCC, 0x87, 0xC9, 0x83, 0x61, 0xCC, 0x88, 0xC9,
	0x83, 0x61, 0xCC, 0x8A, 0xC9, 0x83, 0x61, 0xCC,
	0xA3, 0xB5, 0x83, 0x63, 0xCC, 0xA7, 0xA5, 0x83,
	// Bytes 4740 - 477f
	0x65, 0xCC, 0x82, 0xC9, 0x83, 0x65, 0xCC, 0x84,
	0xC9, 0x83, 0x65, 0xCC, 0xA3, 0xB5, 0x83, 0x65,
	0xCC, 0xA7, 0xA5, 0x83, 0x69, 0xCC, 0x88, 0xC9,
	0x83, 0x6C, 0xCC, 0xA3, 0xB5, 0x83, 0x6F, 0xCC,
	0x82, 0xC9, 0x83, 0x6F, 0xCC, 0x83, 0xC9, 0x83,
	0x6F, 0xCC, 0x84, 0xC9, 0x83, 0x6F, 0xCC, 0x87,
	0xC9, 0x83, 0x6F, 0xCC, 0x88, 0xC9, 0x83, 0x6F,
	0xCC, 0x9B, 0xAD, 0x83, 0x6F, 0xCC, 0xA3, 0xB5,
	// Bytes 4780 - 47bf
	0x83, 0x6F, 0xCC, 0xA8, 0xA5, 0x83, 0x72, 0xCC,
	0xA3, 0xB5, 0x83, 0x73, 0xCC, 0x81, 0xC9, 0x83,
	0x73, 0xCC, 0x8C, 0xC9, 0x83, 0x73, 0xCC, 0xA3,
	0xB5, 0x83, 0x75, 0xCC, 0x83, 0xC9, 0x83, 0x75,
	0xCC, 0x84, 0xC9, 0x83, 0x75, 0xCC, 0x88, 0xC9,
	0x83, 0x75, 0xCC, 0x9B, 0xAD, 0x84, 0xCE, 0x91,
	0xCC, 0x93, 0xC9, 0x84, 0xCE, 0x91, 0xCC, 0x94,
	0xC9, 0x84, 0xCE, 0x95, 0xCC, 0x93, 0xC9, 0x84,
	// Bytes 47c0 - 47ff
	0xCE, 0x95, 0xCC, 0x94, 0xC9, 0x84, 0xCE, 0x97,
	0xCC, 0x93, 0xC9, 0x84, 0xCE, 0x97, 0xCC, 0x94,
	0xC9, 0x84, 0xCE, 0x99, 0xCC, 0x93, 0xC9, 0x84,
	0xCE, 0x99, 0xCC, 0x94, 0xC9, 0x84, 0xCE, 0x9F,
	0xCC, 0x93, 0xC9, 0x84, 0xCE, 0x9F, 0xCC, 0x94,
	0xC9, 0x84, 0xCE, 0xA5, 0xCC, 0x94, 0xC9, 0x84,
	0xCE, 0xA9, 0xCC, 0x93, 0xC9, 0x84, 0xCE, 0xA9,
	0xCC, 0x94, 0xC9, 0x84, 0xCE, 0xB1, 0xCC, 0x80,
	// Bytes 4800 - 483f
	0xC9, 0x84, 0xCE, 0xB1, 0xCC, 0x81, 0xC9, 0x84,
	0xCE, 0xB1, 0xCC, 0x93, 0xC9, 0x84, 0xCE, 0xB1,
	0xCC, 0x94, 0xC9, 0x84, 0xCE, 0xB1, 0xCD, 0x82,
	0xC9, 0x84, 0xCE, 0xB5, 0xCC, 0x93, 0xC9, 0x84,
	0xCE, 0xB5, 0xCC, 0x94, 0xC9, 0x84, 0xCE, 0xB7,
	0xCC, 0x80, 0xC9, 0x84, 0xCE, 0xB7, 0xCC, 0x81,
	0xC9, 0x84, 0xCE, 0xB7, 0xCC, 0x93, 0xC9, 0x84,
	0xCE, 0xB7, 0xCC, 0x94, 0xC9, 0x84, 0xCE, 0xB7,
	// Bytes 4840 - 487f
	0xCD, 0x82, 0xC9, 0x84, 0xCE, 0xB9, 0xCC, 0x88,
	0xC9, 0x84, 0xCE, 0xB9, 0xCC, 0x93, 0xC9, 0x84,
	0xCE, 0xB9, 0xCC, 0x94, 0xC9, 0x84, 0xCE, 0xBF,
	0xCC, 0x93, 0xC9, 0x84, 0xCE, 0xBF, 0xCC, 0x94,
	0xC9, 0x84, 0xCF, 0x85, 0xCC, 0x88, 0xC9, 0x84,
	0xCF, 0x85, 0xCC, 0x93, 0xC9, 0x84, 0xCF, 0x85,
	0xCC, 0x94, 0xC9, 0x84, 0xCF, 0x89, 0xCC, 0x80,
	0xC9, 0x84, 0xCF, 0x89, 0xCC, 0x81, 0xC9, 0x84,
	// Bytes 4880 - 48bf
	0xCF, 0x89, 0xCC, 0x93, 0xC9, 0x84, 0xCF, 0x89,
	0xCC, 0x94, 0xC9, 0x84, 0xCF, 0x89, 0xCD, 0x82,
	0xC9, 0x86, 0xCE, 0x91, 0xCC, 0x93, 0xCC, 0x80,
	0xCA, 0x86, 0xCE, 0x91, 0xCC, 0x93, 0xCC, 0x81,
	0xCA, 0x86, 0xCE, 0x91, 0xCC, 0x93, 0xCD, 0x82,
	0xCA, 0x86, 0xCE, 0x91, 0xCC, 0x94, 0xCC, 0x80,
	0xCA, 0x86, 0xCE, 0x91, 0xCC, 0x94, 0xCC, 0x81,
	0xCA, 0x86, 0xCE, 0x91, 0xCC, 0x94, 0xCD, 0x82,
	// Bytes 48c0 - 48ff
	0xCA, 0x86, 0xCE, 0x97, 0xCC, 0x93, 0xCC, 0x80,
	0xCA, 0x86, 0xCE, 0x97, 0xCC, 0x93, 0xCC, 0x81,
	0xCA, 0x86, 0xCE, 0x97, 0xCC, 0x93, 0xCD, 0x82,
	0xCA, 0x86, 0xCE, 0x97, 0xCC, 0x94, 0xCC, 0x80,
	0xCA, 0x86, 0xCE, 0x97, 0xCC, 0x94, 0xCC, 0x81,
	0xCA, 0x86, 0xCE, 0x97, 0xCC, 0x94, 0xCD, 0x82,
	0xCA, 0x86, 0xCE, 0xA9, 0xCC, 0x93, 0xCC, 0x80,
	0xCA, 0x86, 0xCE, 0xA9, 0xCC, 0x93, 0xCC, 0x81,
	// Bytes 4900 - 493f
	0xCA, 0x86, 0xCE, 0xA9, 0xCC, 0x93, 0xCD, 0x82,
	0xCA, 0x86, 0xCE, 0xA9, 0xCC, 0x94, 0xCC, 0x80,
	0xCA, 0x86, 0xCE, 0xA9, 0xCC, 0x94, 0xCC, 0x81,
	0xCA, 0x86, 0xCE, 0xA9, 0xCC, 0x94, 0xCD, 0x82,
	0xCA, 0x86, 0xCE, 0xB1, 0xCC, 0x93, 0xCC, 0x80,
	0xCA, 0x86, 0xCE, 0xB1, 0xCC, 0x93, 0xCC, 0x81,
	0xCA, 0x86, 0xCE, 0xB1, 0xCC, 0x93, 0xCD, 0x82,
	0xCA, 0x86, 0xCE, 0xB1, 0xCC, 0x94, 0xCC, 0x80,
	// Bytes 4940 - 497f
	0xCA, 0x86, 0xCE, 0xB1, 0xCC, 0x94, 0xCC, 0x81,
	0xCA, 0x86, 0xCE, 0xB1, 0xCC, 0x94, 0xCD, 0x82,
	0xCA, 0x86, 0xCE, 0xB7, 0xCC, 0x93, 0xCC, 0x80,
	0xCA, 0x86, 0xCE, 0xB7, 0xCC, 0x93, 0xCC, 0x81,
	0xCA, 0x86, 0xCE, 0xB7, 0xCC, 0x93, 0xCD, 0x82,
	0xCA, 0x86, 0xCE, 0xB7, 0xCC, 0x94, 0xCC, 0x80,
	0xCA, 0x86, 0xCE, 0xB7, 0xCC, 0x94, 0xCC, 0x81,
	0xCA, 0x86, 0xCE, 0xB7, 0xCC, 0x94, 0xCD, 0x82,
	// Bytes 4980 - 49bf
	0xCA, 0x86, 0xCF, 0x89, 0xCC, 0x93, 0xCC, 0x80,
	0xCA, 0x86, 0xCF, 0x89, 0xCC, 0x93, 0xCC, 0x81,
	0xCA, 0x86, 0xCF, 0x89, 0xCC, 0x93, 0xCD, 0x82,
	0xCA, 0x86, 0xCF, 0x89, 0xCC, 0x94, 0xCC, 0x80,
	0xCA, 0x86, 0xCF, 0x89, 0xCC, 0x94, 0xCC, 0x81,
	0xCA, 0x86, 0xCF, 0x89, 0xCC, 0x94, 0xCD, 0x82,
	0xCA, 0x42, 0xCC, 0x80, 0xC9, 0x32, 0x42, 0xCC,
	0x81, 0xC9, 0x32, 0x42, 0xCC, 0x93, 0xC9, 0x32,
	// Bytes 49c0 - 49ff
	0x43, 0xE1, 0x85, 0xA1, 0x01, 0x00, 0x43, 0xE1,
	0x85, 0xA2, 0x01, 0x00, 0x43, 0xE1, 0x85, 0xA3,
	0x01, 0x00, 0x43, 0xE1, 0x85, 0xA4, 0x01, 0x00,
	0x43, 0xE1, 0x85, 0xA5, 0x01, 0x00, 0x43, 0xE1,
	0x85, 0xA6, 0x01, 0x00, 0x43, 0xE1, 0x85, 0xA7,
	0x01, 0x00, 0x43, 0xE1, 0x85, 0xA8, 0x01, 0x00,
	0x43, 0xE1, 0x85, 0xA9, 0x01, 0x00, 0x43, 0xE1,
	0x85, 0xAA, 0x01, 0x00, 0x43, 0xE1, 0x85, 0xAB,
	// Bytes 4a00 - 4a3f
	0x01, 0x00, 0x43, 0xE1, 0x85, 0xAC, 0x01, 0x00,
	0x43, 0xE1, 0x85, 0xAD, 0x01, 0x00, 0x43, 0xE1,
	0x85, 0xAE, 0x01, 0x00, 0x43, 0xE1, 0x85, 0xAF,
	0x01, 0x00, 0x43, 0xE1, 0x85, 0xB0, 0x01, 0x00,
	0x43, 0xE1, 0x85, 0xB1, 0x01, 0x00, 0x43, 0xE1,
	0x85, 0xB2, 0x01, 0x00, 0x43, 0xE1, 0x85, 0xB3,
	0x01, 0x00, 0x43, 0xE1, 0x85, 0xB4, 0x01, 0x00,
	0x43, 0xE1, 0x85, 0xB5, 0x01, 0x00, 0x43, 0xE1,
	// Bytes 4a40 - 4a7f
	0x86, 0xAA, 0x01, 0x00, 0x43, 0xE1, 0x86, 0xAC,
	0x01, 0x00, 0x43, 0xE1, 0x86, 0xAD, 0x01, 0x00,
	0x43, 0xE1, 0x86, 0xB0, 0x01, 0x00, 0x43, 0xE1,
	0x86, 0xB1, 0x01, 0x00, 0x43, 0xE1, 0x86, 0xB2,
	0x01, 0x00, 0x43, 0xE1, 0x86, 0xB3, 0x01, 0x00,
	0x43, 0xE1, 0x86, 0xB4, 0x01, 0x00, 0x43, 0xE1,
	0x86, 0xB5, 0x01, 0x00, 0x44, 0xCC, 0x88, 0xCC,
	0x81, 0xCA, 0x32, 0x43, 0xE3, 0x82, 0x99, 0x0D,
	// Bytes 4a80 - 4abf
	0x03, 0x43, 0xE3, 0x82, 0x9A, 0x0D, 0x03, 0x46,
	0xE0, 0xBD, 0xB1, 0xE0, 0xBD, 0xB2, 0x9E, 0x26,
	0x46, 0xE0, 0xBD, 0xB1, 0xE0, 0xBD, 0xB4, 0xA2,
	0x26, 0x46, 0xE0, 0xBD, 0xB1, 0xE0, 0xBE, 0x80,
	0x9E, 0x26, 0x00, 0x01,
}

// lookup returns the trie value for the first UTF-8 encoding in s and
// the width in bytes of this encoding. The size will be 0 if s does not
// hold enough bytes to complete the encoding. len(s) must be greater than 0.
func (t *nfcTrie) lookup(s []byte) (v uint16, sz int) {
	c0 := s[0]
	switch {
	case c0 < 0x80: // is ASCII
		return nfcValues[c0], 1
	case c0 < 0xC2:
		return 0, 1 // Illegal UTF-8: not a starter, not ASCII.
	case c0 < 0xE0: // 2-byte UTF-8
		if len(s) < 2 {
			return 0, 0
		}
		i := nfcIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c1), 2
	case c0 < 0xF0: // 3-byte UTF-8
		if len(s) < 3 {
			return 0, 0
		}
		i := nfcIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = nfcIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 2 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c2), 3
	case c0 < 0xF8: // 4-byte UTF-8
		if len(s) < 4 {
			return 0, 0
		}
		i := nfcIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = nfcIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 2 // Illegal UTF-8: not a continuation byte.
		}
		o = uint32(i)<<6 + uint32(c2)
		i = nfcIndex[o]
		c3 := s[3]
		if c3 < 0x80 || 0xC0 <= c3 {
			return 0, 3 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c3), 4
	}
	// Illegal rune
	return 0, 1
}

// lookupUnsafe returns the trie value for the first UTF-8 encoding in s.
// s must start with a full and valid UTF-8 encoded rune.
func (t *nfcTrie) lookupUnsafe(s []byte) uint16 {
	c0 := s[0]
	if c0 < 0x80 { // is ASCII
		return nfcValues[c0]
	}
	i := nfcIndex[c0]
	if c0 < 0xE0 { // 2-byte UTF-8
		return t.lookupValue(uint32(i), s[1])
	}
	i = nfcIndex[uint32(i)<<6+uint32(s[1])]
	if c0 < 0xF0 { // 3-byte UTF-8
		return t.lookupValue(uint32(i), s[2])
	}
	i = nfcIndex[uint32(i)<<6+uint32(s[2])]
	if c0 < 0xF8 { // 4-byte UTF-8
		return t.lookupValue(uint32(i), s[3])
	}
	return 0
}

// lookupString returns the trie value for the first UTF-8 encoding in s and
// the width in bytes of this encoding. The size will be 0 if s does not
// hold enough bytes to complete the encoding. len(s) must be greater than 0.
func (t *nfcTrie) lookupString(s string) (v uint16, sz int) {
	c0 := s[0]
	switch {
	case c0 < 0x80: // is ASCII
		return nfcValues[c0], 1
	case c0 < 0xC2:
		return 0, 1 // Illegal UTF-8: not a starter, not ASCII.
	case c0 < 0xE0: // 2-byte UTF-8
		if len(s) < 2 {
			return 0, 0
		}
		i := nfcIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c1), 2
	case c0 < 0xF0: // 3-byte UTF-8
		if len(s) < 3 {
			return 0, 0
		}
		i := nfcIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = nfcIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 2 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c2), 3
	case c0 < 0xF8: // 4-byte UTF-8
		if len(s) < 4 {
			return 0, 0
		}
		i := nfcIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = nfcIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 2 // Illegal UTF-8: not a continuation byte.
		}
		o = uint32(i)<<6 + uint32(c2)
		i = nfcIndex[o]
		c3 := s[3]
		if c3 < 0x80 || 0xC0 <= c3 {
			return 0, 3 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c3), 4
	}
	// Illegal rune
	return 0, 1
}

// lookupStringUnsafe returns the trie value for the first UTF-8 encoding in s.
// s must start with a full and valid UTF-8 encoded rune.
func (t *nfcTrie) lookupStringUnsafe(s string) uint16 {
	c0 := s[0]
	if c0 < 0x80 { // is ASCII
		return nfcValues[c0]
	}
	i := nfcIndex[c0]
	if c0 < 0xE0 { // 2-byte UTF-8
		return t.lookupValue(uint32(i), s[1])
	}
	i = nfcIndex[uint32(i)<<6+uint32(s[1])]
	if c0 < 0xF0 { // 3-byte UTF-8
		return t.lookupValue(uint32(i), s[2])
	}
	i = nfcIndex[uint32(i)<<6+uint32(s[2])]
	if c0 < 0xF8 { // 4-byte UTF-8
		return t.lookupValue(uint32(i), s[3])
	}
	return 0
}

// nfcTrie. Total size: 10610 bytes (10.36 KiB). Checksum: 95e8869a9f81e5e6.
type nfcTrie struct{}

func newNfcTrie(i int) *nfcTrie {
	return &nfcTrie{}
}

// lookupValue determines the type of block n and looks up the value for b.
func (t *nfcTrie) lookupValue(n uint32, b byte) uint16 {
	switch {
	case n < 46:
		return uint16(nfcValues[n<<6+uint32(b)])
	default:
		n -= 46
		return uint16(nfcSparse.lookup(n, b))
	}
}

// nfcValues: 48 blocks, 3072 entries, 6144 bytes
// The third block is the zero block.
var nfcValues = [3072]uint16{
	// Block 0x0, offset 0x0
	0x3c: 0xa000, 0x3d: 0xa000, 0x3e: 0xa000,
	// Block 0x1, offset 0x40
	0x41: 0xa000, 0x42: 0xa000, 0x43: 0xa000, 0x44: 0xa000, 0x45: 0xa000,
	0x46: 0xa000, 0x47: 0xa000, 0x48: 0xa000, 0x49: 0xa000, 0x4a: 0xa000, 0x4b: 0xa000,
	0x4c: 0xa000, 0x4d: 0xa000, 0x4e: 0xa000, 0x4f: 0xa000, 0x50: 0xa000,
	0x52: 0xa000, 0x53: 0xa000, 0x54: 0xa000, 0x55: 0xa000, 0x56: 0xa000, 0x57: 0xa000,
	0x58: 0xa000, 0x59: 0xa000, 0x5a: 0xa000,
	0x61: 0xa000, 0x62: 0xa000, 0x63: 0xa000,
	0x64: 0xa000, 0x65: 0xa000, 0x66: 0xa000, 0x67: 0xa000, 0x68: 0xa000, 0x69: 0xa000,
	0x6a: 0xa000, 0x6b: 0xa000, 0x6c: 0xa000, 0x6d: 0xa000, 0x6e: 0xa000, 0x6f: 0xa000,
	0x70: 0xa000, 0x72: 0xa000, 0x73: 0xa000, 0x74: 0xa000, 0x75: 0xa000,
	0x76: 0xa000, 0x77: 0xa000, 0x78: 0xa000, 0x79: 0xa000, 0x7a: 0xa000,
	// Block 0x2, offset 0x80
	// Block 0x3, offset 0xc0
	0xc0: 0x2f72, 0xc1: 0x2f77, 0xc2: 0x468b, 0xc3: 0x2f7c, 0xc4: 0x469a, 0xc5: 0x469f,
	0xc6: 0xa000, 0xc7: 0x46a9, 0xc8: 0x2fe5, 0xc9: 0x2fea, 0xca: 0x46ae, 0xcb: 0x2ffe,
	0xcc: 0x3071, 0xcd: 0x3076, 0xce: 0x307b, 0xcf: 0x46c2, 0xd1: 0x3107,
	0xd2: 0x312a, 0xd3: 0x312f, 0xd4: 0x46cc, 0xd5: 0x46d1, 0xd6: 0x46e0,
	0xd8: 0xa000, 0xd9: 0x31b6, 0xda: 0x31bb, 0xdb: 0x31c0, 0xdc: 0x4712, 0xdd: 0x3238,
	0xe0: 0x327e, 0xe1: 0x3283, 0xe2: 0x471c, 0xe3: 0x3288,
	0xe4: 0x472b, 0xe5: 0x4730, 0xe6: 0xa000, 0xe7: 0x473a, 0xe8: 0x32f1, 0xe9: 0x32f6,
	0xea: 0x473f, 0xeb: 0x330a, 0xec: 0x3382, 0xed: 0x3387, 0xee: 0x338c, 0xef: 0x4753,
	0xf1: 0x3418, 0xf2: 0x343b, 0xf3: 0x3440, 0xf4: 0x475d, 0xf5: 0x4762,
	0xf6: 0x4771, 0xf8: 0xa000, 0xf9: 0x34cc, 0xfa: 0x34d1, 0xfb: 0x34d6,
	0xfc: 0x47a3, 0xfd: 0x3553, 0xff: 0x356c,
	// Block 0x4, offset 0x100
	0x100: 0x2f81, 0x101: 0x328d, 0x102: 0x4690, 0x103: 0x4721, 0x104: 0x2f9f, 0x105: 0x32ab,
	0x106: 0x2fb3, 0x107: 0x32bf, 0x108: 0x2fb8, 0x109: 0x32c4, 0x10a: 0x2fbd, 0x10b: 0x32c9,
	0x10c: 0x2fc2, 0x10d: 0x32ce, 0x10e: 0x2fcc, 0x10f: 0x32d8,
	0x112: 0x46b3, 0x113: 0x4744, 0x114: 0x2ff4, 0x115: 0x3300, 0x116: 0x2ff9, 0x117: 0x3305,
	0x118: 0x3017, 0x119: 0x3323, 0x11a: 0x3008, 0x11b: 0x3314, 0x11c: 0x3030, 0x11d: 0x333c,
	0x11e: 0x303a, 0x11f: 0x3346, 0x120: 0x303f, 0x121: 0x334b, 0x122: 0x3049, 0x123: 0x3355,
	0x124: 0x304e, 0x125: 0x335a, 0x128: 0x3080, 0x129: 0x3391,
	0x12a: 0x3085, 0x12b: 0x3396, 0x12c: 0x308a, 0x12d: 0x339b, 0x12e: 0x30ad, 0x12f: 0x33b9,
	0x130: 0x308f, 0x134: 0x30b7, 0x135: 0x33c3,
	0x136: 0x30cb, 0x137: 0x33dc, 0x139: 0x30d5, 0x13a: 0x33e6, 0x13b: 0x30df,
	0x13c: 0x33f0, 0x13d: 0x30da, 0x13e: 0x33eb,
	// Block 0x5, offset 0x140
	0x143: 0x3102, 0x144: 0x3413, 0x145: 0x311b,
	0x146: 0x342c, 0x147: 0x3111, 0x148: 0x3422,
	0x14c: 0x46d6, 0x14d: 0x4767, 0x14e: 0x3134, 0x14f: 0x3445, 0x150: 0x313e, 0x151: 0x344f,
	0x154: 0x315c, 0x155: 0x346d, 0x156: 0x3175, 0x157: 0x3486,
	0x158: 0x3166, 0x159: 0x3477, 0x15a: 0x46f9, 0x15b: 0x478a, 0x15c: 0x317f, 0x15d: 0x3490,
	0x15e: 0x318e, 0x15f: 0x349f, 0x160: 0x46fe, 0x161: 0x478f, 0x162: 0x31a7, 0x163: 0x34bd,
	0x164: 0x3198, 0x165: 0x34ae, 0x168: 0x4708, 0x169: 0x4799,
	0x16a: 0x470d, 0x16b: 0x479e, 0x16c: 0x31c5, 0x16d: 0x34db, 0x16e: 0x31cf, 0x16f: 0x34e5,
	0x170: 0x31d4, 0x171: 0x34ea, 0x172: 0x31f2, 0x173: 0x3508, 0x174: 0x3215, 0x175: 0x352b,
	0x176: 0x323d, 0x177: 0x3558, 0x178: 0x3251, 0x179: 0x3260, 0x17a: 0x3580, 0x17b: 0x326a,
	0x17c: 0x358a, 0x17d: 0x326f, 0x17e: 0x358f, 0x17f: 0xa000,
	// Block 0x6, offset 0x180
	0x184: 0x8100, 0x185: 0x8100,
	0x186: 0x8100,
	0x18d: 0x2f8b, 0x18e: 0x3297, 0x18f: 0x3099, 0x190: 0x33a5, 0x191: 0x3143,
	0x192: 0x3454, 0x193: 0x31d9, 0x194: 0x34ef, 0x195: 0x39d2, 0x196: 0x3b61, 0x197: 0x39cb,
	0x198: 0x3b5a, 0x199: 0x39d9, 0x19a: 0x3b68, 0x19b: 0x39c4, 0x19c: 0x3b53,
	0x19e: 0x38b3, 0x19f: 0x3a42, 0x1a0: 0x38ac, 0x1a1: 0x3a3b, 0x1a2: 0x35b6, 0x1a3: 0x35c8,
	0x1a6: 0x3044, 0x1a7: 0x3350, 0x1a8: 0x30c1, 0x1a9: 0x33d2,
	0x1aa: 0x46ef, 0x1ab: 0x4780, 0x1ac: 0x3993, 0x1ad: 0x3b22, 0x1ae: 0x35da, 0x1af: 0x35e0,
	0x1b0: 0x33c8, 0x1b4: 0x302b, 0x1b5: 0x3337,
	0x1b8: 0x30fd, 0x1b9: 0x340e, 0x1ba: 0x38ba, 0x1bb: 0x3a49,
	0x1bc: 0x35b0, 0x1bd: 0x35c2, 0x1be: 0x35bc, 0x1bf: 0x35ce,
	// Block 0x7, offset 0x1c0
	0x1c0: 0x2f90, 0x1c1: 0x329c, 0x1c2: 0x2f95, 0x1c3: 0x32a1, 0x1c4: 0x300d, 0x1c5: 0x3319,
	0x1c6: 0x3012, 0x1c7: 0x331e, 0x1c8: 0x309e, 0x1c9: 0x33aa, 0x1ca: 0x30a3, 0x1cb: 0x33af,
	0x1cc: 0x3148, 0x1cd: 0x3459, 0x1ce: 0x314d, 0x1cf: 0x345e, 0x1d0: 0x316b, 0x1d1: 0x347c,
	0x1d2: 0x3170, 0x1d3: 0x3481, 0x1d4: 0x31de, 0x1d5: 0x34f4, 0x1d6: 0x31e3, 0x1d7: 0x34f9,
	0x1d8: 0x3189, 0x1d9: 0x349a, 0x1da: 0x31a2, 0x1db: 0x34b8,
	0x1de: 0x305d, 0x1df: 0x3369,
	0x1e6: 0x4695, 0x1e7: 0x4726, 0x1e8: 0x46bd, 0x1e9: 0x474e,
	0x1ea: 0x3962, 0x1eb: 0x3af1, 0x1ec: 0x393f, 0x1ed: 0x3ace, 0x1ee: 0x46db, 0x1ef: 0x476c,
	0x1f0: 0x395b, 0x1f1: 0x3aea, 0x1f2: 0x3247, 0x1f3: 0x3562,
	// Block 0x8, offset 0x200
	0x200: 0x9932, 0x201: 0x9932, 0x202: 0x9932, 0x203: 0x9932, 0x204: 0x9932, 0x205: 0x8132,
	0x206: 0x9932, 0x207: 0x9932, 0x208: 0x9932, 0x209: 0x9932, 0x20a: 0x9932, 0x20b: 0x9932,
	0x20c: 0x9932, 0x20d: 0x8132, 0x20e: 0x8132, 0x20f: 0x9932, 0x210: 0x8132, 0x211: 0x9932,
	0x212: 0x8132, 0x213: 0x9932, 0x214: 0x9932, 0x215: 0x8133, 0x216: 0x812d, 0x217: 0x812d,
	0x218: 0x812d, 0x219: 0x812d, 0x21a: 0x8133, 0x21b: 0x992b, 0x21c: 0x812d, 0x21d: 0x812d,
	0x21e: 0x812d, 0x21f: 0x812d, 0x220: 0x812d, 0x221: 0x8129, 0x222: 0x8129, 0x223: 0x992d,
	0x224: 0x992d, 0x225: 0x992d, 0x226: 0x992d, 0x227: 0x9929, 0x228: 0x9929, 0x229: 0x812d,
	0x22a: 0x812d, 0x22b: 0x812d, 0x22c: 0x812d, 0x22d: 0x992d, 0x22e: 0x992d, 0x22f: 0x812d,
	0x230: 0x992d, 0x231: 0x992d, 0x232: 0x812d, 0x233: 0x812d, 0x234: 0x8101, 0x235: 0x8101,
	0x236: 0x8101, 0x237: 0x8101, 0x238: 0x9901, 0x239: 0x812d, 0x23a: 0x812d, 0x23b: 0x812d,
	0x23c: 0x812d, 0x23d: 0x8132, 0x23e: 0x8132, 0x23f: 0x8132,
	// Block 0x9, offset 0x240
	0x240: 0x49b1, 0x241: 0x49b6, 0x242: 0x9932, 0x243: 0x49bb, 0x244: 0x4a74, 0x245: 0x9936,
	0x246: 0x8132, 0x247: 0x812d, 0x248: 0x812d, 0x249: 0x812d, 0x24a: 0x8132, 0x24b: 0x8132,
	0x24c: 0x8132, 0x24d: 0x812d, 0x24e: 0x812d, 0x250: 0x8132, 0x251: 0x8132,
	0x252: 0x8132, 0x253: 0x812d, 0x254: 0x812d, 0x255: 0x812d, 0x256: 0x812d, 0x257: 0x8132,
	0x258: 0x8133, 0x259: 0x812d, 0x25a: 0x812d, 0x25b: 0x8132, 0x25c: 0x8134, 0x25d: 0x8135,
	0x25e: 0x8135, 0x25f: 0x8134, 0x260: 0x8135, 0x261: 0x8135, 0x262: 0x8134, 0x263: 0x8132,
	0x264: 0x8132, 0x265: 0x8132, 0x266: 0x8132, 0x267: 0x8132, 0x268: 0x8132, 0x269: 0x8132,
	0x26a: 0x8132, 0x26b: 0x8132, 0x26c: 0x8132, 0x26d: 0x8132, 0x26e: 0x8132, 0x26f: 0x8132,
	0x274: 0x0170,
	0x27a: 0x8100,
	0x27e: 0x0037,
	// Block 0xa, offset 0x280
	0x284: 0x8100, 0x285: 0x35a4,
	0x286: 0x35ec, 0x287: 0x00ce, 0x288: 0x360a, 0x289: 0x3616, 0x28a: 0x3628,
	0x28c: 0x3646, 0x28e: 0x3658, 0x28f: 0x3676, 0x290: 0x3e0b, 0x291: 0xa000,
	0x295: 0xa000, 0x297: 0xa000,
	0x299: 0xa000,
	0x29f: 0xa000, 0x2a1: 0xa000,
	0x2a5: 0xa000, 0x2a9: 0xa000,
	0x2aa: 0x363a, 0x2ab: 0x366a, 0x2ac: 0x4801, 0x2ad: 0x369a, 0x2ae: 0x482b, 0x2af: 0x36ac,
	0x2b0: 0x3e73, 0x2b1: 0xa000, 0x2b5: 0xa000,
	0x2b7: 0xa000, 0x2b9: 0xa000,
	0x2bf: 0xa000,
	// Block 0xb, offset 0x2c0
	0x2c0: 0x3724, 0x2c1: 0x3730, 0x2c3: 0x371e,
	0x2c6: 0xa000, 0x2c7: 0x370c,
	0x2cc: 0x3760, 0x2cd: 0x3748, 0x2ce: 0x3772, 0x2d0: 0xa000,
	0x2d3: 0xa000, 0x2d5: 0xa000, 0x2d6: 0xa000, 0x2d7: 0xa000,
	0x2d8: 0xa000, 0x2d9: 0x3754, 0x2da: 0xa000,
	0x2de: 0xa000, 0x2e3: 0xa000,
	0x2e7: 0xa000,
	0x2eb: 0xa000, 0x2ed: 0xa000,
	0x2f0: 0xa000, 0x2f3: 0xa000, 0x2f5: 0xa000,
	0x2f6: 0xa000, 0x2f7: 0xa000, 0x2f8: 0xa000, 0x2f9: 0x37d8, 0x2fa: 0xa000,
	0x2fe: 0xa000,
	// Block 0xc, offset 0x300
	0x301: 0x3736, 0x302: 0x37ba,
	0x310: 0x3712, 0x311: 0x3796,
	0x312: 0x3718, 0x313: 0x379c, 0x316: 0x372a, 0x317: 0x37ae,
	0x318: 0xa000, 0x319: 0xa000, 0x31a: 0x382c, 0x31b: 0x3832, 0x31c: 0x373c, 0x31d: 0x37c0,
	0x31e: 0x3742, 0x31f: 0x37c6, 0x322: 0x374e, 0x323: 0x37d2,
	0x324: 0x375a, 0x325: 0x37de, 0x326: 0x3766, 0x327: 0x37ea, 0x328: 0xa000, 0x329: 0xa000,
	0x32a: 0x3838, 0x32b: 0x383e, 0x32c: 0x3790, 0x32d: 0x3814, 0x32e: 0x376c, 0x32f: 0x37f0,
	0x330: 0x3778, 0x331: 0x37fc, 0x332: 0x377e, 0x333: 0x3802, 0x334: 0x3784, 0x335: 0x3808,
	0x338: 0x378a, 0x339: 0x380e,
	// Block 0xd, offset 0x340
	0x351: 0x812d,
	0x352: 0x8132, 0x353: 0x8132, 0x354: 0x8132, 0x355: 0x8132, 0x356: 0x812d, 0x357: 0x8132,
	0x358: 0x8132, 0x359: 0x8132, 0x35a: 0x812e, 0x35b: 0x812d, 0x35c: 0x8132, 0x35d: 0x8132,
	0x35e: 0x8132, 0x35f: 0x8132, 0x360: 0x8132, 0x361: 0x8132, 0x362: 0x812d, 0x363: 0x812d,
	0x364: 0x812d, 0x365: 0x812d, 0x366: 0x812d, 0x367: 0x812d, 0x368: 0x8132, 0x369: 0x8132,
	0x36a: 0x812d, 0x36b: 0x8132, 0x36c: 0x8132, 0x36d: 0x812e, 0x36e: 0x8131, 0x36f: 0x8132,
	0x370: 0x8105, 0x371: 0x8106, 0x372: 0x8107, 0x373: 0x8108, 0x374: 0x8109, 0x375: 0x810a,
	0x376: 0x810b, 0x377: 0x810c, 0x378: 0x810d, 0x379: 0x810e, 0x37a: 0x810e, 0x37b: 0x810f,
	0x37c: 0x8110, 0x37d: 0x8111, 0x37f: 0x8112,
	// Block 0xe, offset 0x380
	0x388: 0xa000, 0x38a: 0xa000, 0x38b: 0x8116,
	0x38c: 0x8117, 0x38d: 0x8118, 0x38e: 0x8119, 0x38f: 0x811a, 0x390: 0x811b, 0x391: 0x811c,
	0x392: 0x811d, 0x393: 0x9932, 0x394: 0x9932, 0x395: 0x992d, 0x396: 0x812d, 0x397: 0x8132,
	0x398: 0x8132, 0x399: 0x8132, 0x39a: 0x8132, 0x39b: 0x8132, 0x39c: 0x812d, 0x39d: 0x8132,
	0x39e: 0x8132, 0x39f: 0x812d,
	0x3b0: 0x811e,
	// Block 0xf, offset 0x3c0
	0x3d3: 0x812d, 0x3d4: 0x8132, 0x3d5: 0x8132, 0x3d6: 0x8132, 0x3d7: 0x8132,
	0x3d8: 0x8132, 0x3d9: 0x8132, 0x3da: 0x8132, 0x3db: 0x8132, 0x3dc: 0x8132, 0x3dd: 0x8132,
	0x3de: 0x8132, 0x3df: 0x8132, 0x3e0: 0x8132, 0x3e1: 0x8132, 0x3e3: 0x812d,
	0x3e4: 0x8132, 0x3e5: 0x8132, 0x3e6: 0x812d, 0x3e7: 0x8132, 0x3e8: 0x8132, 0x3e9: 0x812d,
	0x3ea: 0x8132, 0x3eb: 0x8132, 0x3ec: 0x8132, 0x3ed: 0x812d, 0x3ee: 0x812d, 0x3ef: 0x812d,
	0x3f0: 0x8116, 0x3f1: 0x8117, 0x3f2: 0x8118, 0x3f3: 0x8132, 0x3f4: 0x8132, 0x3f5: 0x8132,
	0x3f6: 0x812d, 0x3f7: 0x8132, 0x3f8: 0x8132, 0x3f9: 0x812d, 0x3fa: 0x812d, 0x3fb: 0x8132,
	0x3fc: 0x8132, 0x3fd: 0x8132, 0x3fe: 0x8132, 0x3ff: 0x8132,
	// Block 0x10, offset 0x400
	0x405: 0xa000,
	0x406: 0x2d29, 0x407: 0xa000, 0x408: 0x2d31, 0x409: 0xa000, 0x40a: 0x2d39, 0x40b: 0xa000,
	0x40c: 0x2d41, 0x40d: 0xa000, 0x40e: 0x2d49, 0x411: 0xa000,
	0x412: 0x2d51,
	0x434: 0x8102, 0x435: 0x9900,
	0x43a: 0xa000, 0x43b: 0x2d59,
	0x43c: 0xa000, 0x43d: 0x2d61, 0x43e: 0xa000, 0x43f: 0xa000,
	// Block 0x11, offset 0x440
	0x440: 0x8132, 0x441: 0x8132, 0x442: 0x812d, 0x443: 0x8132, 0x444: 0x8132, 0x445: 0x8132,
	0x446: 0x8132, 0x447: 0x8132, 0x448: 0x8132, 0x449: 0x8132, 0x44a: 0x812d, 0x44b: 0x8132,
	0x44c: 0x8132, 0x44d: 0x8135, 0x44e: 0x812a, 0x44f: 0x812d, 0x450: 0x8129, 0x451: 0x8132,
	0x452: 0x8132, 0x453: 0x8132, 0x454: 0x8132, 0x455: 0x8132, 0x456: 0x8132, 0x457: 0x8132,
	0x458: 0x8132, 0x459: 0x8132, 0x45a: 0x8132, 0x45b: 0x8132, 0x45c: 0x8132, 0x45d: 0x8132,
	0x45e: 0x8132, 0x45f: 0x8132, 0x460: 0x8132, 0x461: 0x8132, 0x462: 0x8132, 0x463: 0x8132,
	0x464: 0x8132, 0x465: 0x8132, 0x466: 0x8132, 0x467: 0x8132, 0x468: 0x8132, 0x469: 0x8132,
	0x46a: 0x8132, 0x46b: 0x8132, 0x46c: 0x8132, 0x46d: 0x8132, 0x46e: 0x8132, 0x46f: 0x8132,
	0x470: 0x8132, 0x471: 0x8132, 0x472: 0x8132, 0x473: 0x8132, 0x474: 0x8132, 0x475: 0x8132,
	0x476: 0x8133, 0x477: 0x8131, 0x478: 0x8131, 0x479: 0x812d, 0x47b: 0x8132,
	0x47c: 0x8134, 0x47d: 0x812d, 0x47e: 0x8132, 0x47f: 0x812d,
	// Block 0x12, offset 0x480
	0x480: 0x2f9a, 0x481: 0x32a6, 0x482: 0x2fa4, 0x483: 0x32b0, 0x484: 0x2fa9, 0x485: 0x32b5,
	0x486: 0x2fae, 0x487: 0x32ba, 0x488: 0x38cf, 0x489: 0x3a5e, 0x48a: 0x2fc7, 0x48b: 0x32d3,
	0x48c: 0x2fd1, 0x48d: 0x32dd, 0x48e: 0x2fe0, 0x48f: 0x32ec, 0x490: 0x2fd6, 0x491: 0x32e2,
	0x492: 0x2fdb, 0x493: 0x32e7, 0x494: 0x38f2, 0x495: 0x3a81, 0x496: 0x38f9, 0x497: 0x3a88,
	0x498: 0x301c, 0x499: 0x3328, 0x49a: 0x3021, 0x49b: 0x332d, 0x49c: 0x3907, 0x49d: 0x3a96,
	0x49e: 0x3026, 0x49f: 0x3332, 0x4a0: 0x3035, 0x4a1: 0x3341, 0x4a2: 0x3053, 0x4a3: 0x335f,
	0x4a4: 0x3062, 0x4a5: 0x336e, 0x4a6: 0x3058, 0x4a7: 0x3364, 0x4a8: 0x3067, 0x4a9: 0x3373,
	0x4aa: 0x306c, 0x4ab: 0x3378, 0x4ac: 0x30b2, 0x4ad: 0x33be, 0x4ae: 0x390e, 0x4af: 0x3a9d,
	0x4b0: 0x30bc, 0x4b1: 0x33cd, 0x4b2: 0x30c6, 0x4b3: 0x33d7, 0x4b4: 0x30d0, 0x4b5: 0x33e1,
	0x4b6: 0x46c7, 0x4b7: 0x4758, 0x4b8: 0x3915, 0x4b9: 0x3aa4, 0x4ba: 0x30e9, 0x4bb: 0x33fa,
	0x4bc: 0x30e4, 0x4bd: 0x33f5, 0x4be: 0x30ee, 0x4bf: 0x33ff,
	// Block 0x13, offset 0x4c0
	0x4c0: 0x30f3, 0x4c1: 0x3404, 0x4c2: 0x30f8, 0x4c3: 0x3409, 0x4c4: 0x310c, 0x4c5: 0x341d,
	0x4c6: 0x3116, 0x4c7: 0x3427, 0x4c8: 0x3125, 0x4c9: 0x3436, 0x4ca: 0x3120, 0x4cb: 0x3431,
	0x4cc: 0x3938, 0x4cd: 0x3ac7, 0x4ce: 0x3946, 0x4cf: 0x3ad5, 0x4d0: 0x394d, 0x4d1: 0x3adc,
	0x4d2: 0x3954, 0x4d3: 0x3ae3, 0x4d4: 0x3152, 0x4d5: 0x3463, 0x4d6: 0x3157, 0x4d7: 0x3468,
	0x4d8: 0x3161, 0x4d9: 0x3472, 0x4da: 0x46f4, 0x4db: 0x4785, 0x4dc: 0x399a, 0x4dd: 0x3b29,
	0x4de: 0x317a, 0x4df: 0x348b, 0x4e0: 0x3184, 0x4e1: 0x3495, 0x4e2: 0x4703, 0x4e3: 0x4794,
	0x4e4: 0x39a1, 0x4e5: 0x3b30, 0x4e6: 0x39a8, 0x4e7: 0x3b37, 0x4e8: 0x39af, 0x4e9: 0x3b3e,
	0x4ea: 0x3193, 0x4eb: 0x34a4, 0x4ec: 0x319d, 0x4ed: 0x34b3, 0x4ee: 0x31b1, 0x4ef: 0x34c7,
	0x4f0: 0x31ac, 0x4f1: 0x34c2, 0x4f2: 0x31ed, 0x4f3: 0x3503, 0x4f4: 0x31fc, 0x4f5: 0x3512,
	0x4f6: 0x31f7, 0x4f7: 0x350d, 0x4f8: 0x39b6, 0x4f9: 0x3b45, 0x4fa: 0x39bd, 0x4fb: 0x3b4c,
	0x4fc: 0x3201, 0x4fd: 0x3517, 0x4fe: 0x3206, 0x4ff: 0x351c,
	// Block 0x14, offset 0x500
	0x500: 0x320b, 0x501: 0x3521, 0x502: 0x3210, 0x503: 0x3526, 0x504: 0x321f, 0x505: 0x3535,
	0x506: 0x321a, 0x507: 0x3530, 0x508: 0x3224, 0x509: 0x353f, 0x50a: 0x3229, 0x50b: 0x3544,
	0x50c: 0x322e, 0x50d: 0x3549, 0x50e: 0x324c, 0x50f: 0x3567, 0x510: 0x3265, 0x511: 0x3585,
	0x512: 0x3274, 0x513: 0x3594, 0x514: 0x3279, 0x515: 0x3599, 0x516: 0x337d, 0x517: 0x34a9,
	0x518: 0x353a, 0x519: 0x3576, 0x51b: 0x35d4,
	0x520: 0x46a4, 0x521: 0x4735, 0x522: 0x2f86, 0x523: 0x3292,
	0x524: 0x387b, 0x525: 0x3a0a, 0x526: 0x3874, 0x527: 0x3a03, 0x528: 0x3889, 0x529: 0x3a18,
	0x52a: 0x3882, 0x52b: 0x3a11, 0x52c: 0x38c1, 0x52d: 0x3a50, 0x52e: 0x3897, 0x52f: 0x3a26,
	0x530: 0x3890, 0x531: 0x3a1f, 0x532: 0x38a5, 0x533: 0x3a34, 0x534: 0x389e, 0x535: 0x3a2d,
	0x536: 0x38c8, 0x537: 0x3a57, 0x538: 0x46b8, 0x539: 0x4749, 0x53a: 0x3003, 0x53b: 0x330f,
	0x53c: 0x2fef, 0x53d: 0x32fb, 0x53e: 0x38dd, 0x53f: 0x3a6c,
	// Block 0x15, offset 0x540
	0x540: 0x38d6, 0x541: 0x3a65, 0x542: 0x38eb, 0x543: 0x3a7a, 0x544: 0x38e4, 0x545: 0x3a73,
	0x546: 0x3900, 0x547: 0x3a8f, 0x548: 0x3094, 0x549: 0x33a0, 0x54a: 0x30a8, 0x54b: 0x33b4,
	0x54c: 0x46ea, 0x54d: 0x477b, 0x54e: 0x3139, 0x54f: 0x344a, 0x550: 0x3923, 0x551: 0x3ab2,
	0x552: 0x391c, 0x553: 0x3aab, 0x554: 0x3931, 0x555: 0x3ac0, 0x556: 0x392a, 0x557: 0x3ab9,
	0x558: 0x398c, 0x559: 0x3b1b, 0x55a: 0x3970, 0x55b: 0x3aff, 0x55c: 0x3969, 0x55d: 0x3af8,
	0x55e: 0x397e, 0x55f: 0x3b0d, 0x560: 0x3977, 0x561: 0x3b06, 0x562: 0x3985, 0x563: 0x3b14,
	0x564: 0x31e8, 0x565: 0x34fe, 0x566: 0x31ca, 0x567: 0x34e0, 0x568: 0x39e7, 0x569: 0x3b76,
	0x56a: 0x39e0, 0x56b: 0x3b6f, 0x56c: 0x39f5, 0x56d: 0x3b84, 0x56e: 0x39ee, 0x56f: 0x3b7d,
	0x570: 0x39fc, 0x571: 0x3b8b, 0x572: 0x3233, 0x573: 0x354e, 0x574: 0x325b, 0x575: 0x357b,
	0x576: 0x3256, 0x577: 0x3571, 0x578: 0x3242, 0x579: 0x355d,
	// Block 0x16, offset 0x580
	0x580: 0x4807, 0x581: 0x480d, 0x582: 0x4921, 0x583: 0x4939, 0x584: 0x4929, 0x585: 0x4941,
	0x586: 0x4931, 0x587: 0x4949, 0x588: 0x47ad, 0x589: 0x47b3, 0x58a: 0x4891, 0x58b: 0x48a9,
	0x58c: 0x4899, 0x58d: 0x48b1, 0x58e: 0x48a1, 0x58f: 0x48b9, 0x590: 0x4819, 0x591: 0x481f,
	0x592: 0x3dbb, 0x593: 0x3dcb, 0x594: 0x3dc3, 0x595: 0x3dd3,
	0x598: 0x47b9, 0x599: 0x47bf, 0x59a: 0x3ceb, 0x59b: 0x3cfb, 0x59c: 0x3cf3, 0x59d: 0x3d03,
	0x5a0: 0x4831, 0x5a1: 0x4837, 0x5a2: 0x4951, 0x5a3: 0x4969,
	0x5a4: 0x4959, 0x5a5: 0x4971, 0x5a6: 0x4961, 0x5a7: 0x4979, 0x5a8: 0x47c5, 0x5a9: 0x47cb,
	0x5aa: 0x48c1, 0x5ab: 0x48d9, 0x5ac: 0x48c9, 0x5ad: 0x48e1, 0x5ae: 0x48d1, 0x5af: 0x48e9,
	0x5b0: 0x4849, 0x5b1: 0x484f, 0x5b2: 0x3e1b, 0x5b3: 0x3e33, 0x5b4: 0x3e23, 0x5b5: 0x3e3b,
	0x5b6: 0x3e2b, 0x5b7: 0x3e43, 0x5b8: 0x47d1, 0x5b9: 0x47d7, 0x5ba: 0x3d1b, 0x5bb: 0x3d33,
	0x5bc: 0x3d23, 0x5bd: 0x3d3b, 0x5be: 0x3d2b, 0x5bf: 0x3d43,
	// Block 0x17, offset 0x5c0
	0x5c0: 0x4855, 0x5c1: 0x485b, 0x5c2: 0x3e4b, 0x5c3: 0x3e5b, 0x5c4: 0x3e53, 0x5c5: 0x3e63,
	0x5c8: 0x47dd, 0x5c9: 0x47e3, 0x5ca: 0x3d4b, 0x5cb: 0x3d5b,
	0x5cc: 0x3d53, 0x5cd: 0x3d63, 0x5d0: 0x4867, 0x5d1: 0x486d,
	0x5d2: 0x3e83, 0x5d3: 0x3e9b, 0x5d4: 0x3e8b, 0x5d5: 0x3ea3, 0x5d6: 0x3e93, 0x5d7: 0x3eab,
	0x5d9: 0x47e9, 0x5db: 0x3d6b, 0x5dd: 0x3d73,
	0x5df: 0x3d7b, 0x5e0: 0x487f, 0x5e1: 0x4885, 0x5e2: 0x4981, 0x5e3: 0x4999,
	0x5e4: 0x4989, 0x5e5: 0x49a1, 0x5e6: 0x4991, 0x5e7: 0x49a9, 0x5e8: 0x47ef, 0x5e9: 0x47f5,
	0x5ea: 0x48f1, 0x5eb: 0x4909, 0x5ec: 0x48f9, 0x5ed: 0x4911, 0x5ee: 0x4901, 0x5ef: 0x4919,
	0x5f0: 0x47fb, 0x5f1: 0x4321, 0x5f2: 0x3694, 0x5f3: 0x4327, 0x5f4: 0x4825, 0x5f5: 0x432d,
	0x5f6: 0x36a6, 0x5f7: 0x4333, 0x5f8: 0x36c4, 0x5f9: 0x4339, 0x5fa: 0x36dc, 0x5fb: 0x433f,
	0x5fc: 0x4873, 0x5fd: 0x4345,
	// Block 0x18, offset 0x600
	0x600: 0x3da3, 0x601: 0x3dab, 0x602: 0x4187, 0x603: 0x41a5, 0x604: 0x4191, 0x605: 0x41af,
	0x606: 0x419b, 0x607: 0x41b9, 0x608: 0x3cdb, 0x609: 0x3ce3, 0x60a: 0x40d3, 0x60b: 0x40f1,
	0x60c: 0x40dd, 0x60d: 0x40fb, 0x60e: 0x40e7, 0x60f: 0x4105, 0x610: 0x3deb, 0x611: 0x3df3,
	0x612: 0x41c3, 0x613: 0x41e1, 0x614: 0x41cd, 0x615: 0x41eb, 0x616: 0x41d7, 0x617: 0x41f5,
	0x618: 0x3d0b, 0x619: 0x3d13, 0x61a: 0x410f, 0x61b: 0x412d, 0x61c: 0x4119, 0x61d: 0x4137,
	0x61e: 0x4123, 0x61f: 0x4141, 0x620: 0x3ec3, 0x621: 0x3ecb, 0x622: 0x41ff, 0x623: 0x421d,
	0x624: 0x4209, 0x625: 0x4227, 0x626: 0x4213, 0x627: 0x4231, 0x628: 0x3d83, 0x629: 0x3d8b,
	0x62a: 0x414b, 0x62b: 0x4169, 0x62c: 0x4155, 0x62d: 0x4173, 0x62e: 0x415f, 0x62f: 0x417d,
	0x630: 0x3688, 0x631: 0x3682, 0x632: 0x3d93, 0x633: 0x368e, 0x634: 0x3d9b,
	0x636: 0x4813, 0x637: 0x3db3, 0x638: 0x35f8, 0x639: 0x35f2, 0x63a: 0x35e6, 0x63b: 0x42f1,
	0x63c: 0x35fe, 0x63d: 0x8100, 0x63e: 0x01d3, 0x63f: 0xa100,
	// Block 0x19, offset 0x640
	0x640: 0x8100, 0x641: 0x35aa, 0x642: 0x3ddb, 0x643: 0x36a0, 0x644: 0x3de3,
	0x646: 0x483d, 0x647: 0x3dfb, 0x648: 0x3604, 0x649: 0x42f7, 0x64a: 0x3610, 0x64b: 0x42fd,
	0x64c: 0x361c, 0x64d: 0x3b92, 0x64e: 0x3b99, 0x64f: 0x3ba0, 0x650: 0x36b8, 0x651: 0x36b2,
	0x652: 0x3e03, 0x653: 0x44e7, 0x656: 0x36be, 0x657: 0x3e13,
	0x658: 0x3634, 0x659: 0x362e, 0x65a: 0x3622, 0x65b: 0x4303, 0x65d: 0x3ba7,
	0x65e: 0x3bae, 0x65f: 0x3bb5, 0x660: 0x36ee, 0x661: 0x36e8, 0x662: 0x3e6b, 0x663: 0x44ef,
	0x664: 0x36d0, 0x665: 0x36d6, 0x666: 0x36f4, 0x667: 0x3e7b, 0x668: 0x3664, 0x669: 0x365e,
	0x66a: 0x3652, 0x66b: 0x430f, 0x66c: 0x364c, 0x66d: 0x359e, 0x66e: 0x42eb, 0x66f: 0x0081,
	0x672: 0x3eb3, 0x673: 0x36fa, 0x674: 0x3ebb,
	0x676: 0x488b, 0x677: 0x3ed3, 0x678: 0x3640, 0x679: 0x4309, 0x67a: 0x3670, 0x67b: 0x431b,
	0x67c: 0x367c, 0x67d: 0x4259, 0x67e: 0xa100,
	// Block 0x1a, offset 0x680
	0x681: 0x3c09, 0x683: 0xa000, 0x684: 0x3c10, 0x685: 0xa000,
	0x687: 0x3c17, 0x688: 0xa000, 0x689: 0x3c1e,
	0x68d: 0xa000,
	0x6a0: 0x2f68, 0x6a1: 0xa000, 0x6a2: 0x3c2c,
	0x6a4: 0xa000, 0x6a5: 0xa000,
	0x6ad: 0x3c25, 0x6ae: 0x2f63, 0x6af: 0x2f6d,
	0x6b0: 0x3c33, 0x6b1: 0x3c3a, 0x6b2: 0xa000, 0x6b3: 0xa000, 0x6b4: 0x3c41, 0x6b5: 0x3c48,
	0x6b6: 0xa000, 0x6b7: 0xa000, 0x6b8: 0x3c4f, 0x6b9: 0x3c56, 0x6ba: 0xa000, 0x6bb: 0xa000,
	0x6bc: 0xa000, 0x6bd: 0xa000,
	// Block 0x1b, offset 0x6c0
	0x6c0: 0x3c5d, 0x6c1: 0x3c64, 0x6c2: 0xa000, 0x6c3: 0xa000, 0x6c4: 0x3c79, 0x6c5: 0x3c80,
	0x6c6: 0xa000, 0x6c7: 0xa000, 0x6c8: 0x3c87, 0x6c9: 0x3c8e,
	0x6d1: 0xa000,
	0x6d2: 0xa000,
	0x6e2: 0xa000,
	0x6e8: 0xa000, 0x6e9: 0xa000,
	0x6eb: 0xa000, 0x6ec: 0x3ca3, 0x6ed: 0x3caa, 0x6ee: 0x3cb1, 0x6ef: 0x3cb8,
	0x6f2: 0xa000, 0x6f3: 0xa000, 0x6f4: 0xa000, 0x6f5: 0xa000,
	// Block 0x1c, offset 0x700
	0x706: 0xa000, 0x70b: 0xa000,
	0x70c: 0x3f0b, 0x70d: 0xa000, 0x70e: 0x3f13, 0x70f: 0xa000, 0x710: 0x3f1b, 0x711: 0xa000,
	0x712: 0x3f23, 0x713: 0xa000, 0x714: 0x3f2b, 0x715: 0xa000, 0x716: 0x3f33, 0x717: 0xa000,
	0x718: 0x3f3b, 0x719: 0xa000, 0x71a: 0x3f43, 0x71b: 0xa000, 0x71c: 0x3f4b, 0x71d: 0xa000,
	0x71e: 0x3f53, 0x71f: 0xa000, 0x720: 0x3f5b, 0x721: 0xa000, 0x722: 0x3f63,
	0x724: 0xa000, 0x725: 0x3f6b, 0x726: 0xa000, 0x727: 0x3f73, 0x728: 0xa000, 0x729: 0x3f7b,
	0x72f: 0xa000,
	0x730: 0x3f83, 0x731: 0x3f8b, 0x732: 0xa000, 0x733: 0x3f93, 0x734: 0x3f9b, 0x735: 0xa000,
	0x736: 0x3fa3, 0x737: 0x3fab, 0x738: 0xa000, 0x739: 0x3fb3, 0x73a: 0x3fbb, 0x73b: 0xa000,
	0x73c: 0x3fc3, 0x73d: 0x3fcb,
	// Block 0x1d, offset 0x740
	0x754: 0x3f03,
	0x759: 0x9903, 0x75a: 0x9903, 0x75b: 0x8100, 0x75c: 0x8100, 0x75d: 0xa000,
	0x75e: 0x3fd3,
	0x766: 0xa000,
	0x76b: 0xa000, 0x76c: 0x3fe3, 0x76d: 0xa000, 0x76e: 0x3feb, 0x76f: 0xa000,
	0x770: 0x3ff3, 0x771: 0xa000, 0x772: 0x3ffb, 0x773: 0xa000, 0x774: 0x4003, 0x775: 0xa000,
	0x776: 0x400b, 0x777: 0xa000, 0x778: 0x4013, 0x779: 0xa000, 0x77a: 0x401b, 0x77b: 0xa000,
	0x77c: 0x4023, 0x77d: 0xa000, 0x77e: 0x402b, 0x77f: 0xa000,
	// Block 0x1e, offset 0x780
	0x780: 0x4033, 0x781: 0xa000, 0x782: 0x403b, 0x784: 0xa000, 0x785: 0x4043,
	0x786: 0xa000, 0x787: 0x404b, 0x788: 0xa000, 0x789: 0x4053,
	0x78f: 0xa000, 0x790: 0x405b, 0x791: 0x4063,
	0x792: 0xa000, 0x793: 0x406b, 0x794: 0x4073, 0x795: 0xa000, 0x796: 0x407b, 0x797: 0x4083,
	0x798: 0xa000, 0x799: 0x408b, 0x79a: 0x4093, 0x79b: 0xa000, 0x79c: 0x409b, 0x79d: 0x40a3,
	0x7af: 0xa000,
	0x7b0: 0xa000, 0x7b1: 0xa000, 0x7b2: 0xa000, 0x7b4: 0x3fdb,
	0x7b7: 0x40ab, 0x7b8: 0x40b3, 0x7b9: 0x40bb, 0x7ba: 0x40c3,
	0x7bd: 0xa000, 0x7be: 0x40cb,
	// Block 0x1f, offset 0x7c0
	0x7c0: 0x1377, 0x7c1: 0x0cfb, 0x7c2: 0x13d3, 0x7c3: 0x139f, 0x7c4: 0x0e57, 0x7c5: 0x06eb,
	0x7c6: 0x08df, 0x7c7: 0x162b, 0x7c8: 0x162b, 0x7c9: 0x0a0b, 0x7ca: 0x145f, 0x7cb: 0x0943,
	0x7cc: 0x0a07, 0x7cd: 0x0bef, 0x7ce: 0x0fcf, 0x7cf: 0x115f, 0x7d0: 0x1297, 0x7d1: 0x12d3,
	0x7d2: 0x1307, 0x7d3: 0x141b, 0x7d4: 0x0d73, 0x7d5: 0x0dff, 0x7d6: 0x0eab, 0x7d7: 0x0f43,
	0x7d8: 0x125f, 0x7d9: 0x1447, 0x7da: 0x1573, 0x7db: 0x070f, 0x7dc: 0x08b3, 0x7dd: 0x0d87,
	0x7de: 0x0ecf, 0x7df: 0x1293, 0x7e0: 0x15c3, 0x7e1: 0x0ab3, 0x7e2: 0x0e77, 0x7e3: 0x1283,
	0x7e4: 0x1317, 0x7e5: 0x0c23, 0x7e6: 0x11bb, 0x7e7: 0x12df, 0x7e8: 0x0b1f, 0x7e9: 0x0d0f,
	0x7ea: 0x0e17, 0x7eb: 0x0f1b, 0x7ec: 0x1427, 0x7ed: 0x074f, 0x7ee: 0x07e7, 0x7ef: 0x0853,
	0x7f0: 0x0c8b, 0x7f1: 0x0d7f, 0x7f2: 0x0ecb, 0x7f3: 0x0fef, 0x7f4: 0x1177, 0x7f5: 0x128b,
	0x7f6: 0x12a3, 0x7f7: 0x13c7, 0x7f8: 0x14ef, 0x7f9: 0x15a3, 0x7fa: 0x15bf, 0x7fb: 0x102b,
	0x7fc: 0x106b, 0x7fd: 0x1123, 0x7fe: 0x1243, 0x7ff: 0x147b,
	// Block 0x20, offset 0x800
	0x800: 0x15cb, 0x801: 0x134b, 0x802: 0x09c7, 0x803: 0x0b3b, 0x804: 0x10db, 0x805: 0x119b,
	0x806: 0x0eff, 0x807: 0x1033, 0x808: 0x1397, 0x809: 0x14e7, 0x80a: 0x09c3, 0x80b: 0x0a8f,
	0x80c: 0x0d77, 0x80d: 0x0e2b, 0x80e: 0x0e5f, 0x80f: 0x1113, 0x810: 0x113b, 0x811: 0x14a7,
	0x812: 0x084f, 0x813: 0x11a7, 0x814: 0x07f3, 0x815: 0x07ef, 0x816: 0x1097, 0x817: 0x1127,
	0x818: 0x125b, 0x819: 0x14af, 0x81a: 0x1367, 0x81b: 0x0c27, 0x81c: 0x0d73, 0x81d: 0x1357,
	0x81e: 0x06f7, 0x81f: 0x0a63, 0x820: 0x0b93, 0x821: 0x0f2f, 0x822: 0x0faf, 0x823: 0x0873,
	0x824: 0x103b, 0x825: 0x075f, 0x826: 0x0b77, 0x827: 0x06d7, 0x828: 0x0deb, 0x829: 0x0ca3,
	0x82a: 0x110f, 0x82b: 0x08c7, 0x82c: 0x09b3, 0x82d: 0x0ffb, 0x82e: 0x1263, 0x82f: 0x133b,
	0x830: 0x0db7, 0x831: 0x13f7, 0x832: 0x0de3, 0x833: 0x0c37, 0x834: 0x121b, 0x835: 0x0c57,
	0x836: 0x0fab, 0x837: 0x072b, 0x838: 0x07a7, 0x839: 0x07eb, 0x83a: 0x0d53, 0x83b: 0x10fb,
	0x83c: 0x11f3, 0x83d: 0x1347, 0x83e: 0x145b, 0x83f: 0x085b,
	// Block 0x21, offset 0x840
	0x840: 0x090f, 0x841: 0x0a17, 0x842: 0x0b2f, 0x843: 0x0cbf, 0x844: 0x0e7b, 0x845: 0x103f,
	0x846: 0x1497, 0x847: 0x157b, 0x848: 0x15cf, 0x849: 0x15e7, 0x84a: 0x0837, 0x84b: 0x0cf3,
	0x84c: 0x0da3, 0x84d: 0x13eb, 0x84e: 0x0afb, 0x84f: 0x0bd7, 0x850: 0x0bf3, 0x851: 0x0c83,
	0x852: 0x0e6b, 0x853: 0x0eb7, 0x854: 0x0f67, 0x855: 0x108b, 0x856: 0x112f, 0x857: 0x1193,
	0x858: 0x13db, 0x859: 0x126b, 0x85a: 0x1403, 0x85b: 0x147f, 0x85c: 0x080f, 0x85d: 0x083b,
	0x85e: 0x0923, 0x85f: 0x0ea7, 0x860: 0x12f3, 0x861: 0x133b, 0x862: 0x0b1b, 0x863: 0x0b8b,
	0x864: 0x0c4f, 0x865: 0x0daf, 0x866: 0x10d7, 0x867: 0x0f23, 0x868: 0x073b, 0x869: 0x097f,
	0x86a: 0x0a63, 0x86b: 0x0ac7, 0x86c: 0x0b97, 0x86d: 0x0f3f, 0x86e: 0x0f5b, 0x86f: 0x116b,
	0x870: 0x118b, 0x871: 0x1463, 0x872: 0x14e3, 0x873: 0x14f3, 0x874: 0x152f, 0x875: 0x0753,
	0x876: 0x107f, 0x877: 0x144f, 0x878: 0x14cb, 0x879: 0x0baf, 0x87a: 0x0717, 0x87b: 0x0777,
	0x87c: 0x0a67, 0x87d: 0x0a87, 0x87e: 0x0caf, 0x87f: 0x0d73,
	// Block 0x22, offset 0x880
	0x880: 0x0ec3, 0x881: 0x0fcb, 0x882: 0x1277, 0x883: 0x1417, 0x884: 0x1623, 0x885: 0x0ce3,
	0x886: 0x14a3, 0x887: 0x0833, 0x888: 0x0d2f, 0x889: 0x0d3b, 0x88a: 0x0e0f, 0x88b: 0x0e47,
	0x88c: 0x0f4b, 0x88d: 0x0fa7, 0x88e: 0x1027, 0x88f: 0x110b, 0x890: 0x153b, 0x891: 0x07af,
	0x892: 0x0c03, 0x893: 0x14b3, 0x894: 0x0767, 0x895: 0x0aab, 0x896: 0x0e2f, 0x897: 0x13df,
	0x898: 0x0b67, 0x899: 0x0bb7, 0x89a: 0x0d43, 0x89b: 0x0f2f, 0x89c: 0x14bb, 0x89d: 0x0817,
	0x89e: 0x08ff, 0x89f: 0x0a97, 0x8a0: 0x0cd3, 0x8a1: 0x0d1f, 0x8a2: 0x0d5f, 0x8a3: 0x0df3,
	0x8a4: 0x0f47, 0x8a5: 0x0fbb, 0x8a6: 0x1157, 0x8a7: 0x12f7, 0x8a8: 0x1303, 0x8a9: 0x1457,
	0x8aa: 0x14d7, 0x8ab: 0x0883, 0x8ac: 0x0e4b, 0x8ad: 0x0903, 0x8ae: 0x0ec7, 0x8af: 0x0f6b,
	0x8b0: 0x1287, 0x8b1: 0x14bf, 0x8b2: 0x15ab, 0x8b3: 0x15d3, 0x8b4: 0x0d37, 0x8b5: 0x0e27,
	0x8b6: 0x11c3, 0x8b7: 0x10b7, 0x8b8: 0x10c3, 0x8b9: 0x10e7, 0x8ba: 0x0f17, 0x8bb: 0x0e9f,
	0x8bc: 0x1363, 0x8bd: 0x0733, 0x8be: 0x122b, 0x8bf: 0x081b,
	// Block 0x23, offset 0x8c0
	0x8c0: 0x080b, 0x8c1: 0x0b0b, 0x8c2: 0x0c2b, 0x8c3: 0x10f3, 0x8c4: 0x0a53, 0x8c5: 0x0e03,
	0x8c6: 0x0cef, 0x8c7: 0x13e7, 0x8c8: 0x12e7, 0x8c9: 0x14ab, 0x8ca: 0x1323, 0x8cb: 0x0b27,
	0x8cc: 0x0787, 0x8cd: 0x095b, 0x8d0: 0x09af,
	0x8d2: 0x0cdf, 0x8d5: 0x07f7, 0x8d6: 0x0f1f, 0x8d7: 0x0fe3,
	0x8d8: 0x1047, 0x8d9: 0x1063, 0x8da: 0x1067, 0x8db: 0x107b, 0x8dc: 0x14fb, 0x8dd: 0x10eb,
	0x8de: 0x116f, 0x8e0: 0x128f, 0x8e2: 0x1353,
	0x8e5: 0x1407, 0x8e6: 0x1433,
	0x8ea: 0x154f, 0x8eb: 0x1553, 0x8ec: 0x1557, 0x8ed: 0x15bb, 0x8ee: 0x142b, 0x8ef: 0x14c7,
	0x8f0: 0x0757, 0x8f1: 0x077b, 0x8f2: 0x078f, 0x8f3: 0x084b, 0x8f4: 0x0857, 0x8f5: 0x0897,
	0x8f6: 0x094b, 0x8f7: 0x0967, 0x8f8: 0x096f, 0x8f9: 0x09ab, 0x8fa: 0x09b7, 0x8fb: 0x0a93,
	0x8fc: 0x0a9b, 0x8fd: 0x0ba3, 0x8fe: 0x0bcb, 0x8ff: 0x0bd3,
	// Block 0x24, offset 0x900
	0x900: 0x0beb, 0x901: 0x0c97, 0x902: 0x0cc7, 0x903: 0x0ce7, 0x904: 0x0d57, 0x905: 0x0e1b,
	0x906: 0x0e37, 0x907: 0x0e67, 0x908: 0x0ebb, 0x909: 0x0edb, 0x90a: 0x0f4f, 0x90b: 0x102f,
	0x90c: 0x104b, 0x90d: 0x1053, 0x90e: 0x104f, 0x90f: 0x1057, 0x910: 0x105b, 0x911: 0x105f,
	0x912: 0x1073, 0x913: 0x1077, 0x914: 0x109b, 0x915: 0x10af, 0x916: 0x10cb, 0x917: 0x112f,
	0x918: 0x1137, 0x919: 0x113f, 0x91a: 0x1153, 0x91b: 0x117b, 0x91c: 0x11cb, 0x91d: 0x11ff,
	0x91e: 0x11ff, 0x91f: 0x1267, 0x920: 0x130f, 0x921: 0x1327, 0x922: 0x135b, 0x923: 0x135f,
	0x924: 0x13a3, 0x925: 0x13a7, 0x926: 0x13ff, 0x927: 0x1407, 0x928: 0x14db, 0x929: 0x151f,
	0x92a: 0x1537, 0x92b: 0x0b9b, 0x92c: 0x171e, 0x92d: 0x11e3,
	0x930: 0x06df, 0x931: 0x07e3, 0x932: 0x07a3, 0x933: 0x074b, 0x934: 0x078b, 0x935: 0x07b7,
	0x936: 0x0847, 0x937: 0x0863, 0x938: 0x094b, 0x939: 0x0937, 0x93a: 0x0947, 0x93b: 0x0963,
	0x93c: 0x09af, 0x93d: 0x09bf, 0x93e: 0x0a03, 0x93f: 0x0a0f,
	// Block 0x25, offset 0x940
	0x940: 0x0a2b, 0x941: 0x0a3b, 0x942: 0x0b23, 0x943: 0x0b2b, 0x944: 0x0b5b, 0x945: 0x0b7b,
	0x946: 0x0bab, 0x947: 0x0bc3, 0x948: 0x0bb3, 0x949: 0x0bd3, 0x94a: 0x0bc7, 0x94b: 0x0beb,
	0x94c: 0x0c07, 0x94d: 0x0c5f, 0x94e: 0x0c6b, 0x94f: 0x0c73, 0x950: 0x0c9b, 0x951: 0x0cdf,
	0x952: 0x0d0f, 0x953: 0x0d13, 0x954: 0x0d27, 0x955: 0x0da7, 0x956: 0x0db7, 0x957: 0x0e0f,
	0x958: 0x0e5b, 0x959: 0x0e53, 0x95a: 0x0e67, 0x95b: 0x0e83, 0x95c: 0x0ebb, 0x95d: 0x1013,
	0x95e: 0x0edf, 0x95f: 0x0f13, 0x960: 0x0f1f, 0x961: 0x0f5f, 0x962: 0x0f7b, 0x963: 0x0f9f,
	0x964: 0x0fc3, 0x965: 0x0fc7, 0x966: 0x0fe3, 0x967: 0x0fe7, 0x968: 0x0ff7, 0x969: 0x100b,
	0x96a: 0x1007, 0x96b: 0x1037, 0x96c: 0x10b3, 0x96d: 0x10cb, 0x96e: 0x10e3, 0x96f: 0x111b,
	0x970: 0x112f, 0x971: 0x114b, 0x972: 0x117b, 0x973: 0x122f, 0x974: 0x1257, 0x975: 0x12cb,
	0x976: 0x1313, 0x977: 0x131f, 0x978: 0x1327, 0x979: 0x133f, 0x97a: 0x1353, 0x97b: 0x1343,
	0x97c: 0x135b, 0x97d: 0x1357, 0x97e: 0x134f, 0x97f: 0x135f,
	// Block 0x26, offset 0x980
	0x980: 0x136b, 0x981: 0x13a7, 0x982: 0x13e3, 0x983: 0x1413, 0x984: 0x144b, 0x985: 0x146b,
	0x986: 0x14b7, 0x987: 0x14db, 0x988: 0x14fb, 0x989: 0x150f, 0x98a: 0x151f, 0x98b: 0x152b,
	0x98c: 0x1537, 0x98d: 0x158b, 0x98e: 0x162b, 0x98f: 0x16b5, 0x990: 0x16b0, 0x991: 0x16e2,
	0x992: 0x0607, 0x993: 0x062f, 0x994: 0x0633, 0x995: 0x1764, 0x996: 0x1791, 0x997: 0x1809,
	0x998: 0x1617, 0x999: 0x1627,
	// Block 0x27, offset 0x9c0
	0x9c0: 0x06fb, 0x9c1: 0x06f3, 0x9c2: 0x0703, 0x9c3: 0x1647, 0x9c4: 0x0747, 0x9c5: 0x0757,
	0x9c6: 0x075b, 0x9c7: 0x0763, 0x9c8: 0x076b, 0x9c9: 0x076f, 0x9ca: 0x077b, 0x9cb: 0x0773,
	0x9cc: 0x05b3, 0x9cd: 0x165b, 0x9ce: 0x078f, 0x9cf: 0x0793, 0x9d0: 0x0797, 0x9d1: 0x07b3,
	0x9d2: 0x164c, 0x9d3: 0x05b7, 0x9d4: 0x079f, 0x9d5: 0x07bf, 0x9d6: 0x1656, 0x9d7: 0x07cf,
	0x9d8: 0x07d7, 0x9d9: 0x0737, 0x9da: 0x07df, 0x9db: 0x07e3, 0x9dc: 0x1831, 0x9dd: 0x07ff,
	0x9de: 0x0807, 0x9df: 0x05bf, 0x9e0: 0x081f, 0x9e1: 0x0823, 0x9e2: 0x082b, 0x9e3: 0x082f,
	0x9e4: 0x05c3, 0x9e5: 0x0847, 0x9e6: 0x084b, 0x9e7: 0x0857, 0x9e8: 0x0863, 0x9e9: 0x0867,
	0x9ea: 0x086b, 0x9eb: 0x0873, 0x9ec: 0x0893, 0x9ed: 0x0897, 0x9ee: 0x089f, 0x9ef: 0x08af,
	0x9f0: 0x08b7, 0x9f1: 0x08bb, 0x9f2: 0x08bb, 0x9f3: 0x08bb, 0x9f4: 0x166a, 0x9f5: 0x0e93,
	0x9f6: 0x08cf, 0x9f7: 0x08d7, 0x9f8: 0x166f, 0x9f9: 0x08e3, 0x9fa: 0x08eb, 0x9fb: 0x08f3,
	0x9fc: 0x091b, 0x9fd: 0x0907, 0x9fe: 0x0913, 0x9ff: 0x0917,
	// Block 0x28, offset 0xa00
	0xa00: 0x091f, 0xa01: 0x0927, 0xa02: 0x092b, 0xa03: 0x0933, 0xa04: 0x093b, 0xa05: 0x093f,
	0xa06: 0x093f, 0xa07: 0x0947, 0xa08: 0x094f, 0xa09: 0x0953, 0xa0a: 0x095f, 0xa0b: 0x0983,
	0xa0c: 0x0967, 0xa0d: 0x0987, 0xa0e: 0x096b, 0xa0f: 0x0973, 0xa10: 0x080b, 0xa11: 0x09cf,
	0xa12: 0x0997, 0xa13: 0x099b, 0xa14: 0x099f, 0xa15: 0x0993, 0xa16: 0x09a7, 0xa17: 0x09a3,
	0xa18: 0x09bb, 0xa19: 0x1674, 0xa1a: 0x09d7, 0xa1b: 0x09db, 0xa1c: 0x09e3, 0xa1d: 0x09ef,
	0xa1e: 0x09f7, 0xa1f: 0x0a13, 0xa20: 0x1679, 0xa21: 0x167e, 0xa22: 0x0a1f, 0xa23: 0x0a23,
	0xa24: 0x0a27, 0xa25: 0x0a1b, 0xa26: 0x0a2f, 0xa27: 0x05c7, 0xa28: 0x05cb, 0xa29: 0x0a37,
	0xa2a: 0x0a3f, 0xa2b: 0x0a3f, 0xa2c: 0x1683, 0xa2d: 0x0a5b, 0xa2e: 0x0a5f, 0xa2f: 0x0a63,
	0xa30: 0x0a6b, 0xa31: 0x1688, 0xa32: 0x0a73, 0xa33: 0x0a77, 0xa34: 0x0b4f, 0xa35: 0x0a7f,
	0xa36: 0x05cf, 0xa37: 0x0a8b, 0xa38: 0x0a9b, 0xa39: 0x0aa7, 0xa3a: 0x0aa3, 0xa3b: 0x1692,
	0xa3c: 0x0aaf, 0xa3d: 0x1697, 0xa3e: 0x0abb, 0xa3f: 0x0ab7,
	// Block 0x29, offset 0xa40
	0xa40: 0x0abf, 0xa41: 0x0acf, 0xa42: 0x0ad3, 0xa43: 0x05d3, 0xa44: 0x0ae3, 0xa45: 0x0aeb,
	0xa46: 0x0aef, 0xa47: 0x0af3, 0xa48: 0x05d7, 0xa49: 0x169c, 0xa4a: 0x05db, 0xa4b: 0x0b0f,
	0xa4c: 0x0b13, 0xa4d: 0x0b17, 0xa4e: 0x0b1f, 0xa4f: 0x1863, 0xa50: 0x0b37, 0xa51: 0x16a6,
	0xa52: 0x16a6, 0xa53: 0x11d7, 0xa54: 0x0b47, 0xa55: 0x0b47, 0xa56: 0x05df, 0xa57: 0x16c9,
	0xa58: 0x179b, 0xa59: 0x0b57, 0xa5a: 0x0b5f, 0xa5b: 0x05e3, 0xa5c: 0x0b73, 0xa5d: 0x0b83,
	0xa5e: 0x0b87, 0xa5f: 0x0b8f, 0xa60: 0x0b9f, 0xa61: 0x05eb, 0xa62: 0x05e7, 0xa63: 0x0ba3,
	0xa64: 0x16ab, 0xa65: 0x0ba7, 0xa66: 0x0bbb, 0xa67: 0x0bbf, 0xa68: 0x0bc3, 0xa69: 0x0bbf,
	0xa6a: 0x0bcf, 0xa6b: 0x0bd3, 0xa6c: 0x0be3, 0xa6d: 0x0bdb, 0xa6e: 0x0bdf, 0xa6f: 0x0be7,
	0xa70: 0x0beb, 0xa71: 0x0bef, 0xa72: 0x0bfb, 0xa73: 0x0bff, 0xa74: 0x0c17, 0xa75: 0x0c1f,
	0xa76: 0x0c2f, 0xa77: 0x0c43, 0xa78: 0x16ba, 0xa79: 0x0c3f, 0xa7a: 0x0c33, 0xa7b: 0x0c4b,
	0xa7c: 0x0c53, 0xa7d: 0x0c67, 0xa7e: 0x16bf, 0xa7f: 0x0c6f,
	// Block 0x2a, offset 0xa80
	0xa80: 0x0c63, 0xa81: 0x0c5b, 0xa82: 0x05ef, 0xa83: 0x0c77, 0xa84: 0x0c7f, 0xa85: 0x0c87,
	0xa86: 0x0c7b, 0xa87: 0x05f3, 0xa88: 0x0c97, 0xa89: 0x0c9f, 0xa8a: 0x16c4, 0xa8b: 0x0ccb,
	0xa8c: 0x0cff, 0xa8d: 0x0cdb, 0xa8e: 0x05ff, 0xa8f: 0x0ce7, 0xa90: 0x05fb, 0xa91: 0x05f7,
	0xa92: 0x07c3, 0xa93: 0x07c7, 0xa94: 0x0d03, 0xa95: 0x0ceb, 0xa96: 0x11ab, 0xa97: 0x0663,
	0xa98: 0x0d0f, 0xa99: 0x0d13, 0xa9a: 0x0d17, 0xa9b: 0x0d2b, 0xa9c: 0x0d23, 0xa9d: 0x16dd,
	0xa9e: 0x0603, 0xa9f: 0x0d3f, 0xaa0: 0x0d33, 0xaa1: 0x0d4f, 0xaa2: 0x0d57, 0xaa3: 0x16e7,
	0xaa4: 0x0d5b, 0xaa5: 0x0d47, 0xaa6: 0x0d63, 0xaa7: 0x0607, 0xaa8: 0x0d67, 0xaa9: 0x0d6b,
	0xaaa: 0x0d6f, 0xaab: 0x0d7b, 0xaac: 0x16ec, 0xaad: 0x0d83, 0xaae: 0x060b, 0xaaf: 0x0d8f,
	0xab0: 0x16f1, 0xab1: 0x0d93, 0xab2: 0x060f, 0xab3: 0x0d9f, 0xab4: 0x0dab, 0xab5: 0x0db7,
	0xab6: 0x0dbb, 0xab7: 0x16f6, 0xab8: 0x168d, 0xab9: 0x16fb, 0xaba: 0x0ddb, 0xabb: 0x1700,
	0xabc: 0x0de7, 0xabd: 0x0def, 0xabe: 0x0ddf, 0xabf: 0x0dfb,
	// Block 0x2b, offset 0xac0
	0xac0: 0x0e0b, 0xac1: 0x0e1b, 0xac2: 0x0e0f, 0xac3: 0x0e13, 0xac4: 0x0e1f, 0xac5: 0x0e23,
	0xac6: 0x1705, 0xac7: 0x0e07, 0xac8: 0x0e3b, 0xac9: 0x0e3f, 0xaca: 0x0613, 0xacb: 0x0e53,
	0xacc: 0x0e4f, 0xacd: 0x170a, 0xace: 0x0e33, 0xacf: 0x0e6f, 0xad0: 0x170f, 0xad1: 0x1714,
	0xad2: 0x0e73, 0xad3: 0x0e87, 0xad4: 0x0e83, 0xad5: 0x0e7f, 0xad6: 0x0617, 0xad7: 0x0e8b,
	0xad8: 0x0e9b, 0xad9: 0x0e97, 0xada: 0x0ea3, 0xadb: 0x1651, 0xadc: 0x0eb3, 0xadd: 0x1719,
	0xade: 0x0ebf, 0xadf: 0x1723, 0xae0: 0x0ed3, 0xae1: 0x0edf, 0xae2: 0x0ef3, 0xae3: 0x1728,
	0xae4: 0x0f07, 0xae5: 0x0f0b, 0xae6: 0x172d, 0xae7: 0x1732, 0xae8: 0x0f27, 0xae9: 0x0f37,
	0xaea: 0x061b, 0xaeb: 0x0f3b, 0xaec: 0x061f, 0xaed: 0x061f, 0xaee: 0x0f53, 0xaef: 0x0f57,
	0xaf0: 0x0f5f, 0xaf1: 0x0f63, 0xaf2: 0x0f6f, 0xaf3: 0x0623, 0xaf4: 0x0f87, 0xaf5: 0x1737,
	0xaf6: 0x0fa3, 0xaf7: 0x173c, 0xaf8: 0x0faf, 0xaf9: 0x16a1, 0xafa: 0x0fbf, 0xafb: 0x1741,
	0xafc: 0x1746, 0xafd: 0x174b, 0xafe: 0x0627, 0xaff: 0x062b,
	// Block 0x2c, offset 0xb00
	0xb00: 0x0ff7, 0xb01: 0x1755, 0xb02: 0x1750, 0xb03: 0x175a, 0xb04: 0x175f, 0xb05: 0x0fff,
	0xb06: 0x1003, 0xb07: 0x1003, 0xb08: 0x100b, 0xb09: 0x0633, 0xb0a: 0x100f, 0xb0b: 0x0637,
	0xb0c: 0x063b, 0xb0d: 0x1769, 0xb0e: 0x1023, 0xb0f: 0x102b, 0xb10: 0x1037, 0xb11: 0x063f,
	0xb12: 0x176e, 0xb13: 0x105b, 0xb14: 0x1773, 0xb15: 0x1778, 0xb16: 0x107b, 0xb17: 0x1093,
	0xb18: 0x0643, 0xb19: 0x109b, 0xb1a: 0x109f, 0xb1b: 0x10a3, 0xb1c: 0x177d, 0xb1d: 0x1782,
	0xb1e: 0x1782, 0xb1f: 0x10bb, 0xb20: 0x0647, 0xb21: 0x1787, 0xb22: 0x10cf, 0xb23: 0x10d3,
	0xb24: 0x064b, 0xb25: 0x178c, 0xb26: 0x10ef, 0xb27: 0x064f, 0xb28: 0x10ff, 0xb29: 0x10f7,
	0xb2a: 0x1107, 0xb2b: 0x1796, 0xb2c: 0x111f, 0xb2d: 0x0653, 0xb2e: 0x112b, 0xb2f: 0x1133,
	0xb30: 0x1143, 0xb31: 0x0657, 0xb32: 0x17a0, 0xb33: 0x17a5, 0xb34: 0x065b, 0xb35: 0x17aa,
	0xb36: 0x115b, 0xb37: 0x17af, 0xb38: 0x1167, 0xb39: 0x1173, 0xb3a: 0x117b, 0xb3b: 0x17b4,
	0xb3c: 0x17b9, 0xb3d: 0x118f, 0xb3e: 0x17be, 0xb3f: 0x1197,
	// Block 0x2d, offset 0xb40
	0xb40: 0x16ce, 0xb41: 0x065f, 0xb42: 0x11af, 0xb43: 0x11b3, 0xb44: 0x0667, 0xb45: 0x11b7,
	0xb46: 0x0a33, 0xb47: 0x17c3, 0xb48: 0x17c8, 0xb49: 0x16d3, 0xb4a: 0x16d8, 0xb4b: 0x11d7,
	0xb4c: 0x11db, 0xb4d: 0x13f3, 0xb4e: 0x066b, 0xb4f: 0x1207, 0xb50: 0x1203, 0xb51: 0x120b,
	0xb52: 0x083f, 0xb53: 0x120f, 0xb54: 0x1213, 0xb55: 0x1217, 0xb56: 0x121f, 0xb57: 0x17cd,
	0xb58: 0x121b, 0xb59: 0x1223, 0xb5a: 0x1237, 0xb5b: 0x123b, 0xb5c: 0x1227, 0xb5d: 0x123f,
	0xb5e: 0x1253, 0xb5f: 0x1267, 0xb60: 0x1233, 0xb61: 0x1247, 0xb62: 0x124b, 0xb63: 0x124f,
	0xb64: 0x17d2, 0xb65: 0x17dc, 0xb66: 0x17d7, 0xb67: 0x066f, 0xb68: 0x126f, 0xb69: 0x1273,
	0xb6a: 0x127b, 0xb6b: 0x17f0, 0xb6c: 0x127f, 0xb6d: 0x17e1, 0xb6e: 0x0673, 0xb6f: 0x0677,
	0xb70: 0x17e6, 0xb71: 0x17eb, 0xb72: 0x067b, 0xb73: 0x129f, 0xb74: 0x12a3, 0xb75: 0x12a7,
	0xb76: 0x12ab, 0xb77: 0x12b7, 0xb78: 0x12b3, 0xb79: 0x12bf, 0xb7a: 0x12bb, 0xb7b: 0x12cb,
	0xb7c: 0x12c3, 0xb7d: 0x12c7, 0xb7e: 0x12cf, 0xb7f: 0x067f,
	// Block 0x2e, offset 0xb80
	0xb80: 0x12d7, 0xb81: 0x12db, 0xb82: 0x0683, 0xb83: 0x12eb, 0xb84: 0x12ef, 0xb85: 0x17f5,
	0xb86: 0x12fb, 0xb87: 0x12ff, 0xb88: 0x0687, 0xb89: 0x130b, 0xb8a: 0x05bb, 0xb8b: 0x17fa,
	0xb8c: 0x17ff, 0xb8d: 0x068b, 0xb8e: 0x068f, 0xb8f: 0x1337, 0xb90: 0x134f, 0xb91: 0x136b,
	0xb92: 0x137b, 0xb93: 0x1804, 0xb94: 0x138f, 0xb95: 0x1393, 0xb96: 0x13ab, 0xb97: 0x13b7,
	0xb98: 0x180e, 0xb99: 0x1660, 0xb9a: 0x13c3, 0xb9b: 0x13bf, 0xb9c: 0x13cb, 0xb9d: 0x1665,
	0xb9e: 0x13d7, 0xb9f: 0x13e3, 0xba0: 0x1813, 0xba1: 0x1818, 0xba2: 0x1423, 0xba3: 0x142f,
	0xba4: 0x1437, 0xba5: 0x181d, 0xba6: 0x143b, 0xba7: 0x1467, 0xba8: 0x1473, 0xba9: 0x1477,
	0xbaa: 0x146f, 0xbab: 0x1483, 0xbac: 0x1487, 0xbad: 0x1822, 0xbae: 0x1493, 0xbaf: 0x0693,
	0xbb0: 0x149b, 0xbb1: 0x1827, 0xbb2: 0x0697, 0xbb3: 0x14d3, 0xbb4: 0x0ac3, 0xbb5: 0x14eb,
	0xbb6: 0x182c, 0xbb7: 0x1836, 0xbb8: 0x069b, 0xbb9: 0x069f, 0xbba: 0x1513, 0xbbb: 0x183b,
	0xbbc: 0x06a3, 0xbbd: 0x1840, 0xbbe: 0x152b, 0xbbf: 0x152b,
	// Block 0x2f, offset 0xbc0
	0xbc0: 0x1533, 0xbc1: 0x1845, 0xbc2: 0x154b, 0xbc3: 0x06a7, 0xbc4: 0x155b, 0xbc5: 0x1567,
	0xbc6: 0x156f, 0xbc7: 0x1577, 0xbc8: 0x06ab, 0xbc9: 0x184a, 0xbca: 0x158b, 0xbcb: 0x15a7,
	0xbcc: 0x15b3, 0xbcd: 0x06af, 0xbce: 0x06b3, 0xbcf: 0x15b7, 0xbd0: 0x184f, 0xbd1: 0x06b7,
	0xbd2: 0x1854, 0xbd3: 0x1859, 0xbd4: 0x185e, 0xbd5: 0x15db, 0xbd6: 0x06bb, 0xbd7: 0x15ef,
	0xbd8: 0x15f7, 0xbd9: 0x15fb, 0xbda: 0x1603, 0xbdb: 0x160b, 0xbdc: 0x1613, 0xbdd: 0x1868,
}

// nfcIndex: 22 blocks, 1408 entries, 1408 bytes
// Block 0 is the zero block.
var nfcIndex = [1408]uint8{
	// Block 0x0, offset 0x0
	// Block 0x1, offset 0x40
	// Block 0x2, offset 0x80
	// Block 0x3, offset 0xc0
	0xc2: 0x2e, 0xc3: 0x01, 0xc4: 0x02, 0xc5: 0x03, 0xc6: 0x2f, 0xc7: 0x04,
	0xc8: 0x05, 0xca: 0x30, 0xcb: 0x31, 0xcc: 0x06, 0xcd: 0x07, 0xce: 0x08, 0xcf: 0x32,
	0xd0: 0x09, 0xd1: 0x33, 0xd2: 0x34, 0xd3: 0x0a, 0xd6: 0x0b, 0xd7: 0x35,
	0xd8: 0x36, 0xd9: 0x0c, 0xdb: 0x37, 0xdc: 0x38, 0xdd: 0x39, 0xdf: 0x3a,
	0xe0: 0x02, 0xe1: 0x03, 0xe2: 0x04, 0xe3: 0x05,
	0xea: 0x06, 0xeb: 0x07, 0xec: 0x08, 0xed: 0x09, 0xef: 0x0a,
	0xf0: 0x13,
	// Block 0x4, offset 0x100
	0x120: 0x3b, 0x121: 0x3c, 0x123: 0x0d, 0x124: 0x3d, 0x125: 0x3e, 0x126: 0x3f, 0x127: 0x40,
	0x128: 0x41, 0x129: 0x42, 0x12a: 0x43, 0x12b: 0x44, 0x12c: 0x3f, 0x12d: 0x45, 0x12e: 0x46, 0x12f: 0x47,
	0x131: 0x48, 0x132: 0x49, 0x133: 0x4a, 0x134: 0x4b, 0x135: 0x4c, 0x137: 0x4d,
	0x138: 0x4e, 0x139: 0x4f, 0x13a: 0x50, 0x13b: 0x51, 0x13c: 0x52, 0x13d: 0x53, 0x13e: 0x54, 0x13f: 0x55,
	// Block 0x5, offset 0x140
	0x140: 0x56, 0x142: 0x57, 0x144: 0x58, 0x145: 0x59, 0x146: 0x5a, 0x147: 0x5b,
	0x14d: 0x5c,
	0x15c: 0x5d, 0x15f: 0x5e,
	0x162: 0x5f, 0x164: 0x60,
	0x168: 0x61, 0x169: 0x62, 0x16a: 0x63, 0x16c: 0x0e, 0x16d: 0x64, 0x16e: 0x65, 0x16f: 0x66,
	0x170: 0x67, 0x173: 0x68, 0x177: 0x0f,
	0x178: 0x10, 0x179: 0x11, 0x17a: 0x12, 0x17b: 0x13, 0x17c: 0x14, 0x17d: 0x15, 0x17e: 0x16, 0x17f: 0x17,
	// Block 0x6, offset 0x180
	0x180: 0x69, 0x183: 0x6a, 0x184: 0x6b, 0x186: 0x6c, 0x187: 0x6d,
	0x188: 0x6e, 0x189: 0x18, 0x18a: 0x19, 0x18b: 0x6f, 0x18c: 0x70,
	0x1ab: 0x71,
	0x1b3: 0x72, 0x1b5: 0x73, 0x1b7: 0x74,
	// Block 0x7, offset 0x1c0
	0x1c0: 0x75, 0x1c1: 0x1a, 0x1c2: 0x1b, 0x1c3: 0x1c, 0x1c4: 0x76, 0x1c5: 0x77,
	0x1c9: 0x78, 0x1cc: 0x79, 0x1cd: 0x7a,
	// Block 0x8, offset 0x200
	0x219: 0x7b, 0x21a: 0x7c, 0x21b: 0x7d,
	0x220: 0x7e, 0x223: 0x7f, 0x224: 0x80, 0x225: 0x81, 0x226: 0x82, 0x227: 0x83,
	0x22a: 0x84, 0x22b: 0x85, 0x22f: 0x86,
	0x230: 0x87, 0x231: 0x88, 0x232: 0x89, 0x233: 0x8a, 0x234: 0x8b, 0x235: 0x8c, 0x236: 0x8d, 0x237: 0x87,
	0x238: 0x88, 0x239: 0x89, 0x23a: 0x8a, 0x23b: 0x8b, 0x23c: 0x8c, 0x23d: 0x8d, 0x23e: 0x87, 0x23f: 0x88,
	// Block 0x9, offset 0x240
	0x240: 0x89, 0x241: 0x8a, 0x242: 0x8b, 0x243: 0x8c, 0x244: 0x8d, 0x245: 0x87, 0x246: 0x88, 0x247: 0x89,
	0x248: 0x8a, 0x249: 0x8b, 0x24a: 0x8c, 0x24b: 0x8d, 0x24c: 0x87, 0x24d: 0x88, 0x24e: 0x89, 0x24f: 0x8a,
	0x250: 0x8b, 0x251: 0x8c, 0x252: 0x8d, 0x253: 0x87, 0x254: 0x88, 0x255: 0x89, 0x256: 0x8a, 0x257: 0x8b,
	0x258: 0x8c, 0x259: 0x8d, 0x25a: 0x87, 0x25b: 0x88, 0x25c: 0x89, 0x25d: 0x8a, 0x25e: 0x8b, 0x25f: 0x8c,
	0x260: 0x8d, 0x261: 0x87, 0x262: 0x88, 0x263: 0x89, 0x264: 0x8a, 0x265: 0x8b, 0x266: 0x8c, 0x267: 0x8d,
	0x268: 0x87, 0x269: 0x88, 0x26a: 0x89, 0x26b: 0x8a, 0x26c: 0x8b, 0x26d: 0x8c, 0x26e: 0x8d, 0x26f: 0x87,
	0x270: 0x88, 0x271: 0x89, 0x272: 0x8a, 0x273: 0x8b, 0x274: 0x8c, 0x275: 0x8d, 0x276: 0x87, 0x277: 0x88,
	0x278: 0x89, 0x279: 0x8a, 0x27a: 0x8b, 0x27b: 0x8c, 0x27c: 0x8d, 0x27d: 0x87, 0x27e: 0x88, 0x27f: 0x89,
	// Block 0xa, offset 0x280
	0x280: 0x8a, 0x281: 0x8b, 0x282: 0x8c, 0x283: 0x8d, 0x284: 0x87, 0x285: 0x88, 0x286: 0x89, 0x287: 0x8a,
	0x288: 0x8b, 0x289: 0x8c, 0x28a: 0x8d, 0x28b: 0x87, 0x28c: 0x88, 0x28d: 0x89, 0x28e: 0x8a, 0x28f: 0x8b,
	0x290: 0x8c, 0x291: 0x8d, 0x292: 0x87, 0x293: 0x88, 0x294: 0x89, 0x295: 0x8a, 0x296: 0x8b, 0x297: 0x8c,
	0x298: 0x8d, 0x299: 0x87, 0x29a: 0x88, 0x29b: 0x89, 0x29c: 0x8a, 0x29d: 0x8b, 0x29e: 0x8c, 0x29f: 0x8d,
	0x2a0: 0x87, 0x2a1: 0x88, 0x2a2: 0x89, 0x2a3: 0x8a, 0x2a4: 0x8b, 0x2a5: 0x8c, 0x2a6: 0x8d, 0x2a7: 0x87,
	0x2a8: 0x88, 0x2a9: 0x89, 0x2aa: 0x8a, 0x2ab: 0x8b, 0x2ac: 0x8c, 0x2ad: 0x8d, 0x2ae: 0x87, 0x2af: 0x88,
	0x2b0: 0x89, 0x2b1: 0x8a, 0x2b2: 0x8b, 0x2b3: 0x8c, 0x2b4: 0x8d, 0x2b5: 0x87, 0x2b6: 0x88, 0x2b7: 0x89,
	0x2b8: 0x8a, 0x2b9: 0x8b, 0x2ba: 0x8c, 0x2bb: 0x8d, 0x2bc: 0x87, 0x2bd: 0x88, 0x2be: 0x89, 0x2bf: 0x8a,
	// Block 0xb, offset 0x2c0
	0x2c0: 0x8b, 0x2c1: 0x8c, 0x2c2: 0x8d, 0x2c3: 0x87, 0x2c4: 0x88, 0x2c5: 0x89, 0x2c6: 0x8a, 0x2c7: 0x8b,
	0x2c8: 0x8c, 0x2c9: 0x8d, 0x2ca: 0x87, 0x2cb: 0x88, 0x2cc: 0x89, 0x2cd: 0x8a, 0x2ce: 0x8b, 0x2cf: 0x8c,
	0x2d0: 0x8d, 0x2d1: 0x87, 0x2d2: 0x88, 0x2d3: 0x89, 0x2d4: 0x8a, 0x2d5: 0x8b, 0x2d6: 0x8c, 0x2d7: 0x8d,
	0x2d8: 0x87, 0x2d9: 0x88, 0x2da: 0x89, 0x2db: 0x8a, 0x2dc: 0x8b, 0x2dd: 0x8c, 0x2de: 0x8e,
	// Block 0xc, offset 0x300
	0x324: 0x1d, 0x325: 0x1e, 0x326: 0x1f, 0x327: 0x20,
	0x328: 0x21, 0x329: 0x22, 0x32a: 0x23, 0x32b: 0x24, 0x32c: 0x8f, 0x32d: 0x90, 0x32e: 0x91,
	0x331: 0x92, 0x332: 0x93, 0x333: 0x94, 0x334: 0x95,
	0x338: 0x96, 0x339: 0x97, 0x33a: 0x98, 0x33b: 0x99, 0x33e: 0x9a, 0x33f: 0x9b,
	// Block 0xd, offset 0x340
	0x347: 0x9c,
	0x34b: 0x9d, 0x34d: 0x9e,
	0x368: 0x9f, 0x36b: 0xa0,
	0x374: 0xa1,
	0x37d: 0xa2,
	// Block 0xe, offset 0x380
	0x381: 0xa3, 0x382: 0xa4, 0x384: 0xa5, 0x385: 0x82, 0x387: 0xa6,
	0x388: 0xa7, 0x38b: 0xa8, 0x38c: 0xa9, 0x38d: 0xaa,
	0x391: 0xab, 0x392: 0xac, 0x393: 0xad, 0x396: 0xae, 0x397: 0xaf,
	0x398: 0x73, 0x39a: 0xb0, 0x39c: 0xb1,
	0x3a0: 0xb2, 0x3a7: 0xb3,
	0x3a8: 0xb4, 0x3a9: 0xb5, 0x3aa: 0xb6,
	0x3b0: 0x73, 0x3b5: 0xb7, 0x3b6: 0xb8,
	// Block 0xf, offset 0x3c0
	0x3eb: 0xb9, 0x3ec: 0xba,
	// Block 0x10, offset 0x400
	0x432: 0xbb,
	// Block 0x11, offset 0x440
	0x445: 0xbc, 0x446: 0xbd, 0x447: 0xbe,
	0x449: 0xbf,
	// Block 0x12, offset 0x480
	0x480: 0xc0, 0x484: 0xba,
	0x48b: 0xc1,
	0x4a3: 0xc2, 0x4a5: 0xc3,
	// Block 0x13, offset 0x4c0
	0x4c8: 0xc4,
	// Block 0x14, offset 0x500
	0x520: 0x25, 0x521: 0x26, 0x522: 0x27, 0x523: 0x28, 0x524: 0x29, 0x525: 0x2a, 0x526: 0x2b, 0x527: 0x2c,
	0x528: 0x2d,
	// Block 0x15, offset 0x540
	0x550: 0x0b, 0x551: 0x0c, 0x556: 0x0d,
	0x55b: 0x0e, 0x55d: 0x0f, 0x55e: 0x10, 0x55f: 0x11,
	0x56f: 0x12,
}

// nfcSparseOffset: 151 entries, 302 bytes
var nfcSparseOffset = []uint16{0x0, 0x5, 0x9, 0xb, 0xd, 0x18, 0x28, 0x2a, 0x2f, 0x3a, 0x49, 0x56, 0x5e, 0x63, 0x68, 0x6a, 0x72, 0x79, 0x7c, 0x84, 0x88, 0x8c, 0x8e, 0x90, 0x99, 0x9d, 0xa4, 0xa9, 0xac, 0xb6, 0xb9, 0xc0, 0xc8, 0xcb, 0xcd, 0xd0, 0xd2, 0xd7, 0xe8, 0xf4, 0xf6, 0xfc, 0xfe, 0x100, 0x102, 0x104, 0x106, 0x108, 0x10b, 0x10e, 0x110, 0x113, 0x116, 0x11a, 0x11f, 0x128, 0x12a, 0x12d, 0x12f, 0x13a, 0x13e, 0x14c, 0x14f, 0x155, 0x15b, 0x166, 0x16a, 0x16c, 0x16e, 0x170, 0x172, 0x174, 0x17a, 0x17e, 0x180, 0x182, 0x18a, 0x18e, 0x191, 0x193, 0x195, 0x197, 0x19a, 0x19c, 0x19e, 0x1a0, 0x1a2, 0x1a8, 0x1ab, 0x1ad, 0x1b4, 0x1ba, 0x1c0, 0x1c8, 0x1ce, 0x1d4, 0x1da, 0x1de, 0x1ec, 0x1f5, 0x1f8, 0x1fb, 0x1fd, 0x200, 0x202, 0x206, 0x20b, 0x20d, 0x20f, 0x214, 0x21a, 0x21c, 0x21e, 0x220, 0x226, 0x229, 0x22b, 0x231, 0x234, 0x23c, 0x243, 0x246, 0x249, 0x24b, 0x24e, 0x256, 0x25a, 0x261, 0x264, 0x26a, 0x26c, 0x26f, 0x271, 0x274, 0x276, 0x278, 0x27a, 0x27c, 0x27f, 0x281, 0x283, 0x285, 0x287, 0x294, 0x29e, 0x2a0, 0x2a2, 0x2a8, 0x2aa, 0x2ac, 0x2af}

// nfcSparseValues: 689 entries, 2756 bytes
var nfcSparseValues = [689]valueRange{
	// Block 0x0, offset 0x0
	{value: 0x0000, lo: 0x04},
	{value: 0xa100, lo: 0xa8, hi: 0xa8},
	{value: 0x8100, lo: 0xaf, hi: 0xaf},
	{value: 0x8100, lo: 0xb4, hi: 0xb4},
	{value: 0x8100, lo: 0xb8, hi: 0xb8},
	// Block 0x1, offset 0x5
	{value: 0x0091, lo: 0x03},
	{value: 0x46e5, lo: 0xa0, hi: 0xa1},
	{value: 0x4717, lo: 0xaf, hi: 0xb0},
	{value: 0xa000, lo: 0xb7, hi: 0xb7},
	// Block 0x2, offset 0x9
	{value: 0x0000, lo: 0x01},
	{value: 0xa000, lo: 0x92, hi: 0x92},
	// Block 0x3, offset 0xb
	{value: 0x0000, lo: 0x01},
	{value: 0x8100, lo: 0x98, hi: 0x9d},
	// Block 0x4, offset 0xd
	{value: 0x0006, lo: 0x0a},
	{value: 0xa000, lo: 0x81, hi: 0x81},
	{value: 0xa000, lo: 0x85, hi: 0x85},
	{value: 0xa000, lo: 0x89, hi: 0x89},
	{value: 0x4843, lo: 0x8a, hi: 0x8a},
	{value: 0x4861, lo: 0x8b, hi: 0x8b},
	{value: 0x36ca, lo: 0x8c, hi: 0x8c},
	{value: 0x36e2, lo: 0x8d, hi: 0x8d},
	{value: 0x4879, lo: 0x8e, hi: 0x8e},
	{value: 0xa000, lo: 0x92, hi: 0x92},
	{value: 0x3700, lo: 0x93, hi: 0x94},
	// Block 0x5, offset 0x18
	{value: 0x0000, lo: 0x0f},
	{value: 0xa000, lo: 0x83, hi: 0x83},
	{value: 0xa000, lo: 0x87, hi: 0x87},
	{value: 0xa000, lo: 0x8b, hi: 0x8b},
	{value: 0xa000, lo: 0x8d, hi: 0x8d},
	{value: 0x37a8, lo: 0x90, hi: 0x90},
	{value: 0x37b4, lo: 0x91, hi: 0x91},
	{value: 0x37a2, lo: 0x93, hi: 0x93},
	{value: 0xa000, lo: 0x96, hi: 0x96},
	{value: 0x381a, lo: 0x97, hi: 0x97},
	{value: 0x37e4, lo: 0x9c, hi: 0x9c},
	{value: 0x37cc, lo: 0x9d, hi: 0x9d},
	{value: 0x37f6, lo: 0x9e, hi: 0x9e},
	{value: 0xa000, lo: 0xb4, hi: 0xb5},
	{value: 0x3820, lo: 0xb6, hi: 0xb6},
	{value: 0x3826, lo: 0xb7, hi: 0xb7},
	// Block 0x6, offset 0x28
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0x83, hi: 0x87},
	// Block 0x7, offset 0x2a
	{value: 0x0001, lo: 0x04},
	{value: 0x8113, lo: 0x81, hi: 0x82},
	{value: 0x8132, lo: 0x84, hi: 0x84},
	{value: 0x812d, lo: 0x85, hi: 0x85},
	{value: 0x810d, lo: 0x87, hi: 0x87},
	// Block 0x8, offset 0x2f
	{value: 0x0000, lo: 0x0a},
	{value: 0x8132, lo: 0x90, hi: 0x97},
	{value: 0x8119, lo: 0x98, hi: 0x98},
	{value: 0x811a, lo: 0x99, hi: 0x99},
	{value: 0x811b, lo: 0x9a, hi: 0x9a},
	{value: 0x3844, lo: 0xa2, hi: 0xa2},
	{value: 0x384a, lo: 0xa3, hi: 0xa3},
	{value: 0x3856, lo: 0xa4, hi: 0xa4},
	{value: 0x3850, lo: 0xa5, hi: 0xa5},
	{value: 0x385c, lo: 0xa6, hi: 0xa6},
	{value: 0xa000, lo: 0xa7, hi: 0xa7},
	// Block 0x9, offset 0x3a
	{value: 0x0000, lo: 0x0e},
	{value: 0x386e, lo: 0x80, hi: 0x80},
	{value: 0xa000, lo: 0x81, hi: 0x81},
	{value: 0x3862, lo: 0x82, hi: 0x82},
	{value: 0xa000, lo: 0x92, hi: 0x92},
	{value: 0x3868, lo: 0x93, hi: 0x93},
	{value: 0xa000, lo: 0x95, hi: 0x95},
	{value: 0x8132, lo: 0x96, hi: 0x9c},
	{value: 0x8132, lo: 0x9f, hi: 0xa2},
	{value: 0x812d, lo: 0xa3, hi: 0xa3},
	{value: 0x8132, lo: 0xa4, hi: 0xa4},
	{value: 0x8132, lo: 0xa7, hi: 0xa8},
	{value: 0x812d, lo: 0xaa, hi: 0xaa},
	{value: 0x8132, lo: 0xab, hi: 0xac},
	{value: 0x812d, lo: 0xad, hi: 0xad},
	// Block 0xa, offset 0x49
	{value: 0x0000, lo: 0x0c},
	{value: 0x811f, lo: 0x91, hi: 0x91},
	{value: 0x8132, lo: 0xb0, hi: 0xb0},
	{value: 0x812d, lo: 0xb1, hi: 0xb1},
	{value: 0x8132, lo: 0xb2, hi: 0xb3},
	{value: 0x812d, lo: 0xb4, hi: 0xb4},
	{value: 0x8132, lo: 0xb5, hi: 0xb6},
	{value: 0x812d, lo: 0xb7, hi: 0xb9},
	{value: 0x8132, lo: 0xba, hi: 0xba},
	{value: 0x812d, lo: 0xbb, hi: 0xbc},
	{value: 0x8132, lo: 0xbd, hi: 0xbd},
	{value: 0x812d, lo: 0xbe, hi: 0xbe},
	{value: 0x8132, lo: 0xbf, hi: 0xbf},
	// Block 0xb, offset 0x56
	{value: 0x0005, lo: 0x07},
	{value: 0x8132, lo: 0x80, hi: 0x80},
	{value: 0x8132, lo: 0x81, hi: 0x81},
	{value: 0x812d, lo: 0x82, hi: 0x83},
	{value: 0x812d, lo: 0x84, hi: 0x85},
	{value: 0x812d, lo: 0x86, hi: 0x87},
	{value: 0x812d, lo: 0x88, hi: 0x89},
	{value: 0x8132, lo: 0x8a, hi: 0x8a},
	// Block 0xc, offset 0x5e
	{value: 0x0000, lo: 0x04},
	{value: 0x8132, lo: 0xab, hi: 0xb1},
	{value: 0x812d, lo: 0xb2, hi: 0xb2},
	{value: 0x8132, lo: 0xb3, hi: 0xb3},
	{value: 0x812d, lo: 0xbd, hi: 0xbd},
	// Block 0xd, offset 0x63
	{value: 0x0000, lo: 0x04},
	{value: 0x8132, lo: 0x96, hi: 0x99},
	{value: 0x8132, lo: 0x9b, hi: 0xa3},
	{value: 0x8132, lo: 0xa5, hi: 0xa7},
	{value: 0x8132, lo: 0xa9, hi: 0xad},
	// Block 0xe, offset 0x68
	{value: 0x0000, lo: 0x01},
	{value: 0x812d, lo: 0x99, hi: 0x9b},
	// Block 0xf, offset 0x6a
	{value: 0x0000, lo: 0x07},
	{value: 0xa000, lo: 0xa8, hi: 0xa8},
	{value: 0x3edb, lo: 0xa9, hi: 0xa9},
	{value: 0xa000, lo: 0xb0, hi: 0xb0},
	{value: 0x3ee3, lo: 0xb1, hi: 0xb1},
	{value: 0xa000, lo: 0xb3, hi: 0xb3},
	{value: 0x3eeb, lo: 0xb4, hi: 0xb4},
	{value: 0x9902, lo: 0xbc, hi: 0xbc},
	// Block 0x10, offset 0x72
	{value: 0x0008, lo: 0x06},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x8132, lo: 0x91, hi: 0x91},
	{value: 0x812d, lo: 0x92, hi: 0x92},
	{value: 0x8132, lo: 0x93, hi: 0x93},
	{value: 0x8132, lo: 0x94, hi: 0x94},
	{value: 0x451f, lo: 0x98, hi: 0x9f},
	// Block 0x11, offset 0x79
	{value: 0x0000, lo: 0x02},
	{value: 0x8102, lo: 0xbc, hi: 0xbc},
	{value: 0x9900, lo: 0xbe, hi: 0xbe},
	// Block 0x12, offset 0x7c
	{value: 0x0008, lo: 0x07},
	{value: 0xa000, lo: 0x87, hi: 0x87},
	{value: 0x2ca1, lo: 0x8b, hi: 0x8c},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x9900, lo: 0x97, hi: 0x97},
	{value: 0x455f, lo: 0x9c, hi: 0x9d},
	{value: 0x456f, lo: 0x9f, hi: 0x9f},
	{value: 0x8132, lo: 0xbe, hi: 0xbe},
	// Block 0x13, offset 0x84
	{value: 0x0000, lo: 0x03},
	{value: 0x4597, lo: 0xb3, hi: 0xb3},
	{value: 0x459f, lo: 0xb6, hi: 0xb6},
	{value: 0x8102, lo: 0xbc, hi: 0xbc},
	// Block 0x14, offset 0x88
	{value: 0x0008, lo: 0x03},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x4577, lo: 0x99, hi: 0x9b},
	{value: 0x458f, lo: 0x9e, hi: 0x9e},
	// Block 0x15, offset 0x8c
	{value: 0x0000, lo: 0x01},
	{value: 0x8102, lo: 0xbc, hi: 0xbc},
	// Block 0x16, offset 0x8e
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	// Block 0x17, offset 0x90
	{value: 0x0000, lo: 0x08},
	{value: 0xa000, lo: 0x87, hi: 0x87},
	{value: 0x2cb9, lo: 0x88, hi: 0x88},
	{value: 0x2cb1, lo: 0x8b, hi: 0x8b},
	{value: 0x2cc1, lo: 0x8c, hi: 0x8c},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x9900, lo: 0x96, hi: 0x97},
	{value: 0x45a7, lo: 0x9c, hi: 0x9c},
	{value: 0x45af, lo: 0x9d, hi: 0x9d},
	// Block 0x18, offset 0x99
	{value: 0x0000, lo: 0x03},
	{value: 0xa000, lo: 0x92, hi: 0x92},
	{value: 0x2cc9, lo: 0x94, hi: 0x94},
	{value: 0x9900, lo: 0xbe, hi: 0xbe},
	// Block 0x19, offset 0x9d
	{value: 0x0000, lo: 0x06},
	{value: 0xa000, lo: 0x86, hi: 0x87},
	{value: 0x2cd1, lo: 0x8a, hi: 0x8a},
	{value: 0x2ce1, lo: 0x8b, hi: 0x8b},
	{value: 0x2cd9, lo: 0x8c, hi: 0x8c},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x9900, lo: 0x97, hi: 0x97},
	// Block 0x1a, offset 0xa4
	{value: 0x1801, lo: 0x04},
	{value: 0xa000, lo: 0x86, hi: 0x86},
	{value: 0x3ef3, lo: 0x88, hi: 0x88},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x8120, lo: 0x95, hi: 0x96},
	// Block 0x1b, offset 0xa9
	{value: 0x0000, lo: 0x02},
	{value: 0x8102, lo: 0xbc, hi: 0xbc},
	{value: 0xa000, lo: 0xbf, hi: 0xbf},
	// Block 0x1c, offset 0xac
	{value: 0x0000, lo: 0x09},
	{value: 0x2ce9, lo: 0x80, hi: 0x80},
	{value: 0x9900, lo: 0x82, hi: 0x82},
	{value: 0xa000, lo: 0x86, hi: 0x86},
	{value: 0x2cf1, lo: 0x87, hi: 0x87},
	{value: 0x2cf9, lo: 0x88, hi: 0x88},
	{value: 0x2f53, lo: 0x8a, hi: 0x8a},
	{value: 0x2ddb, lo: 0x8b, hi: 0x8b},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x9900, lo: 0x95, hi: 0x96},
	// Block 0x1d, offset 0xb6
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0xbb, hi: 0xbc},
	{value: 0x9900, lo: 0xbe, hi: 0xbe},
	// Block 0x1e, offset 0xb9
	{value: 0x0000, lo: 0x06},
	{value: 0xa000, lo: 0x86, hi: 0x87},
	{value: 0x2d01, lo: 0x8a, hi: 0x8a},
	{value: 0x2d11, lo: 0x8b, hi: 0x8b},
	{value: 0x2d09, lo: 0x8c, hi: 0x8c},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x9900, lo: 0x97, hi: 0x97},
	// Block 0x1f, offset 0xc0
	{value: 0x6be7, lo: 0x07},
	{value: 0x9904, lo: 0x8a, hi: 0x8a},
	{value: 0x9900, lo: 0x8f, hi: 0x8f},
	{value: 0xa000, lo: 0x99, hi: 0x99},
	{value: 0x3efb, lo: 0x9a, hi: 0x9a},
	{value: 0x2f5b, lo: 0x9c, hi: 0x9c},
	{value: 0x2de6, lo: 0x9d, hi: 0x9d},
	{value: 0x2d19, lo: 0x9e, hi: 0x9f},
	// Block 0x20, offset 0xc8
	{value: 0x0000, lo: 0x02},
	{value: 0x8122, lo: 0xb8, hi: 0xb9},
	{value: 0x8104, lo: 0xba, hi: 0xba},
	// Block 0x21, offset 0xcb
	{value: 0x0000, lo: 0x01},
	{value: 0x8123, lo: 0x88, hi: 0x8b},
	// Block 0x22, offset 0xcd
	{value: 0x0000, lo: 0x02},
	{value: 0x8124, lo: 0xb8, hi: 0xb9},
	{value: 0x8104, lo: 0xba, hi: 0xba},
	// Block 0x23, offset 0xd0
	{value: 0x0000, lo: 0x01},
	{value: 0x8125, lo: 0x88, hi: 0x8b},
	// Block 0x24, offset 0xd2
	{value: 0x0000, lo: 0x04},
	{value: 0x812d, lo: 0x98, hi: 0x99},
	{value: 0x812d, lo: 0xb5, hi: 0xb5},
	{value: 0x812d, lo: 0xb7, hi: 0xb7},
	{value: 0x812b, lo: 0xb9, hi: 0xb9},
	// Block 0x25, offset 0xd7
	{value: 0x0000, lo: 0x10},
	{value: 0x2647, lo: 0x83, hi: 0x83},
	{value: 0x264e, lo: 0x8d, hi: 0x8d},
	{value: 0x2655, lo: 0x92, hi: 0x92},
	{value: 0x265c, lo: 0x97, hi: 0x97},
	{value: 0x2663, lo: 0x9c, hi: 0x9c},
	{value: 0x2640, lo: 0xa9, hi: 0xa9},
	{value: 0x8126, lo: 0xb1, hi: 0xb1},
	{value: 0x8127, lo: 0xb2, hi: 0xb2},
	{value: 0x4a87, lo: 0xb3, hi: 0xb3},
	{value: 0x8128, lo: 0xb4, hi: 0xb4},
	{value: 0x4a90, lo: 0xb5, hi: 0xb5},
	{value: 0x45b7, lo: 0xb6, hi: 0xb6},
	{value: 0x8200, lo: 0xb7, hi: 0xb7},
	{value: 0x45bf, lo: 0xb8, hi: 0xb8},
	{value: 0x8200, lo: 0xb9, hi: 0xb9},
	{value: 0x8127, lo: 0xba, hi: 0xbd},
	// Block 0x26, offset 0xe8
	{value: 0x0000, lo: 0x0b},
	{value: 0x8127, lo: 0x80, hi: 0x80},
	{value: 0x4a99, lo: 0x81, hi: 0x81},
	{value: 0x8132, lo: 0x82, hi: 0x83},
	{value: 0x8104, lo: 0x84, hi: 0x84},
	{value: 0x8132, lo: 0x86, hi: 0x87},
	{value: 0x2671, lo: 0x93, hi: 0x93},
	{value: 0x2678, lo: 0x9d, hi: 0x9d},
	{value: 0x267f, lo: 0xa2, hi: 0xa2},
	{value: 0x2686, lo: 0xa7, hi: 0xa7},
	{value: 0x268d, lo: 0xac, hi: 0xac},
	{value: 0x266a, lo: 0xb9, hi: 0xb9},
	// Block 0x27, offset 0xf4
	{value: 0x0000, lo: 0x01},
	{value: 0x812d, lo: 0x86, hi: 0x86},
	// Block 0x28, offset 0xf6
	{value: 0x0000, lo: 0x05},
	{value: 0xa000, lo: 0xa5, hi: 0xa5},
	{value: 0x2d21, lo: 0xa6, hi: 0xa6},
	{value: 0x9900, lo: 0xae, hi: 0xae},
	{value: 0x8102, lo: 0xb7, hi: 0xb7},
	{value: 0x8104, lo: 0xb9, hi: 0xba},
	// Block 0x29, offset 0xfc
	{value: 0x0000, lo: 0x01},
	{value: 0x812d, lo: 0x8d, hi: 0x8d},
	// Block 0x2a, offset 0xfe
	{value: 0x0000, lo: 0x01},
	{value: 0xa000, lo: 0x80, hi: 0x92},
	// Block 0x2b, offset 0x100
	{value: 0x0000, lo: 0x01},
	{value: 0xb900, lo: 0xa1, hi: 0xb5},
	// Block 0x2c, offset 0x102
	{value: 0x0000, lo: 0x01},
	{value: 0x9900, lo: 0xa8, hi: 0xbf},
	// Block 0x2d, offset 0x104
	{value: 0x0000, lo: 0x01},
	{value: 0x9900, lo: 0x80, hi: 0x82},
	// Block 0x2e, offset 0x106
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0x9d, hi: 0x9f},
	// Block 0x2f, offset 0x108
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0x94, hi: 0x94},
	{value: 0x8104, lo: 0xb4, hi: 0xb4},
	// Block 0x30, offset 0x10b
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0x92, hi: 0x92},
	{value: 0x8132, lo: 0x9d, hi: 0x9d},
	// Block 0x31, offset 0x10e
	{value: 0x0000, lo: 0x01},
	{value: 0x8131, lo: 0xa9, hi: 0xa9},
	// Block 0x32, offset 0x110
	{value: 0x0004, lo: 0x02},
	{value: 0x812e, lo: 0xb9, hi: 0xba},
	{value: 0x812d, lo: 0xbb, hi: 0xbb},
	// Block 0x33, offset 0x113
	{value: 0x0000, lo: 0x02},
	{value: 0x8132, lo: 0x97, hi: 0x97},
	{value: 0x812d, lo: 0x98, hi: 0x98},
	// Block 0x34, offset 0x116
	{value: 0x0000, lo: 0x03},
	{value: 0x8104, lo: 0xa0, hi: 0xa0},
	{value: 0x8132, lo: 0xb5, hi: 0xbc},
	{value: 0x812d, lo: 0xbf, hi: 0xbf},
	// Block 0x35, offset 0x11a
	{value: 0x0000, lo: 0x04},
	{value: 0x8132, lo: 0xb0, hi: 0xb4},
	{value: 0x812d, lo: 0xb5, hi: 0xba},
	{value: 0x8132, lo: 0xbb, hi: 0xbc},
	{value: 0x812d, lo: 0xbd, hi: 0xbd},
	// Block 0x36, offset 0x11f
	{value: 0x0000, lo: 0x08},
	{value: 0x2d69, lo: 0x80, hi: 0x80},
	{value: 0x2d71, lo: 0x81, hi: 0x81},
	{value: 0xa000, lo: 0x82, hi: 0x82},
	{value: 0x2d79, lo: 0x83, hi: 0x83},
	{value: 0x8104, lo: 0x84, hi: 0x84},
	{value: 0x8132, lo: 0xab, hi: 0xab},
	{value: 0x812d, lo: 0xac, hi: 0xac},
	{value: 0x8132, lo: 0xad, hi: 0xb3},
	// Block 0x37, offset 0x128
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0xaa, hi: 0xab},
	// Block 0x38, offset 0x12a
	{value: 0x0000, lo: 0x02},
	{value: 0x8102, lo: 0xa6, hi: 0xa6},
	{value: 0x8104, lo: 0xb2, hi: 0xb3},
	// Block 0x39, offset 0x12d
	{value: 0x0000, lo: 0x01},
	{value: 0x8102, lo: 0xb7, hi: 0xb7},
	// Block 0x3a, offset 0x12f
	{value: 0x0000, lo: 0x0a},
	{value: 0x8132, lo: 0x90, hi: 0x92},
	{value: 0x8101, lo: 0x94, hi: 0x94},
	{value: 0x812d, lo: 0x95, hi: 0x99},
	{value: 0x8132, lo: 0x9a, hi: 0x9b},
	{value: 0x812d, lo: 0x9c, hi: 0x9f},
	{value: 0x8132, lo: 0xa0, hi: 0xa0},
	{value: 0x8101, lo: 0xa2, hi: 0xa8},
	{value: 0x812d, lo: 0xad, hi: 0xad},
	{value: 0x8132, lo: 0xb4, hi: 0xb4},
	{value: 0x8132, lo: 0xb8, hi: 0xb9},
	// Block 0x3b, offset 0x13a
	{value: 0x0004, lo: 0x03},
	{value: 0x0433, lo: 0x80, hi: 0x81},
	{value: 0x8100, lo: 0x97, hi: 0x97},
	{value: 0x8100, lo: 0xbe, hi: 0xbe},
	// Block 0x3c, offset 0x13e
	{value: 0x0000, lo: 0x0d},
	{value: 0x8132, lo: 0x90, hi: 0x91},
	{value: 0x8101, lo: 0x92, hi: 0x93},
	{value: 0x8132, lo: 0x94, hi: 0x97},
	{value: 0x8101, lo: 0x98, hi: 0x9a},
	{value: 0x8132, lo: 0x9b, hi: 0x9c},
	{value: 0x8132, lo: 0xa1, hi: 0xa1},
	{value: 0x8101, lo: 0xa5, hi: 0xa6},
	{value: 0x8132, lo: 0xa7, hi: 0xa7},
	{value: 0x812d, lo: 0xa8, hi: 0xa8},
	{value: 0x8132, lo: 0xa9, hi: 0xa9},
	{value: 0x8101, lo: 0xaa, hi: 0xab},
	{value: 0x812d, lo: 0xac, hi: 0xaf},
	{value: 0x8132, lo: 0xb0, hi: 0xb0},
	// Block 0x3d, offset 0x14c
	{value: 0x427e, lo: 0x02},
	{value: 0x01b8, lo: 0xa6, hi: 0xa6},
	{value: 0x0057, lo: 0xaa, hi: 0xab},
	// Block 0x3e, offset 0x14f
	{value: 0x0007, lo: 0x05},
	{value: 0xa000, lo: 0x90, hi: 0x90},
	{value: 0xa000, lo: 0x92, hi: 0x92},
	{value: 0xa000, lo: 0x94, hi: 0x94},
	{value: 0x3bbc, lo: 0x9a, hi: 0x9b},
	{value: 0x3bca, lo: 0xae, hi: 0xae},
	// Block 0x3f, offset 0x155
	{value: 0x000e, lo: 0x05},
	{value: 0x3bd1, lo: 0x8d, hi: 0x8e},
	{value: 0x3bd8, lo: 0x8f, hi: 0x8f},
	{value: 0xa000, lo: 0x90, hi: 0x90},
	{value: 0xa000, lo: 0x92, hi: 0x92},
	{value: 0xa000, lo: 0x94, hi: 0x94},
	// Block 0x40, offset 0x15b
	{value: 0x6405, lo: 0x0a},
	{value: 0xa000, lo: 0x83, hi: 0x83},
	{value: 0x3be6, lo: 0x84, hi: 0x84},
	{value: 0xa000, lo: 0x88, hi: 0x88},
	{value: 0x3bed, lo: 0x89, hi: 0x89},
	{value: 0xa000, lo: 0x8b, hi: 0x8b},
	{value: 0x3bf4, lo: 0x8c, hi: 0x8c},
	{value: 0xa000, lo: 0xa3, hi: 0xa3},
	{value: 0x3bfb, lo: 0xa4, hi: 0xa5},
	{value: 0x3c02, lo: 0xa6, hi: 0xa6},
	{value: 0xa000, lo: 0xbc, hi: 0xbc},
	// Block 0x41, offset 0x166
	{value: 0x0007, lo: 0x03},
	{value: 0x3c6b, lo: 0xa0, hi: 0xa1},
	{value: 0x3c95, lo: 0xa2, hi: 0xa3},
	{value: 0x3cbf, lo: 0xaa, hi: 0xad},
	// Block 0x42, offset 0x16a
	{value: 0x0004, lo: 0x01},
	{value: 0x048b, lo: 0xa9, hi: 0xaa},
	// Block 0x43, offset 0x16c
	{value: 0x0000, lo: 0x01},
	{value: 0x44e0, lo: 0x9c, hi: 0x9c},
	// Block 0x44, offset 0x16e
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0xaf, hi: 0xb1},
	// Block 0x45, offset 0x170
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0xbf, hi: 0xbf},
	// Block 0x46, offset 0x172
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0xa0, hi: 0xbf},
	// Block 0x47, offset 0x174
	{value: 0x0000, lo: 0x05},
	{value: 0x812c, lo: 0xaa, hi: 0xaa},
	{value: 0x8131, lo: 0xab, hi: 0xab},
	{value: 0x8133, lo: 0xac, hi: 0xac},
	{value: 0x812e, lo: 0xad, hi: 0xad},
	{value: 0x812f, lo: 0xae, hi: 0xaf},
	// Block 0x48, offset 0x17a
	{value: 0x0000, lo: 0x03},
	{value: 0x4aa2, lo: 0xb3, hi: 0xb3},
	{value: 0x4aa2, lo: 0xb5, hi: 0xb6},
	{value: 0x4aa2, lo: 0xba, hi: 0xbf},
	// Block 0x49, offset 0x17e
	{value: 0x0000, lo: 0x01},
	{value: 0x4aa2, lo: 0x8f, hi: 0xa3},
	// Block 0x4a, offset 0x180
	{value: 0x0000, lo: 0x01},
	{value: 0x8100, lo: 0xae, hi: 0xbe},
	// Block 0x4b, offset 0x182
	{value: 0x0000, lo: 0x07},
	{value: 0x8100, lo: 0x84, hi: 0x84},
	{value: 0x8100, lo: 0x87, hi: 0x87},
	{value: 0x8100, lo: 0x90, hi: 0x90},
	{value: 0x8100, lo: 0x9e, hi: 0x9e},
	{value: 0x8100, lo: 0xa1, hi: 0xa1},
	{value: 0x8100, lo: 0xb2, hi: 0xb2},
	{value: 0x8100, lo: 0xbb, hi: 0xbb},
	// Block 0x4c, offset 0x18a
	{value: 0x0000, lo: 0x03},
	{value: 0x8100, lo: 0x80, hi: 0x80},
	{value: 0x8100, lo: 0x8b, hi: 0x8b},
	{value: 0x8100, lo: 0x8e, hi: 0x8e},
	// Block 0x4d, offset 0x18e
	{value: 0x0000, lo: 0x02},
	{value: 0x8132, lo: 0xaf, hi: 0xaf},
	{value: 0x8132, lo: 0xb4, hi: 0xbd},
	// Block 0x4e, offset 0x191
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0x9e, hi: 0x9f},
	// Block 0x4f, offset 0x193
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0xb0, hi: 0xb1},
	// Block 0x50, offset 0x195
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0x86, hi: 0x86},
	// Block 0x51, offset 0x197
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0x84, hi: 0x84},
	{value: 0x8132, lo: 0xa0, hi: 0xb1},
	// Block 0x52, offset 0x19a
	{value: 0x0000, lo: 0x01},
	{value: 0x812d, lo: 0xab, hi: 0xad},
	// Block 0x53, offset 0x19c
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0x93, hi: 0x93},
	// Block 0x54, offset 0x19e
	{value: 0x0000, lo: 0x01},
	{value: 0x8102, lo: 0xb3, hi: 0xb3},
	// Block 0x55, offset 0x1a0
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0x80, hi: 0x80},
	// Block 0x56, offset 0x1a2
	{value: 0x0000, lo: 0x05},
	{value: 0x8132, lo: 0xb0, hi: 0xb0},
	{value: 0x8132, lo: 0xb2, hi: 0xb3},
	{value: 0x812d, lo: 0xb4, hi: 0xb4},
	{value: 0x8132, lo: 0xb7, hi: 0xb8},
	{value: 0x8132, lo: 0xbe, hi: 0xbf},
	// Block 0x57, offset 0x1a8
	{value: 0x0000, lo: 0x02},
	{value: 0x8132, lo: 0x81, hi: 0x81},
	{value: 0x8104, lo: 0xb6, hi: 0xb6},
	// Block 0x58, offset 0x1ab
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0xad, hi: 0xad},
	// Block 0x59, offset 0x1ad
	{value: 0x0000, lo: 0x06},
	{value: 0xe500, lo: 0x80, hi: 0x80},
	{value: 0xc600, lo: 0x81, hi: 0x9b},
	{value: 0xe500, lo: 0x9c, hi: 0x9c},
	{value: 0xc600, lo: 0x9d, hi: 0xb7},
	{value: 0xe500, lo: 0xb8, hi: 0xb8},
	{value: 0xc600, lo: 0xb9, hi: 0xbf},
	// Block 0x5a, offset 0x1b4
	{value: 0x0000, lo: 0x05},
	{value: 0xc600, lo: 0x80, hi: 0x93},
	{value: 0xe500, lo: 0x94, hi: 0x94},
	{value: 0xc600, lo: 0x95, hi: 0xaf},
	{value: 0xe500, lo: 0xb0, hi: 0xb0},
	{value: 0xc600, lo: 0xb1, hi: 0xbf},
	// Block 0x5b, offset 0x1ba
	{value: 0x0000, lo: 0x05},
	{value: 0xc600, lo: 0x80, hi: 0x8b},
	{value: 0xe500, lo: 0x8c, hi: 0x8c},
	{value: 0xc600, lo: 0x8d, hi: 0xa7},
	{value: 0xe500, lo: 0xa8, hi: 0xa8},
	{value: 0xc600, lo: 0xa9, hi: 0xbf},
	// Block 0x5c, offset 0x1c0
	{value: 0x0000, lo: 0x07},
	{value: 0xc600, lo: 0x80, hi: 0x83},
	{value: 0xe500, lo: 0x84, hi: 0x84},
	{value: 0xc600, lo: 0x85, hi: 0x9f},
	{value: 0xe500, lo: 0xa0, hi: 0xa0},
	{value: 0xc600, lo: 0xa1, hi: 0xbb},
	{value: 0xe500, lo: 0xbc, hi: 0xbc},
	{value: 0xc600, lo: 0xbd, hi: 0xbf},
	// Block 0x5d, offset 0x1c8
	{value: 0x0000, lo: 0x05},
	{value: 0xc600, lo: 0x80, hi: 0x97},
	{value: 0xe500, lo: 0x98, hi: 0x98},
	{value: 0xc600, lo: 0x99, hi: 0xb3},
	{value: 0xe500, lo: 0xb4, hi: 0xb4},
	{value: 0xc600, lo: 0xb5, hi: 0xbf},
	// Block 0x5e, offset 0x1ce
	{value: 0x0000, lo: 0x05},
	{value: 0xc600, lo: 0x80, hi: 0x8f},
	{value: 0xe500, lo: 0x90, hi: 0x90},
	{value: 0xc600, lo: 0x91, hi: 0xab},
	{value: 0xe500, lo: 0xac, hi: 0xac},
	{value: 0xc600, lo: 0xad, hi: 0xbf},
	// Block 0x5f, offset 0x1d4
	{value: 0x0000, lo: 0x05},
	{value: 0xc600, lo: 0x80, hi: 0x87},
	{value: 0xe500, lo: 0x88, hi: 0x88},
	{value: 0xc600, lo: 0x89, hi: 0xa3},
	{value: 0xe500, lo: 0xa4, hi: 0xa4},
	{value: 0xc600, lo: 0xa5, hi: 0xbf},
	// Block 0x60, offset 0x1da
	{value: 0x0000, lo: 0x03},
	{value: 0xc600, lo: 0x80, hi: 0x87},
	{value: 0xe500, lo: 0x88, hi: 0x88},
	{value: 0xc600, lo: 0x89, hi: 0xa3},
	// Block 0x61, offset 0x1de
	{value: 0x0006, lo: 0x0d},
	{value: 0x4393, lo: 0x9d, hi: 0x9d},
	{value: 0x8115, lo: 0x9e, hi: 0x9e},
	{value: 0x4405, lo: 0x9f, hi: 0x9f},
	{value: 0x43f3, lo: 0xaa, hi: 0xab},
	{value: 0x44f7, lo: 0xac, hi: 0xac},
	{value: 0x44ff, lo: 0xad, hi: 0xad},
	{value: 0x434b, lo: 0xae, hi: 0xb1},
	{value: 0x4369, lo: 0xb2, hi: 0xb4},
	{value: 0x4381, lo: 0xb5, hi: 0xb6},
	{value: 0x438d, lo: 0xb8, hi: 0xb8},
	{value: 0x4399, lo: 0xb9, hi: 0xbb},
	{value: 0x43b1, lo: 0xbc, hi: 0xbc},
	{value: 0x43b7, lo: 0xbe, hi: 0xbe},
	// Block 0x62, offset 0x1ec
	{value: 0x0006, lo: 0x08},
	{value: 0x43bd, lo: 0x80, hi: 0x81},
	{value: 0x43c9, lo: 0x83, hi: 0x84},
	{value: 0x43db, lo: 0x86, hi: 0x89},
	{value: 0x43ff, lo: 0x8a, hi: 0x8a},
	{value: 0x437b, lo: 0x8b, hi: 0x8b},
	{value: 0x4363, lo: 0x8c, hi: 0x8c},
	{value: 0x43ab, lo: 0x8d, hi: 0x8d},
	{value: 0x43d5, lo: 0x8e, hi: 0x8e},
	// Block 0x63, offset 0x1f5
	{value: 0x0000, lo: 0x02},
	{value: 0x8100, lo: 0xa4, hi: 0xa5},
	{value: 0x8100, lo: 0xb0, hi: 0xb1},
	// Block 0x64, offset 0x1f8
	{value: 0x0000, lo: 0x02},
	{value: 0x8100, lo: 0x9b, hi: 0x9d},
	{value: 0x8200, lo: 0x9e, hi: 0xa3},
	// Block 0x65, offset 0x1fb
	{value: 0x0000, lo: 0x01},
	{value: 0x8100, lo: 0x90, hi: 0x90},
	// Block 0x66, offset 0x1fd
	{value: 0x0000, lo: 0x02},
	{value: 0x8100, lo: 0x99, hi: 0x99},
	{value: 0x8200, lo: 0xb2, hi: 0xb4},
	// Block 0x67, offset 0x200
	{value: 0x0000, lo: 0x01},
	{value: 0x8100, lo: 0xbc, hi: 0xbd},
	// Block 0x68, offset 0x202
	{value: 0x0000, lo: 0x03},
	{value: 0x8132, lo: 0xa0, hi: 0xa6},
	{value: 0x812d, lo: 0xa7, hi: 0xad},
	{value: 0x8132, lo: 0xae, hi: 0xaf},
	// Block 0x69, offset 0x206
	{value: 0x0000, lo: 0x04},
	{value: 0x8100, lo: 0x89, hi: 0x8c},
	{value: 0x8100, lo: 0xb0, hi: 0xb2},
	{value: 0x8100, lo: 0xb4, hi: 0xb4},
	{value: 0x8100, lo: 0xb6, hi: 0xbf},
	// Block 0x6a, offset 0x20b
	{value: 0x0000, lo: 0x01},
	{value: 0x8100, lo: 0x81, hi: 0x8c},
	// Block 0x6b, offset 0x20d
	{value: 0x0000, lo: 0x01},
	{value: 0x8100, lo: 0xb5, hi: 0xba},
	// Block 0x6c, offset 0x20f
	{value: 0x0000, lo: 0x04},
	{value: 0x4aa2, lo: 0x9e, hi: 0x9f},
	{value: 0x4aa2, lo: 0xa3, hi: 0xa3},
	{value: 0x4aa2, lo: 0xa5, hi: 0xa6},
	{value: 0x4aa2, lo: 0xaa, hi: 0xaf},
	// Block 0x6d, offset 0x214
	{value: 0x0000, lo: 0x05},
	{value: 0x4aa2, lo: 0x82, hi: 0x87},
	{value: 0x4aa2, lo: 0x8a, hi: 0x8f},
	{value: 0x4aa2, lo: 0x92, hi: 0x97},
	{value: 0x4aa2, lo: 0x9a, hi: 0x9c},
	{value: 0x8100, lo: 0xa3, hi: 0xa3},
	// Block 0x6e, offset 0x21a
	{value: 0x0000, lo: 0x01},
	{value: 0x812d, lo: 0xbd, hi: 0xbd},
	// Block 0x6f, offset 0x21c
	{value: 0x0000, lo: 0x01},
	{value: 0x812d, lo: 0xa0, hi: 0xa0},
	// Block 0x70, offset 0x21e
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0xb6, hi: 0xba},
	// Block 0x71, offset 0x220
	{value: 0x002c, lo: 0x05},
	{value: 0x812d, lo: 0x8d, hi: 0x8d},
	{value: 0x8132, lo: 0x8f, hi: 0x8f},
	{value: 0x8132, lo: 0xb8, hi: 0xb8},
	{value: 0x8101, lo: 0xb9, hi: 0xba},
	{value: 0x8104, lo: 0xbf, hi: 0xbf},
	// Block 0x72, offset 0x226
	{value: 0x0000, lo: 0x02},
	{value: 0x8132, lo: 0xa5, hi: 0xa5},
	{value: 0x812d, lo: 0xa6, hi: 0xa6},
	// Block 0x73, offset 0x229
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0xa4, hi: 0xa7},
	// Block 0x74, offset 0x22b
	{value: 0x0000, lo: 0x05},
	{value: 0x812d, lo: 0x86, hi: 0x87},
	{value: 0x8132, lo: 0x88, hi: 0x8a},
	{value: 0x812d, lo: 0x8b, hi: 0x8b},
	{value: 0x8132, lo: 0x8c, hi: 0x8c},
	{value: 0x812d, lo: 0x8d, hi: 0x90},
	// Block 0x75, offset 0x231
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0x86, hi: 0x86},
	{value: 0x8104, lo: 0xbf, hi: 0xbf},
	// Block 0x76, offset 0x234
	{value: 0x17fe, lo: 0x07},
	{value: 0xa000, lo: 0x99, hi: 0x99},
	{value: 0x423b, lo: 0x9a, hi: 0x9a},
	{value: 0xa000, lo: 0x9b, hi: 0x9b},
	{value: 0x4245, lo: 0x9c, hi: 0x9c},
	{value: 0xa000, lo: 0xa5, hi: 0xa5},
	{value: 0x424f, lo: 0xab, hi: 0xab},
	{value: 0x8104, lo: 0xb9, hi: 0xba},
	// Block 0x77, offset 0x23c
	{value: 0x0000, lo: 0x06},
	{value: 0x8132, lo: 0x80, hi: 0x82},
	{value: 0x9900, lo: 0xa7, hi: 0xa7},
	{value: 0x2d81, lo: 0xae, hi: 0xae},
	{value: 0x2d8b, lo: 0xaf, hi: 0xaf},
	{value: 0xa000, lo: 0xb1, hi: 0xb2},
	{value: 0x8104, lo: 0xb3, hi: 0xb4},
	// Block 0x78, offset 0x243
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0x80, hi: 0x80},
	{value: 0x8102, lo: 0x8a, hi: 0x8a},
	// Block 0x79, offset 0x246
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0xb5, hi: 0xb5},
	{value: 0x8102, lo: 0xb6, hi: 0xb6},
	// Block 0x7a, offset 0x249
	{value: 0x0002, lo: 0x01},
	{value: 0x8102, lo: 0xa9, hi: 0xaa},
	// Block 0x7b, offset 0x24b
	{value: 0x0000, lo: 0x02},
	{value: 0x8102, lo: 0xbb, hi: 0xbc},
	{value: 0x9900, lo: 0xbe, hi: 0xbe},
	// Block 0x7c, offset 0x24e
	{value: 0x0000, lo: 0x07},
	{value: 0xa000, lo: 0x87, hi: 0x87},
	{value: 0x2d95, lo: 0x8b, hi: 0x8b},
	{value: 0x2d9f, lo: 0x8c, hi: 0x8c},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x9900, lo: 0x97, hi: 0x97},
	{value: 0x8132, lo: 0xa6, hi: 0xac},
	{value: 0x8132, lo: 0xb0, hi: 0xb4},
	// Block 0x7d, offset 0x256
	{value: 0x0000, lo: 0x03},
	{value: 0x8104, lo: 0x82, hi: 0x82},
	{value: 0x8102, lo: 0x86, hi: 0x86},
	{value: 0x8132, lo: 0x9e, hi: 0x9e},
	// Block 0x7e, offset 0x25a
	{value: 0x6b57, lo: 0x06},
	{value: 0x9900, lo: 0xb0, hi: 0xb0},
	{value: 0xa000, lo: 0xb9, hi: 0xb9},
	{value: 0x9900, lo: 0xba, hi: 0xba},
	{value: 0x2db3, lo: 0xbb, hi: 0xbb},
	{value: 0x2da9, lo: 0xbc, hi: 0xbd},
	{value: 0x2dbd, lo: 0xbe, hi: 0xbe},
	// Block 0x7f, offset 0x261
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0x82, hi: 0x82},
	{value: 0x8102, lo: 0x83, hi: 0x83},
	// Block 0x80, offset 0x264
	{value: 0x0000, lo: 0x05},
	{value: 0x9900, lo: 0xaf, hi: 0xaf},
	{value: 0xa000, lo: 0xb8, hi: 0xb9},
	{value: 0x2dc7, lo: 0xba, hi: 0xba},
	{value: 0x2dd1, lo: 0xbb, hi: 0xbb},
	{value: 0x8104, lo: 0xbf, hi: 0xbf},
	// Block 0x81, offset 0x26a
	{value: 0x0000, lo: 0x01},
	{value: 0x8102, lo: 0x80, hi: 0x80},
	// Block 0x82, offset 0x26c
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0xb6, hi: 0xb6},
	{value: 0x8102, lo: 0xb7, hi: 0xb7},
	// Block 0x83, offset 0x26f
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0xab, hi: 0xab},
	// Block 0x84, offset 0x271
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0xb9, hi: 0xb9},
	{value: 0x8102, lo: 0xba, hi: 0xba},
	// Block 0x85, offset 0x274
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0xa0, hi: 0xa0},
	// Block 0x86, offset 0x276
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0xb4, hi: 0xb4},
	// Block 0x87, offset 0x278
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0x87, hi: 0x87},
	// Block 0x88, offset 0x27a
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0x99, hi: 0x99},
	// Block 0x89, offset 0x27c
	{value: 0x0000, lo: 0x02},
	{value: 0x8102, lo: 0x82, hi: 0x82},
	{value: 0x8104, lo: 0x84, hi: 0x85},
	// Block 0x8a, offset 0x27f
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0x97, hi: 0x97},
	// Block 0x8b, offset 0x281
	{value: 0x0000, lo: 0x01},
	{value: 0x8101, lo: 0xb0, hi: 0xb4},
	// Block 0x8c, offset 0x283
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0xb0, hi: 0xb6},
	// Block 0x8d, offset 0x285
	{value: 0x0000, lo: 0x01},
	{value: 0x8101, lo: 0x9e, hi: 0x9e},
	// Block 0x8e, offset 0x287
	{value: 0x0000, lo: 0x0c},
	{value: 0x45cf, lo: 0x9e, hi: 0x9e},
	{value: 0x45d9, lo: 0x9f, hi: 0x9f},
	{value: 0x460d, lo: 0xa0, hi: 0xa0},
	{value: 0x461b, lo: 0xa1, hi: 0xa1},
	{value: 0x4629, lo: 0xa2, hi: 0xa2},
	{value: 0x4637, lo: 0xa3, hi: 0xa3},
	{value: 0x4645, lo: 0xa4, hi: 0xa4},
	{value: 0x812b, lo: 0xa5, hi: 0xa6},
	{value: 0x8101, lo: 0xa7, hi: 0xa9},
	{value: 0x8130, lo: 0xad, hi: 0xad},
	{value: 0x812b, lo: 0xae, hi: 0xb2},
	{value: 0x812d, lo: 0xbb, hi: 0xbf},
	// Block 0x8f, offset 0x294
	{value: 0x0000, lo: 0x09},
	{value: 0x812d, lo: 0x80, hi: 0x82},
	{value: 0x8132, lo: 0x85, hi: 0x89},
	{value: 0x812d, lo: 0x8a, hi: 0x8b},
	{value: 0x8132, lo: 0xaa, hi: 0xad},
	{value: 0x45e3, lo: 0xbb, hi: 0xbb},
	{value: 0x45ed, lo: 0xbc, hi: 0xbc},
	{value: 0x4653, lo: 0xbd, hi: 0xbd},
	{value: 0x466f, lo: 0xbe, hi: 0xbe},
	{value: 0x4661, lo: 0xbf, hi: 0xbf},
	// Block 0x90, offset 0x29e
	{value: 0x0000, lo: 0x01},
	{value: 0x467d, lo: 0x80, hi: 0x80},
	// Block 0x91, offset 0x2a0
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0x82, hi: 0x84},
	// Block 0x92, offset 0x2a2
	{value: 0x0000, lo: 0x05},
	{value: 0x8132, lo: 0x80, hi: 0x86},
	{value: 0x8132, lo: 0x88, hi: 0x98},
	{value: 0x8132, lo: 0x9b, hi: 0xa1},
	{value: 0x8132, lo: 0xa3, hi: 0xa4},
	{value: 0x8132, lo: 0xa6, hi: 0xaa},
	// Block 0x93, offset 0x2a8
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0xac, hi: 0xaf},
	// Block 0x94, offset 0x2aa
	{value: 0x0000, lo: 0x01},
	{value: 0x812d, lo: 0x90, hi: 0x96},
	// Block 0x95, offset 0x2ac
	{value: 0x0000, lo: 0x02},
	{value: 0x8132, lo: 0x84, hi: 0x89},
	{value: 0x8102, lo: 0x8a, hi: 0x8a},
	// Block 0x96, offset 0x2af
	{value: 0x0000, lo: 0x01},
	{value: 0x8100, lo: 0x93, hi: 0x93},
}

// lookup returns the trie value for the first UTF-8 encoding in s and
// the width in bytes of this encoding. The size will be 0 if s does not
// hold enough bytes to complete the encoding. len(s) must be greater than 0.
func (t *nfkcTrie) lookup(s []byte) (v uint16, sz int) {
	c0 := s[0]
	switch {
	case c0 < 0x80: // is ASCII
		return nfkcValues[c0], 1
	case c0 < 0xC2:
		return 0, 1 // Illegal UTF-8: not a starter, not ASCII.
	case c0 < 0xE0: // 2-byte UTF-8
		if len(s) < 2 {
			return 0, 0
		}
		i := nfkcIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c1), 2
	case c0 < 0xF0: // 3-byte UTF-8
		if len(s) < 3 {
			return 0, 0
		}
		i := nfkcIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = nfkcIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 2 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c2), 3
	case c0 < 0xF8: // 4-byte UTF-8
		if len(s) < 4 {
			return 0, 0
		}
		i := nfkcIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = nfkcIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 2 // Illegal UTF-8: not a continuation byte.
		}
		o = uint32(i)<<6 + uint32(c2)
		i = nfkcIndex[o]
		c3 := s[3]
		if c3 < 0x80 || 0xC0 <= c3 {
			return 0, 3 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c3), 4
	}
	// Illegal rune
	return 0, 1
}

// lookupUnsafe returns the trie value for the first UTF-8 encoding in s.
// s must start with a full and valid UTF-8 encoded rune.
func (t *nfkcTrie) lookupUnsafe(s []byte) uint16 {
	c0 := s[0]
	if c0 < 0x80 { // is ASCII
		return nfkcValues[c0]
	}
	i := nfkcIndex[c0]
	if c0 < 0xE0 { // 2-byte UTF-8
		return t.lookupValue(uint32(i), s[1])
	}
	i = nfkcIndex[uint32(i)<<6+uint32(s[1])]
	if c0 < 0xF0 { // 3-byte UTF-8
		return t.lookupValue(uint32(i), s[2])
	}
	i = nfkcIndex[uint32(i)<<6+uint32(s[2])]
	if c0 < 0xF8 { // 4-byte UTF-8
		return t.lookupValue(uint32(i), s[3])
	}
	return 0
}

// lookupString returns the trie value for the first UTF-8 encoding in s and
// the width in bytes of this encoding. The size will be 0 if s does not
// hold enough bytes to complete the encoding. len(s) must be greater than 0.
func (t *nfkcTrie) lookupString(s string) (v uint16, sz int) {
	c0 := s[0]
	switch {
	case c0 < 0x80: // is ASCII
		return nfkcValues[c0], 1
	case c0 < 0xC2:
		return 0, 1 // Illegal UTF-8: not a starter, not ASCII.
	case c0 < 0xE0: // 2-byte UTF-8
		if len(s) < 2 {
			return 0, 0
		}
		i := nfkcIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c1), 2
	case c0 < 0xF0: // 3-byte UTF-8
		if len(s) < 3 {
			return 0, 0
		}
		i := nfkcIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = nfkcIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 2 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c2), 3
	case c0 < 0xF8: // 4-byte UTF-8
		if len(s) < 4 {
			return 0, 0
		}
		i := nfkcIndex[c0]
		c1 := s[1]
		if c1 < 0x80 || 0xC0 <= c1 {
			return 0, 1 // Illegal UTF-8: not a continuation byte.
		}
		o := uint32(i)<<6 + uint32(c1)
		i = nfkcIndex[o]
		c2 := s[2]
		if c2 < 0x80 || 0xC0 <= c2 {
			return 0, 2 // Illegal UTF-8: not a continuation byte.
		}
		o = uint32(i)<<6 + uint32(c2)
		i = nfkcIndex[o]
		c3 := s[3]
		if c3 < 0x80 || 0xC0 <= c3 {
			return 0, 3 // Illegal UTF-8: not a continuation byte.
		}
		return t.lookupValue(uint32(i), c3), 4
	}
	// Illegal rune
	return 0, 1
}

// lookupStringUnsafe returns the trie value for the first UTF-8 encoding in s.
// s must start with a full and valid UTF-8 encoded rune.
func (t *nfkcTrie) lookupStringUnsafe(s string) uint16 {
	c0 := s[0]
	if c0 < 0x80 { // is ASCII
		return nfkcValues[c0]
	}
	i := nfkcIndex[c0]
	if c0 < 0xE0 { // 2-byte UTF-8
		return t.lookupValue(uint32(i), s[1])
	}
	i = nfkcIndex[uint32(i)<<6+uint32(s[1])]
	if c0 < 0xF0 { // 3-byte UTF-8
		return t.lookupValue(uint32(i), s[2])
	}
	i = nfkcIndex[uint32(i)<<6+uint32(s[2])]
	if c0 < 0xF8 { // 4-byte UTF-8
		return t.lookupValue(uint32(i), s[3])
	}
	return 0
}

// nfkcTrie. Total size: 18684 bytes (18.25 KiB). Checksum: 113e23c477adfabd.
type nfkcTrie struct{}

func newNfkcTrie(i int) *nfkcTrie {
	return &nfkcTrie{}
}

// lookupValue determines the type of block n and looks up the value for b.
func (t *nfkcTrie) lookupValue(n uint32, b byte) uint16 {
	switch {
	case n < 92:
		return uint16(nfkcValues[n<<6+uint32(b)])
	default:
		n -= 92
		return uint16(nfkcSparse.lookup(n, b))
	}
}

// nfkcValues: 94 blocks, 6016 entries, 12032 bytes
// The third block is the zero block.
var nfkcValues = [6016]uint16{
	// Block 0x0, offset 0x0
	0x3c: 0xa000, 0x3d: 0xa000, 0x3e: 0xa000,
	// Block 0x1, offset 0x40
	0x41: 0xa000, 0x42: 0xa000, 0x43: 0xa000, 0x44: 0xa000, 0x45: 0xa000,
	0x46: 0xa000, 0x47: 0xa000, 0x48: 0xa000, 0x49: 0xa000, 0x4a: 0xa000, 0x4b: 0xa000,
	0x4c: 0xa000, 0x4d: 0xa000, 0x4e: 0xa000, 0x4f: 0xa000, 0x50: 0xa000,
	0x52: 0xa000, 0x53: 0xa000, 0x54: 0xa000, 0x55: 0xa000, 0x56: 0xa000, 0x57: 0xa000,
	0x58: 0xa000, 0x59: 0xa000, 0x5a: 0xa000,
	0x61: 0xa000, 0x62: 0xa000, 0x63: 0xa000,
	0x64: 0xa000, 0x65: 0xa000, 0x66: 0xa000, 0x67: 0xa000, 0x68: 0xa000, 0x69: 0xa000,
	0x6a: 0xa000, 0x6b: 0xa000, 0x6c: 0xa000, 0x6d: 0xa000, 0x6e: 0xa000, 0x6f: 0xa000,
	0x70: 0xa000, 0x72: 0xa000, 0x73: 0xa000, 0x74: 0xa000, 0x75: 0xa000,
	0x76: 0xa000, 0x77: 0xa000, 0x78: 0xa000, 0x79: 0xa000, 0x7a: 0xa000,
	// Block 0x2, offset 0x80
	// Block 0x3, offset 0xc0
	0xc0: 0x2f72, 0xc1: 0x2f77, 0xc2: 0x468b, 0xc3: 0x2f7c, 0xc4: 0x469a, 0xc5: 0x469f,
	0xc6: 0xa000, 0xc7: 0x46a9, 0xc8: 0x2fe5, 0xc9: 0x2fea, 0xca: 0x46ae, 0xcb: 0x2ffe,
	0xcc: 0x3071, 0xcd: 0x3076, 0xce: 0x307b, 0xcf: 0x46c2, 0xd1: 0x3107,
	0xd2: 0x312a, 0xd3: 0x312f, 0xd4: 0x46cc, 0xd5: 0x46d1, 0xd6: 0x46e0,
	0xd8: 0xa000, 0xd9: 0x31b6, 0xda: 0x31bb, 0xdb: 0x31c0, 0xdc: 0x4712, 0xdd: 0x3238,
	0xe0: 0x327e, 0xe1: 0x3283, 0xe2: 0x471c, 0xe3: 0x3288,
	0xe4: 0x472b, 0xe5: 0x4730, 0xe6: 0xa000, 0xe7: 0x473a, 0xe8: 0x32f1, 0xe9: 0x32f6,
	0xea: 0x473f, 0xeb: 0x330a, 0xec: 0x3382, 0xed: 0x3387, 0xee: 0x338c, 0xef: 0x4753,
	0xf1: 0x3418, 0xf2: 0x343b, 0xf3: 0x3440, 0xf4: 0x475d, 0xf5: 0x4762,
	0xf6: 0x4771, 0xf8: 0xa000, 0xf9: 0x34cc, 0xfa: 0x34d1, 0xfb: 0x34d6,
	0xfc: 0x47a3, 0xfd: 0x3553, 0xff: 0x356c,
	// Block 0x4, offset 0x100
	0x100: 0x2f81, 0x101: 0x328d, 0x102: 0x4690, 0x103: 0x4721, 0x104: 0x2f9f, 0x105: 0x32ab,
	0x106: 0x2fb3, 0x107: 0x32bf, 0x108: 0x2fb8, 0x109: 0x32c4, 0x10a: 0x2fbd, 0x10b: 0x32c9,
	0x10c: 0x2fc2, 0x10d: 0x32ce, 0x10e: 0x2fcc, 0x10f: 0x32d8,
	0x112: 0x46b3, 0x113: 0x4744, 0x114: 0x2ff4, 0x115: 0x3300, 0x116: 0x2ff9, 0x117: 0x3305,
	0x118: 0x3017, 0x119: 0x3323, 0x11a: 0x3008, 0x11b: 0x3314, 0x11c: 0x3030, 0x11d: 0x333c,
	0x11e: 0x303a, 0x11f: 0x3346, 0x120: 0x303f, 0x121: 0x334b, 0x122: 0x3049, 0x123: 0x3355,
	0x124: 0x304e, 0x125: 0x335a, 0x128: 0x3080, 0x129: 0x3391,
	0x12a: 0x3085, 0x12b: 0x3396, 0x12c: 0x308a, 0x12d: 0x339b, 0x12e: 0x30ad, 0x12f: 0x33b9,
	0x130: 0x308f, 0x132: 0x195d, 0x133: 0x19ea, 0x134: 0x30b7, 0x135: 0x33c3,
	0x136: 0x30cb, 0x137: 0x33dc, 0x139: 0x30d5, 0x13a: 0x33e6, 0x13b: 0x30df,
	0x13c: 0x33f0, 0x13d: 0x30da, 0x13e: 0x33eb, 0x13f: 0x1baf,
	// Block 0x5, offset 0x140
	0x140: 0x1c37, 0x143: 0x3102, 0x144: 0x3413, 0x145: 0x311b,
	0x146: 0x342c, 0x147: 0x3111, 0x148: 0x3422, 0x149: 0x1c5f,
	0x14c: 0x46d6, 0x14d: 0x4767, 0x14e: 0x3134, 0x14f: 0x3445, 0x150: 0x313e, 0x151: 0x344f,
	0x154: 0x315c, 0x155: 0x346d, 0x156: 0x3175, 0x157: 0x3486,
	0x158: 0x3166, 0x159: 0x3477, 0x15a: 0x46f9, 0x15b: 0x478a, 0x15c: 0x317f, 0x15d: 0x3490,
	0x15e: 0x318e, 0x15f: 0x349f, 0x160: 0x46fe, 0x161: 0x478f, 0x162: 0x31a7, 0x163: 0x34bd,
	0x164: 0x3198, 0x165: 0x34ae, 0x168: 0x4708, 0x169: 0x4799,
	0x16a: 0x470d, 0x16b: 0x479e, 0x16c: 0x31c5, 0x16d: 0x34db, 0x16e: 0x31cf, 0x16f: 0x34e5,
	0x170: 0x31d4, 0x171: 0x34ea, 0x172: 0x31f2, 0x173: 0x3508, 0x174: 0x3215, 0x175: 0x352b,
	0x176: 0x323d, 0x177: 0x3558, 0x178: 0x3251, 0x179: 0x3260, 0x17a: 0x3580, 0x17b: 0x326a,
	0x17c: 0x358a, 0x17d: 0x326f, 0x17e: 0x358f, 0x17f: 0x00a7,
	// Block 0x6, offset 0x180
	0x184: 0x2df1, 0x185: 0x2df7,
	0x186: 0x2dfd, 0x187: 0x1972, 0x188: 0x1975, 0x189: 0x1a0b, 0x18a: 0x198a, 0x18b: 0x198d,
	0x18c: 0x1a41, 0x18d: 0x2f8b, 0x18e: 0x3297, 0x18f: 0x3099, 0x190: 0x33a5, 0x191: 0x3143,
	0x192: 0x3454, 0x193: 0x31d9, 0x194: 0x34ef, 0x195: 0x39d2, 0x196: 0x3b61, 0x197: 0x39cb,
	0x198: 0x3b5a, 0x199: 0x39d9, 0x19a: 0x3b68, 0x19b: 0x39c4, 0x19c: 0x3b53,
	0x19e: 0x38b3, 0x19f: 0x3a42, 0x1a0: 0x38ac, 0x1a1: 0x3a3b, 0x1a2: 0x35b6, 0x1a3: 0x35c8,
	0x1a6: 0x3044, 0x1a7: 0x3350, 0x1a8: 0x30c1, 0x1a9: 0x33d2,
	0x1aa: 0x46ef, 0x1ab: 0x4780, 0x1ac: 0x3993, 0x1ad: 0x3b22, 0x1ae: 0x35da, 0x1af: 0x35e0,
	0x1b0: 0x33c8, 0x1b1: 0x1942, 0x1b2: 0x1945, 0x1b3: 0x19d2, 0x1b4: 0x302b, 0x1b5: 0x3337,
	0x1b8: 0x30fd, 0x1b9: 0x340e, 0x1ba: 0x38ba, 0x1bb: 0x3a49,
	0x1bc: 0x35b0, 0x1bd: 0x35c2, 0x1be: 0x35bc, 0x1bf: 0x35ce,
	// Block 0x7, offset 0x1c0
	0x1c0: 0x2f90, 0x1c1: 0x329c, 0x1c2: 0x2f95, 0x1c3: 0x32a1, 0x1c4: 0x300d, 0x1c5: 0x3319,
	0x1c6: 0x3012, 0x1c7: 0x331e, 0x1c8: 0x309e, 0x1c9: 0x33aa, 0x1ca: 0x30a3, 0x1cb: 0x33af,
	0x1cc: 0x3148, 0x1cd: 0x3459, 0x1ce: 0x314d, 0x1cf: 0x345e, 0x1d0: 0x316b, 0x1d1: 0x347c,
	0x1d2: 0x3170, 0x1d3: 0x3481, 0x1d4: 0x31de, 0x1d5: 0x34f4, 0x1d6: 0x31e3, 0x1d7: 0x34f9,
	0x1d8: 0x3189, 0x1d9: 0x349a, 0x1da: 0x31a2, 0x1db: 0x34b8,
	0x1de: 0x305d, 0x1df: 0x3369,
	0x1e6: 0x4695, 0x1e7: 0x4726, 0x1e8: 0x46bd, 0x1e9: 0x474e,
	0x1ea: 0x3962, 0x1eb: 0x3af1, 0x1ec: 0x393f, 0x1ed: 0x3ace, 0x1ee: 0x46db, 0x1ef: 0x476c,
	0x1f0: 0x395b, 0x1f1: 0x3aea, 0x1f2: 0x3247, 0x1f3: 0x3562,
	// Block 0x8, offset 0x200
	0x200: 0x9932, 0x201: 0x9932, 0x202: 0x9932, 0x203: 0x9932, 0x204: 0x9932, 0x205: 0x8132,
	0x206: 0x9932, 0x207: 0x9932, 0x208: 0x9932, 0x209: 0x9932, 0x20a: 0x9932, 0x20b: 0x9932,
	0x20c: 0x9932, 0x20d: 0x8132, 0x20e: 0x8132, 0x20f: 0x9932, 0x210: 0x8132, 0x211: 0x9932,
	0x212: 0x8132, 0x213: 0x9932, 0x214: 0x9932, 0x215: 0x8133, 0x216: 0x812d, 0x217: 0x812d,
	0x218: 0x812d, 0x219: 0x812d, 0x21a: 0x8133, 0x21b: 0x992b, 0x21c: 0x812d, 0x21d: 0x812d,
	0x21e: 0x812d, 0x21f: 0x812d, 0x220: 0x812d, 0x221: 0x8129, 0x222: 0x8129, 0x223: 0x992d,
	0x224: 0x992d, 0x225: 0x992d, 0x226: 0x992d, 0x227: 0x9929, 0x228: 0x9929, 0x229: 0x812d,
	0x22a: 0x812d, 0x22b: 0x812d, 0x22c: 0x812d, 0x22d: 0x992d, 0x22e: 0x992d, 0x22f: 0x812d,
	0x230: 0x992d, 0x231: 0x992d, 0x232: 0x812d, 0x233: 0x812d, 0x234: 0x8101, 0x235: 0x8101,
	0x236: 0x8101, 0x237: 0x8101, 0x238: 0x9901, 0x239: 0x812d, 0x23a: 0x812d, 0x23b: 0x812d,
	0x23c: 0x812d, 0x23d: 0x8132, 0x23e: 0x8132, 0x23f: 0x8132,
	// Block 0x9, offset 0x240
	0x240: 0x49b1, 0x241: 0x49b6, 0x242: 0x9932, 0x243: 0x49bb, 0x244: 0x4a74, 0x245: 0x9936,
	0x246: 0x8132, 0x247: 0x812d, 0x248: 0x812d, 0x249: 0x812d, 0x24a: 0x8132, 0x24b: 0x8132,
	0x24c: 0x8132, 0x24d: 0x812d, 0x24e: 0x812d, 0x250: 0x8132, 0x251: 0x8132,
	0x252: 0x8132, 0x253: 0x812d, 0x254: 0x812d, 0x255: 0x812d, 0x256: 0x812d, 0x257: 0x8132,
	0x258: 0x8133, 0x259: 0x812d, 0x25a: 0x812d, 0x25b: 0x8132, 0x25c: 0x8134, 0x25d: 0x8135,
	0x25e: 0x8135, 0x25f: 0x8134, 0x260: 0x8135, 0x261: 0x8135, 0x262: 0x8134, 0x263: 0x8132,
	0x264: 0x8132, 0x265: 0x8132, 0x266: 0x8132, 0x267: 0x8132, 0x268: 0x8132, 0x269: 0x8132,
	0x26a: 0x8132, 0x26b: 0x8132, 0x26c: 0x8132, 0x26d: 0x8132, 0x26e: 0x8132, 0x26f: 0x8132,
	0x274: 0x0170,
	0x27a: 0x42a8,
	0x27e: 0x0037,
	// Block 0xa, offset 0x280
	0x284: 0x425d, 0x285: 0x447e,
	0x286: 0x35ec, 0x287: 0x00ce, 0x288: 0x360a, 0x289: 0x3616, 0x28a: 0x3628,
	0x28c: 0x3646, 0x28e: 0x3658, 0x28f: 0x3676, 0x290: 0x3e0b, 0x291: 0xa000,
	0x295: 0xa000, 0x297: 0xa000,
	0x299: 0xa000,
	0x29f: 0xa000, 0x2a1: 0xa000,
	0x2a5: 0xa000, 0x2a9: 0xa000,
	0x2aa: 0x363a, 0x2ab: 0x366a, 0x2ac: 0x4801, 0x2ad: 0x369a, 0x2ae: 0x482b, 0x2af: 0x36ac,
	0x2b0: 0x3e73, 0x2b1: 0xa000, 0x2b5: 0xa000,
	0x2b7: 0xa000, 0x2b9: 0xa000,
	0x2bf: 0xa000,
	// Block 0xb, offset 0x2c0
	0x2c1: 0xa000, 0x2c5: 0xa000,
	0x2c9: 0xa000, 0x2ca: 0x4843, 0x2cb: 0x4861,
	0x2cc: 0x36ca, 0x2cd: 0x36e2, 0x2ce: 0x4879, 0x2d0: 0x01be, 0x2d1: 0x01d0,
	0x2d2: 0x01ac, 0x2d3: 0x430f, 0x2d4: 0x4315, 0x2d5: 0x01fa, 0x2d6: 0x01e8,
	0x2f0: 0x01d6, 0x2f1: 0x01eb, 0x2f2: 0x01ee, 0x2f4: 0x0188, 0x2f5: 0x01c7,
	0x2f9: 0x01a6,
	// Block 0xc, offset 0x300
	0x300: 0x3724, 0x301: 0x3730, 0x303: 0x371e,
	0x306: 0xa000, 0x307: 0x370c,
	0x30c: 0x3760, 0x30d: 0x3748, 0x30e: 0x3772, 0x310: 0xa000,
	0x313: 0xa000, 0x315: 0xa000, 0x316: 0xa000, 0x317: 0xa000,
	0x318: 0xa000, 0x319: 0x3754, 0x31a: 0xa000,
	0x31e: 0xa000, 0x323: 0xa000,
	0x327: 0xa000,
	0x32b: 0xa000, 0x32d: 0xa000,
	0x330: 0xa000, 0x333: 0xa000, 0x335: 0xa000,
	0x336: 0xa000, 0x337: 0xa000, 0x338: 0xa000, 0x339: 0x37d8, 0x33a: 0xa000,
	0x33e: 0xa000,
	// Block 0xd, offset 0x340
	0x341: 0x3736, 0x342: 0x37ba,
	0x350: 0x3712, 0x351: 0x3796,
	0x352: 0x3718, 0x353: 0x379c, 0x356: 0x372a, 0x357: 0x37ae,
	0x358: 0xa000, 0x359: 0xa000, 0x35a: 0x382c, 0x35b: 0x3832, 0x35c: 0x373c, 0x35d: 0x37c0,
	0x35e: 0x3742, 0x35f: 0x37c6, 0x362: 0x374e, 0x363: 0x37d2,
	0x364: 0x375a, 0x365: 0x37de, 0x366: 0x3766, 0x367: 0x37ea, 0x368: 0xa000, 0x369: 0xa000,
	0x36a: 0x3838, 0x36b: 0x383e, 0x36c: 0x3790, 0x36d: 0x3814, 0x36e: 0x376c, 0x36f: 0x37f0,
	0x370: 0x3778, 0x371: 0x37fc, 0x372: 0x377e, 0x373: 0x3802, 0x374: 0x3784, 0x375: 0x3808,
	0x378: 0x378a, 0x379: 0x380e,
	// Block 0xe, offset 0x380
	0x387: 0x1d64,
	0x391: 0x812d,
	0x392: 0x8132, 0x393: 0x8132, 0x394: 0x8132, 0x395: 0x8132, 0x396: 0x812d, 0x397: 0x8132,
	0x398: 0x8132, 0x399: 0x8132, 0x39a: 0x812e, 0x39b: 0x812d, 0x39c: 0x8132, 0x39d: 0x8132,
	0x39e: 0x8132, 0x39f: 0x8132, 0x3a0: 0x8132, 0x3a1: 0x8132, 0x3a2: 0x812d, 0x3a3: 0x812d,
	0x3a4: 0x812d, 0x3a5: 0x812d, 0x3a6: 0x812d, 0x3a7: 0x812d, 0x3a8: 0x8132, 0x3a9: 0x8132,
	0x3aa: 0x812d, 0x3ab: 0x8132, 0x3ac: 0x8132, 0x3ad: 0x812e, 0x3ae: 0x8131, 0x3af: 0x8132,
	0x3b0: 0x8105, 0x3b1: 0x8106, 0x3b2: 0x8107, 0x3b3: 0x8108, 0x3b4: 0x8109, 0x3b5: 0x810a,
	0x3b6: 0x810b, 0x3b7: 0x810c, 0x3b8: 0x810d, 0x3b9: 0x810e, 0x3ba: 0x810e, 0x3bb: 0x810f,
	0x3bc: 0x8110, 0x3bd: 0x8111, 0x3bf: 0x8112,
	// Block 0xf, offset 0x3c0
	0x3c8: 0xa000, 0x3ca: 0xa000, 0x3cb: 0x8116,
	0x3cc: 0x8117, 0x3cd: 0x8118, 0x3ce: 0x8119, 0x3cf: 0x811a, 0x3d0: 0x811b, 0x3d1: 0x811c,
	0x3d2: 0x811d, 0x3d3: 0x9932, 0x3d4: 0x9932, 0x3d5: 0x992d, 0x3d6: 0x812d, 0x3d7: 0x8132,
	0x3d8: 0x8132, 0x3d9: 0x8132, 0x3da: 0x8132, 0x3db: 0x8132, 0x3dc: 0x812d, 0x3dd: 0x8132,
	0x3de: 0x8132, 0x3df: 0x812d,
	0x3f0: 0x811e, 0x3f5: 0x1d87,
	0x3f6: 0x2016, 0x3f7: 0x2052, 0x3f8: 0x204d,
	// Block 0x10, offset 0x400
	0x413: 0x812d, 0x414: 0x8132, 0x415: 0x8132, 0x416: 0x8132, 0x417: 0x8132,
	0x418: 0x8132, 0x419: 0x8132, 0x41a: 0x8132, 0x41b: 0x8132, 0x41c: 0x8132, 0x41d: 0x8132,
	0x41e: 0x8132, 0x41f: 0x8132, 0x420: 0x8132, 0x421: 0x8132, 0x423: 0x812d,
	0x424: 0x8132, 0x425: 0x8132, 0x426: 0x812d, 0x427: 0x8132, 0x428: 0x8132, 0x429: 0x812d,
	0x42a: 0x8132, 0x42b: 0x8132, 0x42c: 0x8132, 0x42d: 0x812d, 0x42e: 0x812d, 0x42f: 0x812d,
	0x430: 0x8116, 0x431: 0x8117, 0x432: 0x8118, 0x433: 0x8132, 0x434: 0x8132, 0x435: 0x8132,
	0x436: 0x812d, 0x437: 0x8132, 0x438: 0x8132, 0x439: 0x812d, 0x43a: 0x812d, 0x43b: 0x8132,
	0x43c: 0x8132, 0x43d: 0x8132, 0x43e: 0x8132, 0x43f: 0x8132,
	// Block 0x11, offset 0x440
	0x445: 0xa000,
	0x446: 0x2d29, 0x447: 0xa000, 0x448: 0x2d31, 0x449: 0xa000, 0x44a: 0x2d39, 0x44b: 0xa000,
	0x44c: 0x2d41, 0x44d: 0xa000, 0x44e: 0x2d49, 0x451: 0xa000,
	0x452: 0x2d51,
	0x474: 0x8102, 0x475: 0x9900,
	0x47a: 0xa000, 0x47b: 0x2d59,
	0x47c: 0xa000, 0x47d: 0x2d61, 0x47e: 0xa000, 0x47f: 0xa000,
	// Block 0x12, offset 0x480
	0x480: 0x0069, 0x481: 0x006b, 0x482: 0x006f, 0x483: 0x0083, 0x484: 0x00f5, 0x485: 0x00f8,
	0x486: 0x0413, 0x487: 0x0085, 0x488: 0x0089, 0x489: 0x008b, 0x48a: 0x0104, 0x48b: 0x0107,
	0x48c: 0x010a, 0x48d: 0x008f, 0x48f: 0x0097, 0x490: 0x009b, 0x491: 0x00e0,
	0x492: 0x009f, 0x493: 0x00fe, 0x494: 0x0417, 0x495: 0x041b, 0x496: 0x00a1, 0x497: 0x00a9,
	0x498: 0x00ab, 0x499: 0x0423, 0x49a: 0x012b, 0x49b: 0x00ad, 0x49c: 0x0427, 0x49d: 0x01be,
	0x49e: 0x01c1, 0x49f: 0x01c4, 0x4a0: 0x01fa, 0x4a1: 0x01fd, 0x4a2: 0x0093, 0x4a3: 0x00a5,
	0x4a4: 0x00ab, 0x4a5: 0x00ad, 0x4a6: 0x01be, 0x4a7: 0x01c1, 0x4a8: 0x01eb, 0x4a9: 0x01fa,
	0x4aa: 0x01fd,
	0x4b8: 0x020c,
	// Block 0x13, offset 0x4c0
	0x4db: 0x00fb, 0x4dc: 0x0087, 0x4dd: 0x0101,
	0x4de: 0x00d4, 0x4df: 0x010a, 0x4e0: 0x008d, 0x4e1: 0x010d, 0x4e2: 0x0110, 0x4e3: 0x0116,
	0x4e4: 0x011c, 0x4e5: 0x011f, 0x4e6: 0x0122, 0x4e7: 0x042b, 0x4e8: 0x016a, 0x4e9: 0x0128,
	0x4ea: 0x042f, 0x4eb: 0x016d, 0x4ec: 0x0131, 0x4ed: 0x012e, 0x4ee: 0x0134, 0x4ef: 0x0137,
	0x4f0: 0x013a, 0x4f1: 0x013d, 0x4f2: 0x0140, 0x4f3: 0x014c, 0x4f4: 0x014f, 0x4f5: 0x00ec,
	0x4f6: 0x0152, 0x4f7: 0x0155, 0x4f8: 0x041f, 0x4f9: 0x0158, 0x4fa: 0x015b, 0x4fb: 0x00b5,
	0x4fc: 0x015e, 0x4fd: 0x0161, 0x4fe: 0x0164, 0x4ff: 0x01d0,
	// Block 0x14, offset 0x500
	0x500: 0x8132, 0x501: 0x8132, 0x502: 0x812d, 0x503: 0x8132, 0x504: 0x8132, 0x505: 0x8132,
	0x506: 0x8132, 0x507: 0x8132, 0x508: 0x8132, 0x509: 0x8132, 0x50a: 0x812d, 0x50b: 0x8132,
	0x50c: 0x8132, 0x50d: 0x8135, 0x50e: 0x812a, 0x50f: 0x812d, 0x510: 0x8129, 0x511: 0x8132,
	0x512: 0x8132, 0x513: 0x8132, 0x514: 0x8132, 0x515: 0x8132, 0x516: 0x8132, 0x517: 0x8132,
	0x518: 0x8132, 0x519: 0x8132, 0x51a: 0x8132, 0x51b: 0x8132, 0x51c: 0x8132, 0x51d: 0x8132,
	0x51e: 0x8132, 0x51f: 0x8132, 0x520: 0x8132, 0x521: 0x8132, 0x522: 0x8132, 0x523: 0x8132,
	0x524: 0x8132, 0x525: 0x8132, 0x526: 0x8132, 0x527: 0x8132, 0x528: 0x8132, 0x529: 0x8132,
	0x52a: 0x8132, 0x52b: 0x8132, 0x52c: 0x8132, 0x52d: 0x8132, 0x52e: 0x8132, 0x52f: 0x8132,
	0x530: 0x8132, 0x531: 0x8132, 0x532: 0x8132, 0x533: 0x8132, 0x534: 0x8132, 0x535: 0x8132,
	0x536: 0x8133, 0x537: 0x8131, 0x538: 0x8131, 0x539: 0x812d, 0x53b: 0x8132,
	0x53c: 0x8134, 0x53d: 0x812d, 0x53e: 0x8132, 0x53f: 0x812d,
	// Block 0x15, offset 0x540
	0x540: 0x2f9a, 0x541: 0x32a6, 0x542: 0x2fa4, 0x543: 0x32b0, 0x544: 0x2fa9, 0x545: 0x32b5,
	0x546: 0x2fae, 0x547: 0x32ba, 0x548: 0x38cf, 0x549: 0x3a5e, 0x54a: 0x2fc7, 0x54b: 0x32d3,
	0x54c: 0x2fd1, 0x54d: 0x32dd, 0x54e: 0x2fe0, 0x54f: 0x32ec, 0x550: 0x2fd6, 0x551: 0x32e2,
	0x552: 0x2fdb, 0x553: 0x32e7, 0x554: 0x38f2, 0x555: 0x3a81, 0x556: 0x38f9, 0x557: 0x3a88,
	0x558: 0x301c, 0x559: 0x3328, 0x55a: 0x3021, 0x55b: 0x332d, 0x55c: 0x3907, 0x55d: 0x3a96,
	0x55e: 0x3026, 0x55f: 0x3332, 0x560: 0x3035, 0x561: 0x3341, 0x562: 0x3053, 0x563: 0x335f,
	0x564: 0x3062, 0x565: 0x336e, 0x566: 0x3058, 0x567: 0x3364, 0x568: 0x3067, 0x569: 0x3373,
	0x56a: 0x306c, 0x56b: 0x3378, 0x56c: 0x30b2, 0x56d: 0x33be, 0x56e: 0x390e, 0x56f: 0x3a9d,
	0x570: 0x30bc, 0x571: 0x33cd, 0x572: 0x30c6, 0x573: 0x33d7, 0x574: 0x30d0, 0x575: 0x33e1,
	0x576: 0x46c7, 0x577: 0x4758, 0x578: 0x3915, 0x579: 0x3aa4, 0x57a: 0x30e9, 0x57b: 0x33fa,
	0x57c: 0x30e4, 0x57d: 0x33f5, 0x57e: 0x30ee, 0x57f: 0x33ff,
	// Block 0x16, offset 0x580
	0x580: 0x30f3, 0x581: 0x3404, 0x582: 0x30f8, 0x583: 0x3409, 0x584: 0x310c, 0x585: 0x341d,
	0x586: 0x3116, 0x587: 0x3427, 0x588: 0x3125, 0x589: 0x3436, 0x58a: 0x3120, 0x58b: 0x3431,
	0x58c: 0x3938, 0x58d: 0x3ac7, 0x58e: 0x3946, 0x58f: 0x3ad5, 0x590: 0x394d, 0x591: 0x3adc,
	0x592: 0x3954, 0x593: 0x3ae3, 0x594: 0x3152, 0x595: 0x3463, 0x596: 0x3157, 0x597: 0x3468,
	0x598: 0x3161, 0x599: 0x3472, 0x59a: 0x46f4, 0x59b: 0x4785, 0x59c: 0x399a, 0x59d: 0x3b29,
	0x59e: 0x317a, 0x59f: 0x348b, 0x5a0: 0x3184, 0x5a1: 0x3495, 0x5a2: 0x4703, 0x5a3: 0x4794,
	0x5a4: 0x39a1, 0x5a5: 0x3b30, 0x5a6: 0x39a8, 0x5a7: 0x3b37, 0x5a8: 0x39af, 0x5a9: 0x3b3e,
	0x5aa: 0x3193, 0x5ab: 0x34a4, 0x5ac: 0x319d, 0x5ad: 0x34b3, 0x5ae: 0x31b1, 0x5af: 0x34c7,
	0x5b0: 0x31ac, 0x5b1: 0x34c2, 0x5b2: 0x31ed, 0x5b3: 0x3503, 0x5b4: 0x31fc, 0x5b5: 0x3512,
	0x5b6: 0x31f7, 0x5b7: 0x350d, 0x5b8: 0x39b6, 0x5b9: 0x3b45, 0x5ba: 0x39bd, 0x5bb: 0x3b4c,
	0x5bc: 0x3201, 0x5bd: 0x3517, 0x5be: 0x3206, 0x5bf: 0x351c,
	// Block 0x17, offset 0x5c0
	0x5c0: 0x320b, 0x5c1: 0x3521, 0x5c2: 0x3210, 0x5c3: 0x3526, 0x5c4: 0x321f, 0x5c5: 0x3535,
	0x5c6: 0x321a, 0x5c7: 0x3530, 0x5c8: 0x3224, 0x5c9: 0x353f, 0x5ca: 0x3229, 0x5cb: 0x3544,
	0x5cc: 0x322e, 0x5cd: 0x3549, 0x5ce: 0x324c, 0x5cf: 0x3567, 0x5d0: 0x3265, 0x5d1: 0x3585,
	0x5d2: 0x3274, 0x5d3: 0x3594, 0x5d4: 0x3279, 0x5d5: 0x3599, 0x5d6: 0x337d, 0x5d7: 0x34a9,
	0x5d8: 0x353a, 0x5d9: 0x3576, 0x5da: 0x1be3, 0x5db: 0x42da,
	0x5e0: 0x46a4, 0x5e1: 0x4735, 0x5e2: 0x2f86, 0x5e3: 0x3292,
	0x5e4: 0x387b, 0x5e5: 0x3a0a, 0x5e6: 0x3874, 0x5e7: 0x3a03, 0x5e8: 0x3889, 0x5e9: 0x3a18,
	0x5ea: 0x3882, 0x5eb: 0x3a11, 0x5ec: 0x38c1, 0x5ed: 0x3a50, 0x5ee: 0x3897, 0x5ef: 0x3a26,
	0x5f0: 0x3890, 0x5f1: 0x3a1f, 0x5f2: 0x38a5, 0x5f3: 0x3a34, 0x5f4: 0x389e, 0x5f5: 0x3a2d,
	0x5f6: 0x38c8, 0x5f7: 0x3a57, 0x5f8: 0x46b8, 0x5f9: 0x4749, 0x5fa: 0x3003, 0x5fb: 0x330f,
	0x5fc: 0x2fef, 0x5fd: 0x32fb, 0x5fe: 0x38dd, 0x5ff: 0x3a6c,
	// Block 0x18, offset 0x600
	0x600: 0x38d6, 0x601: 0x3a65, 0x602: 0x38eb, 0x603: 0x3a7a, 0x604: 0x38e4, 0x605: 0x3a73,
	0x606: 0x3900, 0x607: 0x3a8f, 0x608: 0x3094, 0x609: 0x33a0, 0x60a: 0x30a8, 0x60b: 0x33b4,
	0x60c: 0x46ea, 0x60d: 0x477b, 0x60e: 0x3139, 0x60f: 0x344a, 0x610: 0x3923, 0x611: 0x3ab2,
	0x612: 0x391c, 0x613: 0x3aab, 0x614: 0x3931, 0x615: 0x3ac0, 0x616: 0x392a, 0x617: 0x3ab9,
	0x618: 0x398c, 0x619: 0x3b1b, 0x61a: 0x3970, 0x61b: 0x3aff, 0x61c: 0x3969, 0x61d: 0x3af8,
	0x61e: 0x397e, 0x61f: 0x3b0d, 0x620: 0x3977, 0x621: 0x3b06, 0x622: 0x3985, 0x623: 0x3b14,
	0x624: 0x31e8, 0x625: 0x34fe, 0x626: 0x31ca, 0x627: 0x34e0, 0x628: 0x39e7, 0x629: 0x3b76,
	0x62a: 0x39e0, 0x62b: 0x3b6f, 0x62c: 0x39f5, 0x62d: 0x3b84, 0x62e: 0x39ee, 0x62f: 0x3b7d,
	0x630: 0x39fc, 0x631: 0x3b8b, 0x632: 0x3233, 0x633: 0x354e, 0x634: 0x325b, 0x635: 0x357b,
	0x636: 0x3256, 0x637: 0x3571, 0x638: 0x3242, 0x639: 0x355d,
	// Block 0x19, offset 0x640
	0x640: 0x4807, 0x641: 0x480d, 0x642: 0x4921, 0x643: 0x4939, 0x644: 0x4929, 0x645: 0x4941,
	0x646: 0x4931, 0x647: 0x4949, 0x648: 0x47ad, 0x649: 0x47b3, 0x64a: 0x4891, 0x64b: 0x48a9,
	0x64c: 0x4899, 0x64d: 0x48b1, 0x64e: 0x48a1, 0x64f: 0x48b9, 0x650: 0x4819, 0x651: 0x481f,
	0x652: 0x3dbb, 0x653: 0x3dcb, 0x654: 0x3dc3, 0x655: 0x3dd3,
	0x658: 0x47b9, 0x659: 0x47bf, 0x65a: 0x3ceb, 0x65b: 0x3cfb, 0x65c: 0x3cf3, 0x65d: 0x3d03,
	0x660: 0x4831, 0x661: 0x4837, 0x662: 0x4951, 0x663: 0x4969,
	0x664: 0x4959, 0x665: 0x4971, 0x666: 0x4961, 0x667: 0x4979, 0x668: 0x47c5, 0x669: 0x47cb,
	0x66a: 0x48c1, 0x66b: 0x48d9, 0x66c: 0x48c9, 0x66d: 0x48e1, 0x66e: 0x48d1, 0x66f: 0x48e9,
	0x670: 0x4849, 0x671: 0x484f, 0x672: 0x3e1b, 0x673: 0x3e33, 0x674: 0x3e23, 0x675: 0x3e3b,
	0x676: 0x3e2b, 0x677: 0x3e43, 0x678: 0x47d1, 0x679: 0x47d7, 0x67a: 0x3d1b, 0x67b: 0x3d33,
	0x67c: 0x3d23, 0x67d: 0x3d3b, 0x67e: 0x3d2b, 0x67f: 0x3d43,
	// Block 0x1a, offset 0x680
	0x680: 0x4855, 0x681: 0x485b, 0x682: 0x3e4b, 0x683: 0x3e5b, 0x684: 0x3e53, 0x685: 0x3e63,
	0x688: 0x47dd, 0x689: 0x47e3, 0x68a: 0x3d4b, 0x68b: 0x3d5b,
	0x68c: 0x3d53, 0x68d: 0x3d63, 0x690: 0x4867, 0x691: 0x486d,
	0x692: 0x3e83, 0x693: 0x3e9b, 0x694: 0x3e8b, 0x695: 0x3ea3, 0x696: 0x3e93, 0x697: 0x3eab,
	0x699: 0x47e9, 0x69b: 0x3d6b, 0x69d: 0x3d73,
	0x69f: 0x3d7b, 0x6a0: 0x487f, 0x6a1: 0x4885, 0x6a2: 0x4981, 0x6a3: 0x4999,
	0x6a4: 0x4989, 0x6a5: 0x49a1, 0x6a6: 0x4991, 0x6a7: 0x49a9, 0x6a8: 0x47ef, 0x6a9: 0x47f5,
	0x6aa: 0x48f1, 0x6ab: 0x4909, 0x6ac: 0x48f9, 0x6ad: 0x4911, 0x6ae: 0x4901, 0x6af: 0x4919,
	0x6b0: 0x47fb, 0x6b1: 0x4321, 0x6b2: 0x3694, 0x6b3: 0x4327, 0x6b4: 0x4825, 0x6b5: 0x432d,
	0x6b6: 0x36a6, 0x6b7: 0x4333, 0x6b8: 0x36c4, 0x6b9: 0x4339, 0x6ba: 0x36dc, 0x6bb: 0x433f,
	0x6bc: 0x4873, 0x6bd: 0x4345,
	// Block 0x1b, offset 0x6c0
	0x6c0: 0x3da3, 0x6c1: 0x3dab, 0x6c2: 0x4187, 0x6c3: 0x41a5, 0x6c4: 0x4191, 0x6c5: 0x41af,
	0x6c6: 0x419b, 0x6c7: 0x41b9, 0x6c8: 0x3cdb, 0x6c9: 0x3ce3, 0x6ca: 0x40d3, 0x6cb: 0x40f1,
	0x6cc: 0x40dd, 0x6cd: 0x40fb, 0x6ce: 0x40e7, 0x6cf: 0x4105, 0x6d0: 0x3deb, 0x6d1: 0x3df3,
	0x6d2: 0x41c3, 0x6d3: 0x41e1, 0x6d4: 0x41cd, 0x6d5: 0x41eb, 0x6d6: 0x41d7, 0x6d7: 0x41f5,
	0x6d8: 0x3d0b, 0x6d9: 0x3d13, 0x6da: 0x410f, 0x6db: 0x412d, 0x6dc: 0x4119, 0x6dd: 0x4137,
	0x6de: 0x4123, 0x6df: 0x4141, 0x6e0: 0x3ec3, 0x6e1: 0x3ecb, 0x6e2: 0x41ff, 0x6e3: 0x421d,
	0x6e4: 0x4209, 0x6e5: 0x4227, 0x6e6: 0x4213, 0x6e7: 0x4231, 0x6e8: 0x3d83, 0x6e9: 0x3d8b,
	0x6ea: 0x414b, 0x6eb: 0x4169, 0x6ec: 0x4155, 0x6ed: 0x4173, 0x6ee: 0x415f, 0x6ef: 0x417d,
	0x6f0: 0x3688, 0x6f1: 0x3682, 0x6f2: 0x3d93, 0x6f3: 0x368e, 0x6f4: 0x3d9b,
	0x6f6: 0x4813, 0x6f7: 0x3db3, 0x6f8: 0x35f8, 0x6f9: 0x35f2, 0x6fa: 0x35e6, 0x6fb: 0x42f1,
	0x6fc: 0x35fe, 0x6fd: 0x428a, 0x6fe: 0x01d3, 0x6ff: 0x428a,
	// Block 0x1c, offset 0x700
	0x700: 0x42a3, 0x701: 0x4485, 0x702: 0x3ddb, 0x703: 0x36a0, 0x704: 0x3de3,
	0x706: 0x483d, 0x707: 0x3dfb, 0x708: 0x3604, 0x709: 0x42f7, 0x70a: 0x3610, 0x70b: 0x42fd,
	0x70c: 0x361c, 0x70d: 0x448c, 0x70e: 0x4493, 0x70f: 0x449a, 0x710: 0x36b8, 0x711: 0x36b2,
	0x712: 0x3e03, 0x713: 0x44e7, 0x716: 0x36be, 0x717: 0x3e13,
	0x718: 0x3634, 0x719: 0x362e, 0x71a: 0x3622, 0x71b: 0x4303, 0x71d: 0x44a1,
	0x71e: 0x44a8, 0x71f: 0x44af, 0x720: 0x36ee, 0x721: 0x36e8, 0x722: 0x3e6b, 0x723: 0x44ef,
	0x724: 0x36d0, 0x725: 0x36d6, 0x726: 0x36f4, 0x727: 0x3e7b, 0x728: 0x3664, 0x729: 0x365e,
	0x72a: 0x3652, 0x72b: 0x430f, 0x72c: 0x364c, 0x72d: 0x4477, 0x72e: 0x447e, 0x72f: 0x0081,
	0x732: 0x3eb3, 0x733: 0x36fa, 0x734: 0x3ebb,
	0x736: 0x488b, 0x737: 0x3ed3, 0x738: 0x3640, 0x739: 0x4309, 0x73a: 0x3670, 0x73b: 0x431b,
	0x73c: 0x367c, 0x73d: 0x425d, 0x73e: 0x428f,
	// Block 0x1d, offset 0x740
	0x740: 0x1bdb, 0x741: 0x1bdf, 0x742: 0x0047, 0x743: 0x1c57, 0x745: 0x1beb,
	0x746: 0x1bef, 0x747: 0x00e9, 0x749: 0x1c5b, 0x74a: 0x008f, 0x74b: 0x0051,
	0x74c: 0x0051, 0x74d: 0x0051, 0x74e: 0x0091, 0x74f: 0x00da, 0x750: 0x0053, 0x751: 0x0053,
	0x752: 0x0059, 0x753: 0x0099, 0x755: 0x005d, 0x756: 0x1990,
	0x759: 0x0061, 0x75a: 0x0063, 0x75b: 0x0065, 0x75c: 0x0065, 0x75d: 0x0065,
	0x760: 0x19a2, 0x761: 0x1bcb, 0x762: 0x19ab,
	0x764: 0x0075, 0x766: 0x01b8, 0x768: 0x0075,
	0x76a: 0x0057, 0x76b: 0x42d5, 0x76c: 0x0045, 0x76d: 0x0047, 0x76f: 0x008b,
	0x770: 0x004b, 0x771: 0x004d, 0x773: 0x005b, 0x774: 0x009f, 0x775: 0x0215,
	0x776: 0x0218, 0x777: 0x021b, 0x778: 0x021e, 0x779: 0x0093, 0x77b: 0x1b9b,
	0x77c: 0x01e8, 0x77d: 0x01c1, 0x77e: 0x0179, 0x77f: 0x01a0,
	// Block 0x1e, offset 0x780
	0x780: 0x0463, 0x785: 0x0049,
	0x786: 0x0089, 0x787: 0x008b, 0x788: 0x0093, 0x789: 0x0095,
	0x790: 0x2231, 0x791: 0x223d,
	0x792: 0x22f1, 0x793: 0x2219, 0x794: 0x229d, 0x795: 0x2225, 0x796: 0x22a3, 0x797: 0x22bb,
	0x798: 0x22c7, 0x799: 0x222b, 0x79a: 0x22cd, 0x79b: 0x2237, 0x79c: 0x22c1, 0x79d: 0x22d3,
	0x79e: 0x22d9, 0x79f: 0x1cbf, 0x7a0: 0x0053, 0x7a1: 0x195a, 0x7a2: 0x1ba7, 0x7a3: 0x1963,
	0x7a4: 0x006d, 0x7a5: 0x19ae, 0x7a6: 0x1bd3, 0x7a7: 0x1d4b, 0x7a8: 0x1966, 0x7a9: 0x0071,
	0x7aa: 0x19ba, 0x7ab: 0x1bd7, 0x7ac: 0x0059, 0x7ad: 0x0047, 0x7ae: 0x0049, 0x7af: 0x005b,
	0x7b0: 0x0093, 0x7b1: 0x19e7, 0x7b2: 0x1c1b, 0x7b3: 0x19f0, 0x7b4: 0x00ad, 0x7b5: 0x1a65,
	0x7b6: 0x1c4f, 0x7b7: 0x1d5f, 0x7b8: 0x19f3, 0x7b9: 0x00b1, 0x7ba: 0x1a68, 0x7bb: 0x1c53,
	0x7bc: 0x0099, 0x7bd: 0x0087, 0x7be: 0x0089, 0x7bf: 0x009b,
	// Block 0x1f, offset 0x7c0
	0x7c1: 0x3c09, 0x7c3: 0xa000, 0x7c4: 0x3c10, 0x7c5: 0xa000,
	0x7c7: 0x3c17, 0x7c8: 0xa000, 0x7c9: 0x3c1e,
	0x7cd: 0xa000,
	0x7e0: 0x2f68, 0x7e1: 0xa000, 0x7e2: 0x3c2c,
	0x7e4: 0xa000, 0x7e5: 0xa000,
	0x7ed: 0x3c25, 0x7ee: 0x2f63, 0x7ef: 0x2f6d,
	0x7f0: 0x3c33, 0x7f1: 0x3c3a, 0x7f2: 0xa000, 0x7f3: 0xa000, 0x7f4: 0x3c41, 0x7f5: 0x3c48,
	0x7f6: 0xa000, 0x7f7: 0xa000, 0x7f8: 0x3c4f, 0x7f9: 0x3c56, 0x7fa: 0xa000, 0x7fb: 0xa000,
	0x7fc: 0xa000, 0x7fd: 0xa000,
	// Block 0x20, offset 0x800
	0x800: 0x3c5d, 0x801: 0x3c64, 0x802: 0xa000, 0x803: 0xa000, 0x804: 0x3c79, 0x805: 0x3c80,
	0x806: 0xa000, 0x807: 0xa000, 0x808: 0x3c87, 0x809: 0x3c8e,
	0x811: 0xa000,
	0x812: 0xa000,
	0x822: 0xa000,
	0x828: 0xa000, 0x829: 0xa000,
	0x82b: 0xa000, 0x82c: 0x3ca3, 0x82d: 0x3caa, 0x82e: 0x3cb1, 0x82f: 0x3cb8,
	0x832: 0xa000, 0x833: 0xa000, 0x834: 0xa000, 0x835: 0xa000,
	// Block 0x21, offset 0x840
	0x860: 0x0023, 0x861: 0x0025, 0x862: 0x0027, 0x863: 0x0029,
	0x864: 0x002b, 0x865: 0x002d, 0x866: 0x002f, 0x867: 0x0031, 0x868: 0x0033, 0x869: 0x1882,
	0x86a: 0x1885, 0x86b: 0x1888, 0x86c: 0x188b, 0x86d: 0x188e, 0x86e: 0x1891, 0x86f: 0x1894,
	0x870: 0x1897, 0x871: 0x189a, 0x872: 0x189d, 0x873: 0x18a6, 0x874: 0x1a6b, 0x875: 0x1a6f,
	0x876: 0x1a73, 0x877: 0x1a77, 0x878: 0x1a7b, 0x879: 0x1a7f, 0x87a: 0x1a83, 0x87b: 0x1a87,
	0x87c: 0x1a8b, 0x87d: 0x1c83, 0x87e: 0x1c88, 0x87f: 0x1c8d,
	// Block 0x22, offset 0x880
	0x880: 0x1c92, 0x881: 0x1c97, 0x882: 0x1c9c, 0x883: 0x1ca1, 0x884: 0x1ca6, 0x885: 0x1cab,
	0x886: 0x1cb0, 0x887: 0x1cb5, 0x888: 0x187f, 0x889: 0x18a3, 0x88a: 0x18c7, 0x88b: 0x18eb,
	0x88c: 0x190f, 0x88d: 0x1918, 0x88e: 0x191e, 0x88f: 0x1924, 0x890: 0x192a, 0x891: 0x1b63,
	0x892: 0x1b67, 0x893: 0x1b6b, 0x894: 0x1b6f, 0x895: 0x1b73, 0x896: 0x1b77, 0x897: 0x1b7b,
	0x898: 0x1b7f, 0x899: 0x1b83, 0x89a: 0x1b87, 0x89b: 0x1b8b, 0x89c: 0x1af7, 0x89d: 0x1afb,
	0x89e: 0x1aff, 0x89f: 0x1b03, 0x8a0: 0x1b07, 0x8a1: 0x1b0b, 0x8a2: 0x1b0f, 0x8a3: 0x1b13,
	0x8a4: 0x1b17, 0x8a5: 0x1b1b, 0x8a6: 0x1b1f, 0x8a7: 0x1b23, 0x8a8: 0x1b27, 0x8a9: 0x1b2b,
	0x8aa: 0x1b2f, 0x8ab: 0x1b33, 0x8ac: 0x1b37, 0x8ad: 0x1b3b, 0x8ae: 0x1b3f, 0x8af: 0x1b43,
	0x8b0: 0x1b47, 0x8b1: 0x1b4b, 0x8b2: 0x1b4f, 0x8b3: 0x1b53, 0x8b4: 0x1b57, 0x8b5: 0x1b5b,
	0x8b6: 0x0043, 0x8b7: 0x0045, 0x8b8: 0x0047, 0x8b9: 0x0049, 0x8ba: 0x004b, 0x8bb: 0x004d,
	0x8bc: 0x004f, 0x8bd: 0x0051, 0x8be: 0x0053, 0x8bf: 0x0055,
	// Block 0x23, offset 0x8c0
	0x8c0: 0x06bf, 0x8c1: 0x06e3, 0x8c2: 0x06ef, 0x8c3: 0x06ff, 0x8c4: 0x0707, 0x8c5: 0x0713,
	0x8c6: 0x071b, 0x8c7: 0x0723, 0x8c8: 0x072f, 0x8c9: 0x0783, 0x8ca: 0x079b, 0x8cb: 0x07ab,
	0x8cc: 0x07bb, 0x8cd: 0x07cb, 0x8ce: 0x07db, 0x8cf: 0x07fb, 0x8d0: 0x07ff, 0x8d1: 0x0803,
	0x8d2: 0x0837, 0x8d3: 0x085f, 0x8d4: 0x086f, 0x8d5: 0x0877, 0x8d6: 0x087b, 0x8d7: 0x0887,
	0x8d8: 0x08a3, 0x8d9: 0x08a7, 0x8da: 0x08bf, 0x8db: 0x08c3, 0x8dc: 0x08cb, 0x8dd: 0x08db,
	0x8de: 0x0977, 0x8df: 0x098b, 0x8e0: 0x09cb, 0x8e1: 0x09df, 0x8e2: 0x09e7, 0x8e3: 0x09eb,
	0x8e4: 0x09fb, 0x8e5: 0x0a17, 0x8e6: 0x0a43, 0x8e7: 0x0a4f, 0x8e8: 0x0a6f, 0x8e9: 0x0a7b,
	0x8ea: 0x0a7f, 0x8eb: 0x0a83, 0x8ec: 0x0a9b, 0x8ed: 0x0a9f, 0x8ee: 0x0acb, 0x8ef: 0x0ad7,
	0x8f0: 0x0adf, 0x8f1: 0x0ae7, 0x8f2: 0x0af7, 0x8f3: 0x0aff, 0x8f4: 0x0b07, 0x8f5: 0x0b33,
	0x8f6: 0x0b37, 0x8f7: 0x0b3f, 0x8f8: 0x0b43, 0x8f9: 0x0b4b, 0x8fa: 0x0b53, 0x8fb: 0x0b63,
	0x8fc: 0x0b7f, 0x8fd: 0x0bf7, 0x8fe: 0x0c0b, 0x8ff: 0x0c0f,
	// Block 0x24, offset 0x900
	0x900: 0x0c8f, 0x901: 0x0c93, 0x902: 0x0ca7, 0x903: 0x0cab, 0x904: 0x0cb3, 0x905: 0x0cbb,
	0x906: 0x0cc3, 0x907: 0x0ccf, 0x908: 0x0cf7, 0x909: 0x0d07, 0x90a: 0x0d1b, 0x90b: 0x0d8b,
	0x90c: 0x0d97, 0x90d: 0x0da7, 0x90e: 0x0db3, 0x90f: 0x0dbf, 0x910: 0x0dc7, 0x911: 0x0dcb,
	0x912: 0x0dcf, 0x913: 0x0dd3, 0x914: 0x0dd7, 0x915: 0x0e8f, 0x916: 0x0ed7, 0x917: 0x0ee3,
	0x918: 0x0ee7, 0x919: 0x0eeb, 0x91a: 0x0eef, 0x91b: 0x0ef7, 0x91c: 0x0efb, 0x91d: 0x0f0f,
	0x91e: 0x0f2b, 0x91f: 0x0f33, 0x920: 0x0f73, 0x921: 0x0f77, 0x922: 0x0f7f, 0x923: 0x0f83,
	0x924: 0x0f8b, 0x925: 0x0f8f, 0x926: 0x0fb3, 0x927: 0x0fb7, 0x928: 0x0fd3, 0x929: 0x0fd7,
	0x92a: 0x0fdb, 0x92b: 0x0fdf, 0x92c: 0x0ff3, 0x92d: 0x1017, 0x92e: 0x101b, 0x92f: 0x101f,
	0x930: 0x1043, 0x931: 0x1083, 0x932: 0x1087, 0x933: 0x10a7, 0x934: 0x10b7, 0x935: 0x10bf,
	0x936: 0x10df, 0x937: 0x1103, 0x938: 0x1147, 0x939: 0x114f, 0x93a: 0x1163, 0x93b: 0x116f,
	0x93c: 0x1177, 0x93d: 0x117f, 0x93e: 0x1183, 0x93f: 0x1187,
	// Block 0x25, offset 0x940
	0x940: 0x119f, 0x941: 0x11a3, 0x942: 0x11bf, 0x943: 0x11c7, 0x944: 0x11cf, 0x945: 0x11d3,
	0x946: 0x11df, 0x947: 0x11e7, 0x948: 0x11eb, 0x949: 0x11ef, 0x94a: 0x11f7, 0x94b: 0x11fb,
	0x94c: 0x129b, 0x94d: 0x12af, 0x94e: 0x12e3, 0x94f: 0x12e7, 0x950: 0x12ef, 0x951: 0x131b,
	0x952: 0x1323, 0x953: 0x132b, 0x954: 0x1333, 0x955: 0x136f, 0x956: 0x1373, 0x957: 0x137b,
	0x958: 0x137f, 0x959: 0x1383, 0x95a: 0x13af, 0x95b: 0x13b3, 0x95c: 0x13bb, 0x95d: 0x13cf,
	0x95e: 0x13d3, 0x95f: 0x13ef, 0x960: 0x13f7, 0x961: 0x13fb, 0x962: 0x141f, 0x963: 0x143f,
	0x964: 0x1453, 0x965: 0x1457, 0x966: 0x145f, 0x967: 0x148b, 0x968: 0x148f, 0x969: 0x149f,
	0x96a: 0x14c3, 0x96b: 0x14cf, 0x96c: 0x14df, 0x96d: 0x14f7, 0x96e: 0x14ff, 0x96f: 0x1503,
	0x970: 0x1507, 0x971: 0x150b, 0x972: 0x1517, 0x973: 0x151b, 0x974: 0x1523, 0x975: 0x153f,
	0x976: 0x1543, 0x977: 0x1547, 0x978: 0x155f, 0x979: 0x1563, 0x97a: 0x156b, 0x97b: 0x157f,
	0x97c: 0x1583, 0x97d: 0x1587, 0x97e: 0x158f, 0x97f: 0x1593,
	// Block 0x26, offset 0x980
	0x986: 0xa000, 0x98b: 0xa000,
	0x98c: 0x3f0b, 0x98d: 0xa000, 0x98e: 0x3f13, 0x98f: 0xa000, 0x990: 0x3f1b, 0x991: 0xa000,
	0x992: 0x3f23, 0x993: 0xa000, 0x994: 0x3f2b, 0x995: 0xa000, 0x996: 0x3f33, 0x997: 0xa000,
	0x998: 0x3f3b, 0x999: 0xa000, 0x99a: 0x3f43, 0x99b: 0xa000, 0x99c: 0x3f4b, 0x99d: 0xa000,
	0x99e: 0x3f53, 0x99f: 0xa000, 0x9a0: 0x3f5b, 0x9a1: 0xa000, 0x9a2: 0x3f63,
	0x9a4: 0xa000, 0x9a5: 0x3f6b, 0x9a6: 0xa000, 0x9a7: 0x3f73, 0x9a8: 0xa000, 0x9a9: 0x3f7b,
	0x9af: 0xa000,
	0x9b0: 0x3f83, 0x9b1: 0x3f8b, 0x9b2: 0xa000, 0x9b3: 0x3f93, 0x9b4: 0x3f9b, 0x9b5: 0xa000,
	0x9b6: 0x3fa3, 0x9b7: 0x3fab, 0x9b8: 0xa000, 0x9b9: 0x3fb3, 0x9ba: 0x3fbb, 0x9bb: 0xa000,
	0x9bc: 0x3fc3, 0x9bd: 0x3fcb,
	// Block 0x27, offset 0x9c0
	0x9d4: 0x3f03,
	0x9d9: 0x9903, 0x9da: 0x9903, 0x9db: 0x42df, 0x9dc: 0x42e5, 0x9dd: 0xa000,
	0x9de: 0x3fd3, 0x9df: 0x26b7,
	0x9e6: 0xa000,
	0x9eb: 0xa000, 0x9ec: 0x3fe3, 0x9ed: 0xa000, 0x9ee: 0x3feb, 0x9ef: 0xa000,
	0x9f0: 0x3ff3, 0x9f1: 0xa000, 0x9f2: 0x3ffb, 0x9f3: 0xa000, 0x9f4: 0x4003, 0x9f5: 0xa000,
	0x9f6: 0x400b, 0x9f7: 0xa000, 0x9f8: 0x4013, 0x9f9: 0xa000, 0x9fa: 0x401b, 0x9fb: 0xa000,
	0x9fc: 0x4023, 0x9fd: 0xa000, 0x9fe: 0x402b, 0x9ff: 0xa000,
	// Block 0x28, offset 0xa00
	0xa00: 0x4033, 0xa01: 0xa000, 0xa02: 0x403b, 0xa04: 0xa000, 0xa05: 0x4043,
	0xa06: 0xa000, 0xa07: 0x404b, 0xa08: 0xa000, 0xa09: 0x4053,
	0xa0f: 0xa000, 0xa10: 0x405b, 0xa11: 0x4063,
	0xa12: 0xa000, 0xa13: 0x406b, 0xa14: 0x4073, 0xa15: 0xa000, 0xa16: 0x407b, 0xa17: 0x4083,
	0xa18: 0xa000, 0xa19: 0x408b, 0xa1a: 0x4093, 0xa1b: 0xa000, 0xa1c: 0x409b, 0xa1d: 0x40a3,
	0xa2f: 0xa000,
	0xa30: 0xa000, 0xa31: 0xa000, 0xa32: 0xa000, 0xa34: 0x3fdb,
	0xa37: 0x40ab, 0xa38: 0x40b3, 0xa39: 0x40bb, 0xa3a: 0x40c3,
	0xa3d: 0xa000, 0xa3e: 0x40cb, 0xa3f: 0x26cc,
	// Block 0x29, offset 0xa40
	0xa40: 0x0367, 0xa41: 0x032b, 0xa42: 0x032f, 0xa43: 0x0333, 0xa44: 0x037b, 0xa45: 0x0337,
	0xa46: 0x033b, 0xa47: 0x033f, 0xa48: 0x0343, 0xa49: 0x0347, 0xa4a: 0x034b, 0xa4b: 0x034f,
	0xa4c: 0x0353, 0xa4d: 0x0357, 0xa4e: 0x035b, 0xa4f: 0x49c0, 0xa50: 0x49c6, 0xa51: 0x49cc,
	0xa52: 0x49d2, 0xa53: 0x49d8, 0xa54: 0x49de, 0xa55: 0x49e4, 0xa56: 0x49ea, 0xa57: 0x49f0,
	0xa58: 0x49f6, 0xa59: 0x49fc, 0xa5a: 0x4a02, 0xa5b: 0x4a08, 0xa5c: 0x4a0e, 0xa5d: 0x4a14,
	0xa5e: 0x4a1a, 0xa5f: 0x4a20, 0xa60: 0x4a26, 0xa61: 0x4a2c, 0xa62: 0x4a32, 0xa63: 0x4a38,
	0xa64: 0x03c3, 0xa65: 0x035f, 0xa66: 0x0363, 0xa67: 0x03e7, 0xa68: 0x03eb, 0xa69: 0x03ef,
	0xa6a: 0x03f3, 0xa6b: 0x03f7, 0xa6c: 0x03fb, 0xa6d: 0x03ff, 0xa6e: 0x036b, 0xa6f: 0x0403,
	0xa70: 0x0407, 0xa71: 0x036f, 0xa72: 0x0373, 0xa73: 0x0377, 0xa74: 0x037f, 0xa75: 0x0383,
	0xa76: 0x0387, 0xa77: 0x038b, 0xa78: 0x038f, 0xa79: 0x0393, 0xa7a: 0x0397, 0xa7b: 0x039b,
	0xa7c: 0x039f, 0xa7d: 0x03a3, 0xa7e: 0x03a7, 0xa7f: 0x03ab,
	// Block 0x2a, offset 0xa80
	0xa80: 0x03af, 0xa81: 0x03b3, 0xa82: 0x040b, 0xa83: 0x040f, 0xa84: 0x03b7, 0xa85: 0x03bb,
	0xa86: 0x03bf, 0xa87: 0x03c7, 0xa88: 0x03cb, 0xa89: 0x03cf, 0xa8a: 0x03d3, 0xa8b: 0x03d7,
	0xa8c: 0x03db, 0xa8d: 0x03df, 0xa8e: 0x03e3,
	0xa92: 0x06bf, 0xa93: 0x071b, 0xa94: 0x06cb, 0xa95: 0x097b, 0xa96: 0x06cf, 0xa97: 0x06e7,
	0xa98: 0x06d3, 0xa99: 0x0f93, 0xa9a: 0x0707, 0xa9b: 0x06db, 0xa9c: 0x06c3, 0xa9d: 0x09ff,
	0xa9e: 0x098f, 0xa9f: 0x072f,
	// Block 0x2b, offset 0xac0
	0xac0: 0x2057, 0xac1: 0x205d, 0xac2: 0x2063, 0xac3: 0x2069, 0xac4: 0x206f, 0xac5: 0x2075,
	0xac6: 0x207b, 0xac7: 0x2081, 0xac8: 0x2087, 0xac9: 0x208d, 0xaca: 0x2093, 0xacb: 0x2099,
	0xacc: 0x209f, 0xacd: 0x20a5, 0xace: 0x2729, 0xacf: 0x2732, 0xad0: 0x273b, 0xad1: 0x2744,
	0xad2: 0x274d, 0xad3: 0x2756, 0xad4: 0x275f, 0xad5: 0x2768, 0xad6: 0x2771, 0xad7: 0x2783,
	0xad8: 0x278c, 0xad9: 0x2795, 0xada: 0x279e, 0xadb: 0x27a7, 0xadc: 0x277a, 0xadd: 0x2baf,
	0xade: 0x2af0, 0xae0: 0x20ab, 0xae1: 0x20c3, 0xae2: 0x20b7, 0xae3: 0x210b,
	0xae4: 0x20c9, 0xae5: 0x20e7, 0xae6: 0x20b1, 0xae7: 0x20e1, 0xae8: 0x20bd, 0xae9: 0x20f3,
	0xaea: 0x2123, 0xaeb: 0x2141, 0xaec: 0x213b, 0xaed: 0x212f, 0xaee: 0x217d, 0xaef: 0x2111,
	0xaf0: 0x211d, 0xaf1: 0x2135, 0xaf2: 0x2129, 0xaf3: 0x2153, 0xaf4: 0x20ff, 0xaf5: 0x2147,
	0xaf6: 0x2171, 0xaf7: 0x2159, 0xaf8: 0x20ed, 0xaf9: 0x20cf, 0xafa: 0x2105, 0xafb: 0x2117,
	0xafc: 0x214d, 0xafd: 0x20d5, 0xafe: 0x2177, 0xaff: 0x20f9,
	// Block 0x2c, offset 0xb00
	0xb00: 0x215f, 0xb01: 0x20db, 0xb02: 0x2165, 0xb03: 0x216b, 0xb04: 0x092f, 0xb05: 0x0b03,
	0xb06: 0x0ca7, 0xb07: 0x10c7,
	0xb10: 0x1bc7, 0xb11: 0x18a9,
	0xb12: 0x18ac, 0xb13: 0x18af, 0xb14: 0x18b2, 0xb15: 0x18b5, 0xb16: 0x18b8, 0xb17: 0x18bb,
	0xb18: 0x18be, 0xb19: 0x18c1, 0xb1a: 0x18ca, 0xb1b: 0x18cd, 0xb1c: 0x18d0, 0xb1d: 0x18d3,
	0xb1e: 0x18d6, 0xb1f: 0x18d9, 0xb20: 0x0313, 0xb21: 0x031b, 0xb22: 0x031f, 0xb23: 0x0327,
	0xb24: 0x032b, 0xb25: 0x032f, 0xb26: 0x0337, 0xb27: 0x033f, 0xb28: 0x0343, 0xb29: 0x034b,
	0xb2a: 0x034f, 0xb2b: 0x0353, 0xb2c: 0x0357, 0xb2d: 0x035b, 0xb2e: 0x2e1b, 0xb2f: 0x2e23,
	0xb30: 0x2e2b, 0xb31: 0x2e33, 0xb32: 0x2e3b, 0xb33: 0x2e43, 0xb34: 0x2e4b, 0xb35: 0x2e53,
	0xb36: 0x2e63, 0xb37: 0x2e6b, 0xb38: 0x2e73, 0xb39: 0x2e7b, 0xb3a: 0x2e83, 0xb3b: 0x2e8b,
	0xb3c: 0x2ed6, 0xb3d: 0x2e9e, 0xb3e: 0x2e5b,
	// Block 0x2d, offset 0xb40
	0xb40: 0x06bf, 0xb41: 0x071b, 0xb42: 0x06cb, 0xb43: 0x097b, 0xb44: 0x071f, 0xb45: 0x07af,
	0xb46: 0x06c7, 0xb47: 0x07ab, 0xb48: 0x070b, 0xb49: 0x0887, 0xb4a: 0x0d07, 0xb4b: 0x0e8f,
	0xb4c: 0x0dd7, 0xb4d: 0x0d1b, 0xb4e: 0x145f, 0xb4f: 0x098b, 0xb50: 0x0ccf, 0xb51: 0x0d4b,
	0xb52: 0x0d0b, 0xb53: 0x104b, 0xb54: 0x08fb, 0xb55: 0x0f03, 0xb56: 0x1387, 0xb57: 0x105f,
	0xb58: 0x0843, 0xb59: 0x108f, 0xb5a: 0x0f9b, 0xb5b: 0x0a17, 0xb5c: 0x140f, 0xb5d: 0x077f,
	0xb5e: 0x08ab, 0xb5f: 0x0df7, 0xb60: 0x1527, 0xb61: 0x0743, 0xb62: 0x07d3, 0xb63: 0x0d9b,
	0xb64: 0x06cf, 0xb65: 0x06e7, 0xb66: 0x06d3, 0xb67: 0x0adb, 0xb68: 0x08ef, 0xb69: 0x087f,
	0xb6a: 0x0a57, 0xb6b: 0x0a4b, 0xb6c: 0x0feb, 0xb6d: 0x073f, 0xb6e: 0x139b, 0xb6f: 0x089b,
	0xb70: 0x09f3, 0xb71: 0x18dc, 0xb72: 0x18df, 0xb73: 0x18e2, 0xb74: 0x18e5, 0xb75: 0x18ee,
	0xb76: 0x18f1, 0xb77: 0x18f4, 0xb78: 0x18f7, 0xb79: 0x18fa, 0xb7a: 0x18fd, 0xb7b: 0x1900,
	0xb7c: 0x1903, 0xb7d: 0x1906, 0xb7e: 0x1909, 0xb7f: 0x1912,
	// Block 0x2e, offset 0xb80
	0xb80: 0x1cc9, 0xb81: 0x1cd8, 0xb82: 0x1ce7, 0xb83: 0x1cf6, 0xb84: 0x1d05, 0xb85: 0x1d14,
	0xb86: 0x1d23, 0xb87: 0x1d32, 0xb88: 0x1d41, 0xb89: 0x218f, 0xb8a: 0x21a1, 0xb8b: 0x21b3,
	0xb8c: 0x1954, 0xb8d: 0x1c07, 0xb8e: 0x19d5, 0xb8f: 0x1bab, 0xb90: 0x04cb, 0xb91: 0x04d3,
	0xb92: 0x04db, 0xb93: 0x04e3, 0xb94: 0x04eb, 0xb95: 0x04ef, 0xb96: 0x04f3, 0xb97: 0x04f7,
	0xb98: 0x04fb, 0xb99: 0x04ff, 0xb9a: 0x0503, 0xb9b: 0x0507, 0xb9c: 0x050b, 0xb9d: 0x050f,
	0xb9e: 0x0513, 0xb9f: 0x0517, 0xba0: 0x051b, 0xba1: 0x0523, 0xba2: 0x0527, 0xba3: 0x052b,
	0xba4: 0x052f, 0xba5: 0x0533, 0xba6: 0x0537, 0xba7: 0x053b, 0xba8: 0x053f, 0xba9: 0x0543,
	0xbaa: 0x0547, 0xbab: 0x054b, 0xbac: 0x054f, 0xbad: 0x0553, 0xbae: 0x0557, 0xbaf: 0x055b,
	0xbb0: 0x055f, 0xbb1: 0x0563, 0xbb2: 0x0567, 0xbb3: 0x056f, 0xbb4: 0x0577, 0xbb5: 0x057f,
	0xbb6: 0x0583, 0xbb7: 0x0587, 0xbb8: 0x058b, 0xbb9: 0x058f, 0xbba: 0x0593, 0xbbb: 0x0597,
	0xbbc: 0x059b, 0xbbd: 0x059f, 0xbbe: 0x05a3,
	// Block 0x2f, offset 0xbc0
	0xbc0: 0x2b0f, 0xbc1: 0x29ab, 0xbc2: 0x2b1f, 0xbc3: 0x2883, 0xbc4: 0x2ee7, 0xbc5: 0x288d,
	0xbc6: 0x2897, 0xbc7: 0x2f2b, 0xbc8: 0x29b8, 0xbc9: 0x28a1, 0xbca: 0x28ab, 0xbcb: 0x28b5,
	0xbcc: 0x29df, 0xbcd: 0x29ec, 0xbce: 0x29c5, 0xbcf: 0x29d2, 0xbd0: 0x2eac, 0xbd1: 0x29f9,
	0xbd2: 0x2a06, 0xbd3: 0x2bc1, 0xbd4: 0x26be, 0xbd5: 0x2bd4, 0xbd6: 0x2be7, 0xbd7: 0x2b2f,
	0xbd8: 0x2a13, 0xbd9: 0x2bfa, 0xbda: 0x2c0d, 0xbdb: 0x2a20, 0xbdc: 0x28bf, 0xbdd: 0x28c9,
	0xbde: 0x2eba, 0xbdf: 0x2a2d, 0xbe0: 0x2b3f, 0xbe1: 0x2ef8, 0xbe2: 0x28d3, 0xbe3: 0x28dd,
	0xbe4: 0x2a3a, 0xbe5: 0x28e7, 0xbe6: 0x28f1, 0xbe7: 0x26d3, 0xbe8: 0x26da, 0xbe9: 0x28fb,
	0xbea: 0x2905, 0xbeb: 0x2c20, 0xbec: 0x2a47, 0xbed: 0x2b4f, 0xbee: 0x2c33, 0xbef: 0x2a54,
	0xbf0: 0x2919, 0xbf1: 0x290f, 0xbf2: 0x2f3f, 0xbf3: 0x2a61, 0xbf4: 0x2c46, 0xbf5: 0x2923,
	0xbf6: 0x2b5f, 0xbf7: 0x292d, 0xbf8: 0x2a7b, 0xbf9: 0x2937, 0xbfa: 0x2a88, 0xbfb: 0x2f09,
	0xbfc: 0x2a6e, 0xbfd: 0x2b6f, 0xbfe: 0x2a95, 0xbff: 0x26e1,
	// Block 0x30, offset 0xc00
	0xc00: 0x2f1a, 0xc01: 0x2941, 0xc02: 0x294b, 0xc03: 0x2aa2, 0xc04: 0x2955, 0xc05: 0x295f,
	0xc06: 0x2969, 0xc07: 0x2b7f, 0xc08: 0x2aaf, 0xc09: 0x26e8, 0xc0a: 0x2c59, 0xc0b: 0x2e93,
	0xc0c: 0x2b8f, 0xc0d: 0x2abc, 0xc0e: 0x2ec8, 0xc0f: 0x2973, 0xc10: 0x297d, 0xc11: 0x2ac9,
	0xc12: 0x26ef, 0xc13: 0x2ad6, 0xc14: 0x2b9f, 0xc15: 0x26f6, 0xc16: 0x2c6c, 0xc17: 0x2987,
	0xc18: 0x1cba, 0xc19: 0x1cce, 0xc1a: 0x1cdd, 0xc1b: 0x1cec, 0xc1c: 0x1cfb, 0xc1d: 0x1d0a,
	0xc1e: 0x1d19, 0xc1f: 0x1d28, 0xc20: 0x1d37, 0xc21: 0x1d46, 0xc22: 0x2195, 0xc23: 0x21a7,
	0xc24: 0x21b9, 0xc25: 0x21c5, 0xc26: 0x21d1, 0xc27: 0x21dd, 0xc28: 0x21e9, 0xc29: 0x21f5,
	0xc2a: 0x2201, 0xc2b: 0x220d, 0xc2c: 0x2249, 0xc2d: 0x2255, 0xc2e: 0x2261, 0xc2f: 0x226d,
	0xc30: 0x2279, 0xc31: 0x1c17, 0xc32: 0x19c9, 0xc33: 0x1936, 0xc34: 0x1be7, 0xc35: 0x1a4a,
	0xc36: 0x1a59, 0xc37: 0x19cf, 0xc38: 0x1bff, 0xc39: 0x1c03, 0xc3a: 0x1960, 0xc3b: 0x2704,
	0xc3c: 0x2712, 0xc3d: 0x26fd, 0xc3e: 0x270b, 0xc3f: 0x2ae3,
	// Block 0x31, offset 0xc40
	0xc40: 0x1a4d, 0xc41: 0x1a35, 0xc42: 0x1c63, 0xc43: 0x1a1d, 0xc44: 0x19f6, 0xc45: 0x1969,
	0xc46: 0x1978, 0xc47: 0x1948, 0xc48: 0x1bf3, 0xc49: 0x1d55, 0xc4a: 0x1a50, 0xc4b: 0x1a38,
	0xc4c: 0x1c67, 0xc4d: 0x1c73, 0xc4e: 0x1a29, 0xc4f: 0x19ff, 0xc50: 0x1957, 0xc51: 0x1c1f,
	0xc52: 0x1bb3, 0xc53: 0x1b9f, 0xc54: 0x1bcf, 0xc55: 0x1c77, 0xc56: 0x1a2c, 0xc57: 0x19cc,
	0xc58: 0x1a02, 0xc59: 0x19e1, 0xc5a: 0x1a44, 0xc5b: 0x1c7b, 0xc5c: 0x1a2f, 0xc5d: 0x19c3,
	0xc5e: 0x1a05, 0xc5f: 0x1c3f, 0xc60: 0x1bf7, 0xc61: 0x1a17, 0xc62: 0x1c27, 0xc63: 0x1c43,
	0xc64: 0x1bfb, 0xc65: 0x1a1a, 0xc66: 0x1c2b, 0xc67: 0x22eb, 0xc68: 0x22ff, 0xc69: 0x1999,
	0xc6a: 0x1c23, 0xc6b: 0x1bb7, 0xc6c: 0x1ba3, 0xc6d: 0x1c4b, 0xc6e: 0x2719, 0xc6f: 0x27b0,
	0xc70: 0x1a5c, 0xc71: 0x1a47, 0xc72: 0x1c7f, 0xc73: 0x1a32, 0xc74: 0x1a53, 0xc75: 0x1a3b,
	0xc76: 0x1c6b, 0xc77: 0x1a20, 0xc78: 0x19f9, 0xc79: 0x1984, 0xc7a: 0x1a56, 0xc7b: 0x1a3e,
	0xc7c: 0x1c6f, 0xc7d: 0x1a23, 0xc7e: 0x19fc, 0xc7f: 0x1987,
	// Block 0x32, offset 0xc80
	0xc80: 0x1c2f, 0xc81: 0x1bbb, 0xc82: 0x1d50, 0xc83: 0x1939, 0xc84: 0x19bd, 0xc85: 0x19c0,
	0xc86: 0x22f8, 0xc87: 0x1b97, 0xc88: 0x19c6, 0xc89: 0x194b, 0xc8a: 0x19e4, 0xc8b: 0x194e,
	0xc8c: 0x19ed, 0xc8d: 0x196c, 0xc8e: 0x196f, 0xc8f: 0x1a08, 0xc90: 0x1a0e, 0xc91: 0x1a11,
	0xc92: 0x1c33, 0xc93: 0x1a14, 0xc94: 0x1a26, 0xc95: 0x1c3b, 0xc96: 0x1c47, 0xc97: 0x1993,
	0xc98: 0x1d5a, 0xc99: 0x1bbf, 0xc9a: 0x1996, 0xc9b: 0x1a5f, 0xc9c: 0x19a8, 0xc9d: 0x19b7,
	0xc9e: 0x22e5, 0xc9f: 0x22df, 0xca0: 0x1cc4, 0xca1: 0x1cd3, 0xca2: 0x1ce2, 0xca3: 0x1cf1,
	0xca4: 0x1d00, 0xca5: 0x1d0f, 0xca6: 0x1d1e, 0xca7: 0x1d2d, 0xca8: 0x1d3c, 0xca9: 0x2189,
	0xcaa: 0x219b, 0xcab: 0x21ad, 0xcac: 0x21bf, 0xcad: 0x21cb, 0xcae: 0x21d7, 0xcaf: 0x21e3,
	0xcb0: 0x21ef, 0xcb1: 0x21fb, 0xcb2: 0x2207, 0xcb3: 0x2243, 0xcb4: 0x224f, 0xcb5: 0x225b,
	0xcb6: 0x2267, 0xcb7: 0x2273, 0xcb8: 0x227f, 0xcb9: 0x2285, 0xcba: 0x228b, 0xcbb: 0x2291,
	0xcbc: 0x2297, 0xcbd: 0x22a9, 0xcbe: 0x22af, 0xcbf: 0x1c13,
	// Block 0x33, offset 0xcc0
	0xcc0: 0x1377, 0xcc1: 0x0cfb, 0xcc2: 0x13d3, 0xcc3: 0x139f, 0xcc4: 0x0e57, 0xcc5: 0x06eb,
	0xcc6: 0x08df, 0xcc7: 0x162b, 0xcc8: 0x162b, 0xcc9: 0x0a0b, 0xcca: 0x145f, 0xccb: 0x0943,
	0xccc: 0x0a07, 0xccd: 0x0bef, 0xcce: 0x0fcf, 0xccf: 0x115f, 0xcd0: 0x1297, 0xcd1: 0x12d3,
	0xcd2: 0x1307, 0xcd3: 0x141b, 0xcd4: 0x0d73, 0xcd5: 0x0dff, 0xcd6: 0x0eab, 0xcd7: 0x0f43,
	0xcd8: 0x125f, 0xcd9: 0x1447, 0xcda: 0x1573, 0xcdb: 0x070f, 0xcdc: 0x08b3, 0xcdd: 0x0d87,
	0xcde: 0x0ecf, 0xcdf: 0x1293, 0xce0: 0x15c3, 0xce1: 0x0ab3, 0xce2: 0x0e77, 0xce3: 0x1283,
	0xce4: 0x1317, 0xce5: 0x0c23, 0xce6: 0x11bb, 0xce7: 0x12df, 0xce8: 0x0b1f, 0xce9: 0x0d0f,
	0xcea: 0x0e17, 0xceb: 0x0f1b, 0xcec: 0x1427, 0xced: 0x074f, 0xcee: 0x07e7, 0xcef: 0x0853,
	0xcf0: 0x0c8b, 0xcf1: 0x0d7f, 0xcf2: 0x0ecb, 0xcf3: 0x0fef, 0xcf4: 0x1177, 0xcf5: 0x128b,
	0xcf6: 0x12a3, 0xcf7: 0x13c7, 0xcf8: 0x14ef, 0xcf9: 0x15a3, 0xcfa: 0x15bf, 0xcfb: 0x102b,
	0xcfc: 0x106b, 0xcfd: 0x1123, 0xcfe: 0x1243, 0xcff: 0x147b,
	// Block 0x34, offset 0xd00
	0xd00: 0x15cb, 0xd01: 0x134b, 0xd02: 0x09c7, 0xd03: 0x0b3b, 0xd04: 0x10db, 0xd05: 0x119b,
	0xd06: 0x0eff, 0xd07: 0x1033, 0xd08: 0x1397, 0xd09: 0x14e7, 0xd0a: 0x09c3, 0xd0b: 0x0a8f,
	0xd0c: 0x0d77, 0xd0d: 0x0e2b, 0xd0e: 0x0e5f, 0xd0f: 0x1113, 0xd10: 0x113b, 0xd11: 0x14a7,
	0xd12: 0x084f, 0xd13: 0x11a7, 0xd14: 0x07f3, 0xd15: 0x07ef, 0xd16: 0x1097, 0xd17: 0x1127,
	0xd18: 0x125b, 0xd19: 0x14af, 0xd1a: 0x1367, 0xd1b: 0x0c27, 0xd1c: 0x0d73, 0xd1d: 0x1357,
	0xd1e: 0x06f7, 0xd1f: 0x0a63, 0xd20: 0x0b93, 0xd21: 0x0f2f, 0xd22: 0x0faf, 0xd23: 0x0873,
	0xd24: 0x103b, 0xd25: 0x075f, 0xd26: 0x0b77, 0xd27: 0x06d7, 0xd28: 0x0deb, 0xd29: 0x0ca3,
	0xd2a: 0x110f, 0xd2b: 0x08c7, 0xd2c: 0x09b3, 0xd2d: 0x0ffb, 0xd2e: 0x1263, 0xd2f: 0x133b,
	0xd30: 0x0db7, 0xd31: 0x13f7, 0xd32: 0x0de3, 0xd33: 0x0c37, 0xd34: 0x121b, 0xd35: 0x0c57,
	0xd36: 0x0fab, 0xd37: 0x072b, 0xd38: 0x07a7, 0xd39: 0x07eb, 0xd3a: 0x0d53, 0xd3b: 0x10fb,
	0xd3c: 0x11f3, 0xd3d: 0x1347, 0xd3e: 0x145b, 0xd3f: 0x085b,
	// Block 0x35, offset 0xd40
	0xd40: 0x090f, 0xd41: 0x0a17, 0xd42: 0x0b2f, 0xd43: 0x0cbf, 0xd44: 0x0e7b, 0xd45: 0x103f,
	0xd46: 0x1497, 0xd47: 0x157b, 0xd48: 0x15cf, 0xd49: 0x15e7, 0xd4a: 0x0837, 0xd4b: 0x0cf3,
	0xd4c: 0x0da3, 0xd4d: 0x13eb, 0xd4e: 0x0afb, 0xd4f: 0x0bd7, 0xd50: 0x0bf3, 0xd51: 0x0c83,
	0xd52: 0x0e6b, 0xd53: 0x0eb7, 0xd54: 0x0f67, 0xd55: 0x108b, 0xd56: 0x112f, 0xd57: 0x1193,
	0xd58: 0x13db, 0xd59: 0x126b, 0xd5a: 0x1403, 0xd5b: 0x147f, 0xd5c: 0x080f, 0xd5d: 0x083b,
	0xd5e: 0x0923, 0xd5f: 0x0ea7, 0xd60: 0x12f3, 0xd61: 0x133b, 0xd62: 0x0b1b, 0xd63: 0x0b8b,
	0xd64: 0x0c4f, 0xd65: 0x0daf, 0xd66: 0x10d7, 0xd67: 0x0f23, 0xd68: 0x073b, 0xd69: 0x097f,
	0xd6a: 0x0a63, 0xd6b: 0x0ac7, 0xd6c: 0x0b97, 0xd6d: 0x0f3f, 0xd6e: 0x0f5b, 0xd6f: 0x116b,
	0xd70: 0x118b, 0xd71: 0x1463, 0xd72: 0x14e3, 0xd73: 0x14f3, 0xd74: 0x152f, 0xd75: 0x0753,
	0xd76: 0x107f, 0xd77: 0x144f, 0xd78: 0x14cb, 0xd79: 0x0baf, 0xd7a: 0x0717, 0xd7b: 0x0777,
	0xd7c: 0x0a67, 0xd7d: 0x0a87, 0xd7e: 0x0caf, 0xd7f: 0x0d73,
	// Block 0x36, offset 0xd80
	0xd80: 0x0ec3, 0xd81: 0x0fcb, 0xd82: 0x1277, 0xd83: 0x1417, 0xd84: 0x1623, 0xd85: 0x0ce3,
	0xd86: 0x14a3, 0xd87: 0x0833, 0xd88: 0x0d2f, 0xd89: 0x0d3b, 0xd8a: 0x0e0f, 0xd8b: 0x0e47,
	0xd8c: 0x0f4b, 0xd8d: 0x0fa7, 0xd8e: 0x1027, 0xd8f: 0x110b, 0xd90: 0x153b, 0xd91: 0x07af,
	0xd92: 0x0c03, 0xd93: 0x14b3, 0xd94: 0x0767, 0xd95: 0x0aab, 0xd96: 0x0e2f, 0xd97: 0x13df,
	0xd98: 0x0b67, 0xd99: 0x0bb7, 0xd9a: 0x0d43, 0xd9b: 0x0f2f, 0xd9c: 0x14bb, 0xd9d: 0x0817,
	0xd9e: 0x08ff, 0xd9f: 0x0a97, 0xda0: 0x0cd3, 0xda1: 0x0d1f, 0xda2: 0x0d5f, 0xda3: 0x0df3,
	0xda4: 0x0f47, 0xda5: 0x0fbb, 0xda6: 0x1157, 0xda7: 0x12f7, 0xda8: 0x1303, 0xda9: 0x1457,
	0xdaa: 0x14d7, 0xdab: 0x0883, 0xdac: 0x0e4b, 0xdad: 0x0903, 0xdae: 0x0ec7, 0xdaf: 0x0f6b,
	0xdb0: 0x1287, 0xdb1: 0x14bf, 0xdb2: 0x15ab, 0xdb3: 0x15d3, 0xdb4: 0x0d37, 0xdb5: 0x0e27,
	0xdb6: 0x11c3, 0xdb7: 0x10b7, 0xdb8: 0x10c3, 0xdb9: 0x10e7, 0xdba: 0x0f17, 0xdbb: 0x0e9f,
	0xdbc: 0x1363, 0xdbd: 0x0733, 0xdbe: 0x122b, 0xdbf: 0x081b,
	// Block 0x37, offset 0xdc0
	0xdc0: 0x080b, 0xdc1: 0x0b0b, 0xdc2: 0x0c2b, 0xdc3: 0x10f3, 0xdc4: 0x0a53, 0xdc5: 0x0e03,
	0xdc6: 0x0cef, 0xdc7: 0x13e7, 0xdc8: 0x12e7, 0xdc9: 0x14ab, 0xdca: 0x1323, 0xdcb: 0x0b27,
	0xdcc: 0x0787, 0xdcd: 0x095b, 0xdd0: 0x09af,
	0xdd2: 0x0cdf, 0xdd5: 0x07f7, 0xdd6: 0x0f1f, 0xdd7: 0x0fe3,
	0xdd8: 0x1047, 0xdd9: 0x1063, 0xdda: 0x1067, 0xddb: 0x107b, 0xddc: 0x14fb, 0xddd: 0x10eb,
	0xdde: 0x116f, 0xde0: 0x128f, 0xde2: 0x1353,
	0xde5: 0x1407, 0xde6: 0x1433,
	0xdea: 0x154f, 0xdeb: 0x1553, 0xdec: 0x1557, 0xded: 0x15bb, 0xdee: 0x142b, 0xdef: 0x14c7,
	0xdf0: 0x0757, 0xdf1: 0x077b, 0xdf2: 0x078f, 0xdf3: 0x084b, 0xdf4: 0x0857, 0xdf5: 0x0897,
	0xdf6: 0x094b, 0xdf7: 0x0967, 0xdf8: 0x096f, 0xdf9: 0x09ab, 0xdfa: 0x09b7, 0xdfb: 0x0a93,
	0xdfc: 0x0a9b, 0xdfd: 0x0ba3, 0xdfe: 0x0bcb, 0xdff: 0x0bd3,
	// Block 0x38, offset 0xe00
	0xe00: 0x0beb, 0xe01: 0x0c97, 0xe02: 0x0cc7, 0xe03: 0x0ce7, 0xe04: 0x0d57, 0xe05: 0x0e1b,
	0xe06: 0x0e37, 0xe07: 0x0e67, 0xe08: 0x0ebb, 0xe09: 0x0edb, 0xe0a: 0x0f4f, 0xe0b: 0x102f,
	0xe0c: 0x104b, 0xe0d: 0x1053, 0xe0e: 0x104f, 0xe0f: 0x1057, 0xe10: 0x105b, 0xe11: 0x105f,
	0xe12: 0x1073, 0xe13: 0x1077, 0xe14: 0x109b, 0xe15: 0x10af, 0xe16: 0x10cb, 0xe17: 0x112f,
	0xe18: 0x1137, 0xe19: 0x113f, 0xe1a: 0x1153, 0xe1b: 0x117b, 0xe1c: 0x11cb, 0xe1d: 0x11ff,
	0xe1e: 0x11ff, 0xe1f: 0x1267, 0xe20: 0x130f, 0xe21: 0x1327, 0xe22: 0x135b, 0xe23: 0x135f,
	0xe24: 0x13a3, 0xe25: 0x13a7, 0xe26: 0x13ff, 0xe27: 0x1407, 0xe28: 0x14db, 0xe29: 0x151f,
	0xe2a: 0x1537, 0xe2b: 0x0b9b, 0xe2c: 0x171e, 0xe2d: 0x11e3,
	0xe30: 0x06df, 0xe31: 0x07e3, 0xe32: 0x07a3, 0xe33: 0x074b, 0xe34: 0x078b, 0xe35: 0x07b7,
	0xe36: 0x0847, 0xe37: 0x0863, 0xe38: 0x094b, 0xe39: 0x0937, 0xe3a: 0x0947, 0xe3b: 0x0963,
	0xe3c: 0x09af, 0xe3d: 0x09bf, 0xe3e: 0x0a03, 0xe3f: 0x0a0f,
	// Block 0x39, offset 0xe40
	0xe40: 0x0a2b, 0xe41: 0x0a3b, 0xe42: 0x0b23, 0xe43: 0x0b2b, 0xe44: 0x0b5b, 0xe45: 0x0b7b,
	0xe46: 0x0bab, 0xe47: 0x0bc3, 0xe48: 0x0bb3, 0xe49: 0x0bd3, 0xe4a: 0x0bc7, 0xe4b: 0x0beb,
	0xe4c: 0x0c07, 0xe4d: 0x0c5f, 0xe4e: 0x0c6b, 0xe4f: 0x0c73, 0xe50: 0x0c9b, 0xe51: 0x0cdf,
	0xe52: 0x0d0f, 0xe53: 0x0d13, 0xe54: 0x0d27, 0xe55: 0x0da7, 0xe56: 0x0db7, 0xe57: 0x0e0f,
	0xe58: 0x0e5b, 0xe59: 0x0e53, 0xe5a: 0x0e67, 0xe5b: 0x0e83, 0xe5c: 0x0ebb, 0xe5d: 0x1013,
	0xe5e: 0x0edf, 0xe5f: 0x0f13, 0xe60: 0x0f1f, 0xe61: 0x0f5f, 0xe62: 0x0f7b, 0xe63: 0x0f9f,
	0xe64: 0x0fc3, 0xe65: 0x0fc7, 0xe66: 0x0fe3, 0xe67: 0x0fe7, 0xe68: 0x0ff7, 0xe69: 0x100b,
	0xe6a: 0x1007, 0xe6b: 0x1037, 0xe6c: 0x10b3, 0xe6d: 0x10cb, 0xe6e: 0x10e3, 0xe6f: 0x111b,
	0xe70: 0x112f, 0xe71: 0x114b, 0xe72: 0x117b, 0xe73: 0x122f, 0xe74: 0x1257, 0xe75: 0x12cb,
	0xe76: 0x1313, 0xe77: 0x131f, 0xe78: 0x1327, 0xe79: 0x133f, 0xe7a: 0x1353, 0xe7b: 0x1343,
	0xe7c: 0x135b, 0xe7d: 0x1357, 0xe7e: 0x134f, 0xe7f: 0x135f,
	// Block 0x3a, offset 0xe80
	0xe80: 0x136b, 0xe81: 0x13a7, 0xe82: 0x13e3, 0xe83: 0x1413, 0xe84: 0x144b, 0xe85: 0x146b,
	0xe86: 0x14b7, 0xe87: 0x14db, 0xe88: 0x14fb, 0xe89: 0x150f, 0xe8a: 0x151f, 0xe8b: 0x152b,
	0xe8c: 0x1537, 0xe8d: 0x158b, 0xe8e: 0x162b, 0xe8f: 0x16b5, 0xe90: 0x16b0, 0xe91: 0x16e2,
	0xe92: 0x0607, 0xe93: 0x062f, 0xe94: 0x0633, 0xe95: 0x1764, 0xe96: 0x1791, 0xe97: 0x1809,
	0xe98: 0x1617, 0xe99: 0x1627,
	// Block 0x3b, offset 0xec0
	0xec0: 0x19d8, 0xec1: 0x19db, 0xec2: 0x19de, 0xec3: 0x1c0b, 0xec4: 0x1c0f, 0xec5: 0x1a62,
	0xec6: 0x1a62,
	0xed3: 0x1d78, 0xed4: 0x1d69, 0xed5: 0x1d6e, 0xed6: 0x1d7d, 0xed7: 0x1d73,
	0xedd: 0x4393,
	0xede: 0x8115, 0xedf: 0x4405, 0xee0: 0x022d, 0xee1: 0x0215, 0xee2: 0x021e, 0xee3: 0x0221,
	0xee4: 0x0224, 0xee5: 0x0227, 0xee6: 0x022a, 0xee7: 0x0230, 0xee8: 0x0233, 0xee9: 0x0017,
	0xeea: 0x43f3, 0xeeb: 0x43f9, 0xeec: 0x44f7, 0xeed: 0x44ff, 0xeee: 0x434b, 0xeef: 0x4351,
	0xef0: 0x4357, 0xef1: 0x435d, 0xef2: 0x4369, 0xef3: 0x436f, 0xef4: 0x4375, 0xef5: 0x4381,
	0xef6: 0x4387, 0xef8: 0x438d, 0xef9: 0x4399, 0xefa: 0x439f, 0xefb: 0x43a5,
	0xefc: 0x43b1, 0xefe: 0x43b7,
	// Block 0x3c, offset 0xf00
	0xf00: 0x43bd, 0xf01: 0x43c3, 0xf03: 0x43c9, 0xf04: 0x43cf,
	0xf06: 0x43db, 0xf07: 0x43e1, 0xf08: 0x43e7, 0xf09: 0x43ed, 0xf0a: 0x43ff, 0xf0b: 0x437b,
	0xf0c: 0x4363, 0xf0d: 0x43ab, 0xf0e: 0x43d5, 0xf0f: 0x1d82, 0xf10: 0x0299, 0xf11: 0x0299,
	0xf12: 0x02a2, 0xf13: 0x02a2, 0xf14: 0x02a2, 0xf15: 0x02a2, 0xf16: 0x02a5, 0xf17: 0x02a5,
	0xf18: 0x02a5, 0xf19: 0x02a5, 0xf1a: 0x02ab, 0xf1b: 0x02ab, 0xf1c: 0x02ab, 0xf1d: 0x02ab,
	0xf1e: 0x029f, 0xf1f: 0x029f, 0xf20: 0x029f, 0xf21: 0x029f, 0xf22: 0x02a8, 0xf23: 0x02a8,
	0xf24: 0x02a8, 0xf25: 0x02a8, 0xf26: 0x029c, 0xf27: 0x029c, 0xf28: 0x029c, 0xf29: 0x029c,
	0xf2a: 0x02cf, 0xf2b: 0x02cf, 0xf2c: 0x02cf, 0xf2d: 0x02cf, 0xf2e: 0x02d2, 0xf2f: 0x02d2,
	0xf30: 0x02d2, 0xf31: 0x02d2, 0xf32: 0x02b1, 0xf33: 0x02b1, 0xf34: 0x02b1, 0xf35: 0x02b1,
	0xf36: 0x02ae, 0xf37: 0x02ae, 0xf38: 0x02ae, 0xf39: 0x02ae, 0xf3a: 0x02b4, 0xf3b: 0x02b4,
	0xf3c: 0x02b4, 0xf3d: 0x02b4, 0xf3e: 0x02b7, 0xf3f: 0x02b7,
	// Block 0x3d, offset 0xf40
	0xf40: 0x02b7, 0xf41: 0x02b7, 0xf42: 0x02c0, 0xf43: 0x02c0, 0xf44: 0x02bd, 0xf45: 0x02bd,
	0xf46: 0x02c3, 0xf47: 0x02c3, 0xf48: 0x02ba, 0xf49: 0x02ba, 0xf4a: 0x02c9, 0xf4b: 0x02c9,
	0xf4c: 0x02c6, 0xf4d: 0x02c6, 0xf4e: 0x02d5, 0xf4f: 0x02d5, 0xf50: 0x02d5, 0xf51: 0x02d5,
	0xf52: 0x02db, 0xf53: 0x02db, 0xf54: 0x02db, 0xf55: 0x02db, 0xf56: 0x02e1, 0xf57: 0x02e1,
	0xf58: 0x02e1, 0xf59: 0x02e1, 0xf5a: 0x02de, 0xf5b: 0x02de, 0xf5c: 0x02de, 0xf5d: 0x02de,
	0xf5e: 0x02e4, 0xf5f: 0x02e4, 0xf60: 0x02e7, 0xf61: 0x02e7, 0xf62: 0x02e7, 0xf63: 0x02e7,
	0xf64: 0x4471, 0xf65: 0x4471, 0xf66: 0x02ed, 0xf67: 0x02ed, 0xf68: 0x02ed, 0xf69: 0x02ed,
	0xf6a: 0x02ea, 0xf6b: 0x02ea, 0xf6c: 0x02ea, 0xf6d: 0x02ea, 0xf6e: 0x0308, 0xf6f: 0x0308,
	0xf70: 0x446b, 0xf71: 0x446b,
	// Block 0x3e, offset 0xf80
	0xf93: 0x02d8, 0xf94: 0x02d8, 0xf95: 0x02d8, 0xf96: 0x02d8, 0xf97: 0x02f6,
	0xf98: 0x02f6, 0xf99: 0x02f3, 0xf9a: 0x02f3, 0xf9b: 0x02f9, 0xf9c: 0x02f9, 0xf9d: 0x2052,
	0xf9e: 0x02ff, 0xf9f: 0x02ff, 0xfa0: 0x02f0, 0xfa1: 0x02f0, 0xfa2: 0x02fc, 0xfa3: 0x02fc,
	0xfa4: 0x0305, 0xfa5: 0x0305, 0xfa6: 0x0305, 0xfa7: 0x0305, 0xfa8: 0x028d, 0xfa9: 0x028d,
	0xfaa: 0x25ad, 0xfab: 0x25ad, 0xfac: 0x261d, 0xfad: 0x261d, 0xfae: 0x25ec, 0xfaf: 0x25ec,
	0xfb0: 0x2608, 0xfb1: 0x2608, 0xfb2: 0x2601, 0xfb3: 0x2601, 0xfb4: 0x260f, 0xfb5: 0x260f,
	0xfb6: 0x2616, 0xfb7: 0x2616, 0xfb8: 0x2616, 0xfb9: 0x25f3, 0xfba: 0x25f3, 0xfbb: 0x25f3,
	0xfbc: 0x0302, 0xfbd: 0x0302, 0xfbe: 0x0302, 0xfbf: 0x0302,
	// Block 0x3f, offset 0xfc0
	0xfc0: 0x25b4, 0xfc1: 0x25bb, 0xfc2: 0x25d7, 0xfc3: 0x25f3, 0xfc4: 0x25fa, 0xfc5: 0x1d8c,
	0xfc6: 0x1d91, 0xfc7: 0x1d96, 0xfc8: 0x1da5, 0xfc9: 0x1db4, 0xfca: 0x1db9, 0xfcb: 0x1dbe,
	0xfcc: 0x1dc3, 0xfcd: 0x1dc8, 0xfce: 0x1dd7, 0xfcf: 0x1de6, 0xfd0: 0x1deb, 0xfd1: 0x1df0,
	0xfd2: 0x1dff, 0xfd3: 0x1e0e, 0xfd4: 0x1e13, 0xfd5: 0x1e18, 0xfd6: 0x1e1d, 0xfd7: 0x1e2c,
	0xfd8: 0x1e31, 0xfd9: 0x1e40, 0xfda: 0x1e45, 0xfdb: 0x1e4a, 0xfdc: 0x1e59, 0xfdd: 0x1e5e,
	0xfde: 0x1e63, 0xfdf: 0x1e6d, 0xfe0: 0x1ea9, 0xfe1: 0x1eb8, 0xfe2: 0x1ec7, 0xfe3: 0x1ecc,
	0xfe4: 0x1ed1, 0xfe5: 0x1edb, 0xfe6: 0x1eea, 0xfe7: 0x1eef, 0xfe8: 0x1efe, 0xfe9: 0x1f03,
	0xfea: 0x1f08, 0xfeb: 0x1f17, 0xfec: 0x1f1c, 0xfed: 0x1f2b, 0xfee: 0x1f30, 0xfef: 0x1f35,
	0xff0: 0x1f3a, 0xff1: 0x1f3f, 0xff2: 0x1f44, 0xff3: 0x1f49, 0xff4: 0x1f4e, 0xff5: 0x1f53,
	0xff6: 0x1f58, 0xff7: 0x1f5d, 0xff8: 0x1f62, 0xff9: 0x1f67, 0xffa: 0x1f6c, 0xffb: 0x1f71,
	0xffc: 0x1f76, 0xffd: 0x1f7b, 0xffe: 0x1f80, 0xfff: 0x1f8a,
	// Block 0x40, offset 0x1000
	0x1000: 0x1f8f, 0x1001: 0x1f94, 0x1002: 0x1f99, 0x1003: 0x1fa3, 0x1004: 0x1fa8, 0x1005: 0x1fb2,
	0x1006: 0x1fb7, 0x1007: 0x1fbc, 0x1008: 0x1fc1, 0x1009: 0x1fc6, 0x100a: 0x1fcb, 0x100b: 0x1fd0,
	0x100c: 0x1fd5, 0x100d: 0x1fda, 0x100e: 0x1fe9, 0x100f: 0x1ff8, 0x1010: 0x1ffd, 0x1011: 0x2002,
	0x1012: 0x2007, 0x1013: 0x200c, 0x1014: 0x2011, 0x1015: 0x201b, 0x1016: 0x2020, 0x1017: 0x2025,
	0x1018: 0x2034, 0x1019: 0x2043, 0x101a: 0x2048, 0x101b: 0x4423, 0x101c: 0x4429, 0x101d: 0x445f,
	0x101e: 0x44b6, 0x101f: 0x44bd, 0x1020: 0x44c4, 0x1021: 0x44cb, 0x1022: 0x44d2, 0x1023: 0x44d9,
	0x1024: 0x25c9, 0x1025: 0x25d0, 0x1026: 0x25d7, 0x1027: 0x25de, 0x1028: 0x25f3, 0x1029: 0x25fa,
	0x102a: 0x1d9b, 0x102b: 0x1da0, 0x102c: 0x1da5, 0x102d: 0x1daa, 0x102e: 0x1db4, 0x102f: 0x1db9,
	0x1030: 0x1dcd, 0x1031: 0x1dd2, 0x1032: 0x1dd7, 0x1033: 0x1ddc, 0x1034: 0x1de6, 0x1035: 0x1deb,
	0x1036: 0x1df5, 0x1037: 0x1dfa, 0x1038: 0x1dff, 0x1039: 0x1e04, 0x103a: 0x1e0e, 0x103b: 0x1e13,
	0x103c: 0x1f3f, 0x103d: 0x1f44, 0x103e: 0x1f53, 0x103f: 0x1f58,
	// Block 0x41, offset 0x1040
	0x1040: 0x1f5d, 0x1041: 0x1f71, 0x1042: 0x1f76, 0x1043: 0x1f7b, 0x1044: 0x1f80, 0x1045: 0x1f99,
	0x1046: 0x1fa3, 0x1047: 0x1fa8, 0x1048: 0x1fad, 0x1049: 0x1fc1, 0x104a: 0x1fdf, 0x104b: 0x1fe4,
	0x104c: 0x1fe9, 0x104d: 0x1fee, 0x104e: 0x1ff8, 0x104f: 0x1ffd, 0x1050: 0x445f, 0x1051: 0x202a,
	0x1052: 0x202f, 0x1053: 0x2034, 0x1054: 0x2039, 0x1055: 0x2043, 0x1056: 0x2048, 0x1057: 0x25b4,
	0x1058: 0x25bb, 0x1059: 0x25c2, 0x105a: 0x25d7, 0x105b: 0x25e5, 0x105c: 0x1d8c, 0x105d: 0x1d91,
	0x105e: 0x1d96, 0x105f: 0x1da5, 0x1060: 0x1daf, 0x1061: 0x1dbe, 0x1062: 0x1dc3, 0x1063: 0x1dc8,
	0x1064: 0x1dd7, 0x1065: 0x1de1, 0x1066: 0x1dff, 0x1067: 0x1e18, 0x1068: 0x1e1d, 0x1069: 0x1e2c,
	0x106a: 0x1e31, 0x106b: 0x1e40, 0x106c: 0x1e4a, 0x106d: 0x1e59, 0x106e: 0x1e5e, 0x106f: 0x1e63,
	0x1070: 0x1e6d, 0x1071: 0x1ea9, 0x1072: 0x1eae, 0x1073: 0x1eb8, 0x1074: 0x1ec7, 0x1075: 0x1ecc,
	0x1076: 0x1ed1, 0x1077: 0x1edb, 0x1078: 0x1eea, 0x1079: 0x1efe, 0x107a: 0x1f03, 0x107b: 0x1f08,
	0x107c: 0x1f17, 0x107d: 0x1f1c, 0x107e: 0x1f2b, 0x107f: 0x1f30,
	// Block 0x42, offset 0x1080
	0x1080: 0x1f35, 0x1081: 0x1f3a, 0x1082: 0x1f49, 0x1083: 0x1f4e, 0x1084: 0x1f62, 0x1085: 0x1f67,
	0x1086: 0x1f6c, 0x1087: 0x1f71, 0x1088: 0x1f76, 0x1089: 0x1f8a, 0x108a: 0x1f8f, 0x108b: 0x1f94,
	0x108c: 0x1f99, 0x108d: 0x1f9e, 0x108e: 0x1fb2, 0x108f: 0x1fb7, 0x1090: 0x1fbc, 0x1091: 0x1fc1,
	0x1092: 0x1fd0, 0x1093: 0x1fd5, 0x1094: 0x1fda, 0x1095: 0x1fe9, 0x1096: 0x1ff3, 0x1097: 0x2002,
	0x1098: 0x2007, 0x1099: 0x4453, 0x109a: 0x201b, 0x109b: 0x2020, 0x109c: 0x2025, 0x109d: 0x2034,
	0x109e: 0x203e, 0x109f: 0x25d7, 0x10a0: 0x25e5, 0x10a1: 0x1da5, 0x10a2: 0x1daf, 0x10a3: 0x1dd7,
	0x10a4: 0x1de1, 0x10a5: 0x1dff, 0x10a6: 0x1e09, 0x10a7: 0x1e6d, 0x10a8: 0x1e72, 0x10a9: 0x1e95,
	0x10aa: 0x1e9a, 0x10ab: 0x1f71, 0x10ac: 0x1f76, 0x10ad: 0x1f99, 0x10ae: 0x1fe9, 0x10af: 0x1ff3,
	0x10b0: 0x2034, 0x10b1: 0x203e, 0x10b2: 0x4507, 0x10b3: 0x450f, 0x10b4: 0x4517, 0x10b5: 0x1ef4,
	0x10b6: 0x1ef9, 0x10b7: 0x1f0d, 0x10b8: 0x1f12, 0x10b9: 0x1f21, 0x10ba: 0x1f26, 0x10bb: 0x1e77,
	0x10bc: 0x1e7c, 0x10bd: 0x1e9f, 0x10be: 0x1ea4, 0x10bf: 0x1e36,
	// Block 0x43, offset 0x10c0
	0x10c0: 0x1e3b, 0x10c1: 0x1e22, 0x10c2: 0x1e27, 0x10c3: 0x1e4f, 0x10c4: 0x1e54, 0x10c5: 0x1ebd,
	0x10c6: 0x1ec2, 0x10c7: 0x1ee0, 0x10c8: 0x1ee5, 0x10c9: 0x1e81, 0x10ca: 0x1e86, 0x10cb: 0x1e8b,
	0x10cc: 0x1e95, 0x10cd: 0x1e90, 0x10ce: 0x1e68, 0x10cf: 0x1eb3, 0x10d0: 0x1ed6, 0x10d1: 0x1ef4,
	0x10d2: 0x1ef9, 0x10d3: 0x1f0d, 0x10d4: 0x1f12, 0x10d5: 0x1f21, 0x10d6: 0x1f26, 0x10d7: 0x1e77,
	0x10d8: 0x1e7c, 0x10d9: 0x1e9f, 0x10da: 0x1ea4, 0x10db: 0x1e36, 0x10dc: 0x1e3b, 0x10dd: 0x1e22,
	0x10de: 0x1e27, 0x10df: 0x1e4f, 0x10e0: 0x1e54, 0x10e1: 0x1ebd, 0x10e2: 0x1ec2, 0x10e3: 0x1ee0,
	0x10e4: 0x1ee5, 0x10e5: 0x1e81, 0x10e6: 0x1e86, 0x10e7: 0x1e8b, 0x10e8: 0x1e95, 0x10e9: 0x1e90,
	0x10ea: 0x1e68, 0x10eb: 0x1eb3, 0x10ec: 0x1ed6, 0x10ed: 0x1e81, 0x10ee: 0x1e86, 0x10ef: 0x1e8b,
	0x10f0: 0x1e95, 0x10f1: 0x1e72, 0x10f2: 0x1e9a, 0x10f3: 0x1eef, 0x10f4: 0x1e59, 0x10f5: 0x1e5e,
	0x10f6: 0x1e63, 0x10f7: 0x1e81, 0x10f8: 0x1e86, 0x10f9: 0x1e8b, 0x10fa: 0x1eef, 0x10fb: 0x1efe,
	0x10fc: 0x440b, 0x10fd: 0x440b,
	// Block 0x44, offset 0x1100
	0x1110: 0x2314, 0x1111: 0x2329,
	0x1112: 0x2329, 0x1113: 0x2330, 0x1114: 0x2337, 0x1115: 0x234c, 0x1116: 0x2353, 0x1117: 0x235a,
	0x1118: 0x237d, 0x1119: 0x237d, 0x111a: 0x23a0, 0x111b: 0x2399, 0x111c: 0x23b5, 0x111d: 0x23a7,
	0x111e: 0x23ae, 0x111f: 0x23d1, 0x1120: 0x23d1, 0x1121: 0x23ca, 0x1122: 0x23d8, 0x1123: 0x23d8,
	0x1124: 0x2402, 0x1125: 0x2402, 0x1126: 0x241e, 0x1127: 0x23e6, 0x1128: 0x23e6, 0x1129: 0x23df,
	0x112a: 0x23f4, 0x112b: 0x23f4, 0x112c: 0x23fb, 0x112d: 0x23fb, 0x112e: 0x2425, 0x112f: 0x2433,
	0x1130: 0x2433, 0x1131: 0x243a, 0x1132: 0x243a, 0x1133: 0x2441, 0x1134: 0x2448, 0x1135: 0x244f,
	0x1136: 0x2456, 0x1137: 0x2456, 0x1138: 0x245d, 0x1139: 0x246b, 0x113a: 0x2479, 0x113b: 0x2472,
	0x113c: 0x2480, 0x113d: 0x2480, 0x113e: 0x2495, 0x113f: 0x249c,
	// Block 0x45, offset 0x1140
	0x1140: 0x24cd, 0x1141: 0x24db, 0x1142: 0x24d4, 0x1143: 0x24b8, 0x1144: 0x24b8, 0x1145: 0x24e2,
	0x1146: 0x24e2, 0x1147: 0x24e9, 0x1148: 0x24e9, 0x1149: 0x2513, 0x114a: 0x251a, 0x114b: 0x2521,
	0x114c: 0x24f7, 0x114d: 0x2505, 0x114e: 0x2528, 0x114f: 0x252f,
	0x1152: 0x24fe, 0x1153: 0x2583, 0x1154: 0x258a, 0x1155: 0x2560, 0x1156: 0x2567, 0x1157: 0x254b,
	0x1158: 0x254b, 0x1159: 0x2552, 0x115a: 0x257c, 0x115b: 0x2575, 0x115c: 0x259f, 0x115d: 0x259f,
	0x115e: 0x230d, 0x115f: 0x2322, 0x1160: 0x231b, 0x1161: 0x2345, 0x1162: 0x233e, 0x1163: 0x2368,
	0x1164: 0x2361, 0x1165: 0x238b, 0x1166: 0x236f, 0x1167: 0x2384, 0x1168: 0x23bc, 0x1169: 0x2409,
	0x116a: 0x23ed, 0x116b: 0x242c, 0x116c: 0x24c6, 0x116d: 0x24f0, 0x116e: 0x2598, 0x116f: 0x2591,
	0x1170: 0x25a6, 0x1171: 0x253d, 0x1172: 0x24a3, 0x1173: 0x256e, 0x1174: 0x2495, 0x1175: 0x24cd,
	0x1176: 0x2464, 0x1177: 0x24b1, 0x1178: 0x2544, 0x1179: 0x2536, 0x117a: 0x24bf, 0x117b: 0x24aa,
	0x117c: 0x24bf, 0x117d: 0x2544, 0x117e: 0x2376, 0x117f: 0x2392,
	// Block 0x46, offset 0x1180
	0x1180: 0x250c, 0x1181: 0x2487, 0x1182: 0x2306, 0x1183: 0x24aa, 0x1184: 0x244f, 0x1185: 0x241e,
	0x1186: 0x23c3, 0x1187: 0x2559,
	0x11b0: 0x2417, 0x11b1: 0x248e, 0x11b2: 0x27c2, 0x11b3: 0x27b9, 0x11b4: 0x27ef, 0x11b5: 0x27dd,
	0x11b6: 0x27cb, 0x11b7: 0x27e6, 0x11b8: 0x27f8, 0x11b9: 0x2410, 0x11ba: 0x2c7f, 0x11bb: 0x2aff,
	0x11bc: 0x27d4,
	// Block 0x47, offset 0x11c0
	0x11d0: 0x0019, 0x11d1: 0x0483,
	0x11d2: 0x0487, 0x11d3: 0x0035, 0x11d4: 0x0037, 0x11d5: 0x0003, 0x11d6: 0x003f, 0x11d7: 0x04bf,
	0x11d8: 0x04c3, 0x11d9: 0x1b5f,
	0x11e0: 0x8132, 0x11e1: 0x8132, 0x11e2: 0x8132, 0x11e3: 0x8132,
	0x11e4: 0x8132, 0x11e5: 0x8132, 0x11e6: 0x8132, 0x11e7: 0x812d, 0x11e8: 0x812d, 0x11e9: 0x812d,
	0x11ea: 0x812d, 0x11eb: 0x812d, 0x11ec: 0x812d, 0x11ed: 0x812d, 0x11ee: 0x8132, 0x11ef: 0x8132,
	0x11f0: 0x1873, 0x11f1: 0x0443, 0x11f2: 0x043f, 0x11f3: 0x007f, 0x11f4: 0x007f, 0x11f5: 0x0011,
	0x11f6: 0x0013, 0x11f7: 0x00b7, 0x11f8: 0x00bb, 0x11f9: 0x04b7, 0x11fa: 0x04bb, 0x11fb: 0x04ab,
	0x11fc: 0x04af, 0x11fd: 0x0493, 0x11fe: 0x0497, 0x11ff: 0x048b,
	// Block 0x48, offset 0x1200
	0x1200: 0x048f, 0x1201: 0x049b, 0x1202: 0x049f, 0x1203: 0x04a3, 0x1204: 0x04a7,
	0x1207: 0x0077, 0x1208: 0x007b, 0x1209: 0x426c, 0x120a: 0x426c, 0x120b: 0x426c,
	0x120c: 0x426c, 0x120d: 0x007f, 0x120e: 0x007f, 0x120f: 0x007f, 0x1210: 0x0019, 0x1211: 0x0483,
	0x1212: 0x001d, 0x1214: 0x0037, 0x1215: 0x0035, 0x1216: 0x003f, 0x1217: 0x0003,
	0x1218: 0x0443, 0x1219: 0x0011, 0x121a: 0x0013, 0x121b: 0x00b7, 0x121c: 0x00bb, 0x121d: 0x04b7,
	0x121e: 0x04bb, 0x121f: 0x0007, 0x1220: 0x000d, 0x1221: 0x0015, 0x1222: 0x0017, 0x1223: 0x001b,
	0x1224: 0x0039, 0x1225: 0x003d, 0x1226: 0x003b, 0x1228: 0x0079, 0x1229: 0x0009,
	0x122a: 0x000b, 0x122b: 0x0041,
	0x1230: 0x42ad, 0x1231: 0x442f, 0x1232: 0x42b2, 0x1234: 0x42b7,
	0x1236: 0x42bc, 0x1237: 0x4435, 0x1238: 0x42c1, 0x1239: 0x443b, 0x123a: 0x42c6, 0x123b: 0x4441,
	0x123c: 0x42cb, 0x123d: 0x4447, 0x123e: 0x42d0, 0x123f: 0x444d,
	// Block 0x49, offset 0x1240
	0x1240: 0x0236, 0x1241: 0x4411, 0x1242: 0x4411, 0x1243: 0x4417, 0x1244: 0x4417, 0x1245: 0x4459,
	0x1246: 0x4459, 0x1247: 0x441d, 0x1248: 0x441d, 0x1249: 0x4465, 0x124a: 0x4465, 0x124b: 0x4465,
	0x124c: 0x4465, 0x124d: 0x0239, 0x124e: 0x0239, 0x124f: 0x023c, 0x1250: 0x023c, 0x1251: 0x023c,
	0x1252: 0x023c, 0x1253: 0x023f, 0x1254: 0x023f, 0x1255: 0x0242, 0x1256: 0x0242, 0x1257: 0x0242,
	0x1258: 0x0242, 0x1259: 0x0245, 0x125a: 0x0245, 0x125b: 0x0245, 0x125c: 0x0245, 0x125d: 0x0248,
	0x125e: 0x0248, 0x125f: 0x0248, 0x1260: 0x0248, 0x1261: 0x024b, 0x1262: 0x024b, 0x1263: 0x024b,
	0x1264: 0x024b, 0x1265: 0x024e, 0x1266: 0x024e, 0x1267: 0x024e, 0x1268: 0x024e, 0x1269: 0x0251,
	0x126a: 0x0251, 0x126b: 0x0254, 0x126c: 0x0254, 0x126d: 0x0257, 0x126e: 0x0257, 0x126f: 0x025a,
	0x1270: 0x025a, 0x1271: 0x025d, 0x1272: 0x025d, 0x1273: 0x025d, 0x1274: 0x025d, 0x1275: 0x0260,
	0x1276: 0x0260, 0x1277: 0x0260, 0x1278: 0x0260, 0x1279: 0x0263, 0x127a: 0x0263, 0x127b: 0x0263,
	0x127c: 0x0263, 0x127d: 0x0266, 0x127e: 0x0266, 0x127f: 0x0266,
	// Block 0x4a, offset 0x1280
	0x1280: 0x0266, 0x1281: 0x0269, 0x1282: 0x0269, 0x1283: 0x0269, 0x1284: 0x0269, 0x1285: 0x026c,
	0x1286: 0x026c, 0x1287: 0x026c, 0x1288: 0x026c, 0x1289: 0x026f, 0x128a: 0x026f, 0x128b: 0x026f,
	0x128c: 0x026f, 0x128d: 0x0272, 0x128e: 0x0272, 0x128f: 0x0272, 0x1290: 0x0272, 0x1291: 0x0275,
	0x1292: 0x0275, 0x1293: 0x0275, 0x1294: 0x0275, 0x1295: 0x0278, 0x1296: 0x0278, 0x1297: 0x0278,
	0x1298: 0x0278, 0x1299: 0x027b, 0x129a: 0x027b, 0x129b: 0x027b, 0x129c: 0x027b, 0x129d: 0x027e,
	0x129e: 0x027e, 0x129f: 0x027e, 0x12a0: 0x027e, 0x12a1: 0x0281, 0x12a2: 0x0281, 0x12a3: 0x0281,
	0x12a4: 0x0281, 0x12a5: 0x0284, 0x12a6: 0x0284, 0x12a7: 0x0284, 0x12a8: 0x0284, 0x12a9: 0x0287,
	0x12aa: 0x0287, 0x12ab: 0x0287, 0x12ac: 0x0287, 0x12ad: 0x028a, 0x12ae: 0x028a, 0x12af: 0x028d,
	0x12b0: 0x028d, 0x12b1: 0x0290, 0x12b2: 0x0290, 0x12b3: 0x0290, 0x12b4: 0x0290, 0x12b5: 0x2e03,
	0x12b6: 0x2e03, 0x12b7: 0x2e0b, 0x12b8: 0x2e0b, 0x12b9: 0x2e13, 0x12ba: 0x2e13, 0x12bb: 0x1f85,
	0x12bc: 0x1f85,
	// Block 0x4b, offset 0x12c0
	0x12c0: 0x0081, 0x12c1: 0x0083, 0x12c2: 0x0085, 0x12c3: 0x0087, 0x12c4: 0x0089, 0x12c5: 0x008b,
	0x12c6: 0x008d, 0x12c7: 0x008f, 0x12c8: 0x0091, 0x12c9: 0x0093, 0x12ca: 0x0095, 0x12cb: 0x0097,
	0x12cc: 0x0099, 0x12cd: 0x009b, 0x12ce: 0x009d, 0x12cf: 0x009f, 0x12d0: 0x00a1, 0x12d1: 0x00a3,
	0x12d2: 0x00a5, 0x12d3: 0x00a7, 0x12d4: 0x00a9, 0x12d5: 0x00ab, 0x12d6: 0x00ad, 0x12d7: 0x00af,
	0x12d8: 0x00b1, 0x12d9: 0x00b3, 0x12da: 0x00b5, 0x12db: 0x00b7, 0x12dc: 0x00b9, 0x12dd: 0x00bb,
	0x12de: 0x00bd, 0x12df: 0x0477, 0x12e0: 0x047b, 0x12e1: 0x0487, 0x12e2: 0x049b, 0x12e3: 0x049f,
	0x12e4: 0x0483, 0x12e5: 0x05ab, 0x12e6: 0x05a3, 0x12e7: 0x04c7, 0x12e8: 0x04cf, 0x12e9: 0x04d7,
	0x12ea: 0x04df, 0x12eb: 0x04e7, 0x12ec: 0x056b, 0x12ed: 0x0573, 0x12ee: 0x057b, 0x12ef: 0x051f,
	0x12f0: 0x05af, 0x12f1: 0x04cb, 0x12f2: 0x04d3, 0x12f3: 0x04db, 0x12f4: 0x04e3, 0x12f5: 0x04eb,
	0x12f6: 0x04ef, 0x12f7: 0x04f3, 0x12f8: 0x04f7, 0x12f9: 0x04fb, 0x12fa: 0x04ff, 0x12fb: 0x0503,
	0x12fc: 0x0507, 0x12fd: 0x050b, 0x12fe: 0x050f, 0x12ff: 0x0513,
	// Block 0x4c, offset 0x1300
	0x1300: 0x0517, 0x1301: 0x051b, 0x1302: 0x0523, 0x1303: 0x0527, 0x1304: 0x052b, 0x1305: 0x052f,
	0x1306: 0x0533, 0x1307: 0x0537, 0x1308: 0x053b, 0x1309: 0x053f, 0x130a: 0x0543, 0x130b: 0x0547,
	0x130c: 0x054b, 0x130d: 0x054f, 0x130e: 0x0553, 0x130f: 0x0557, 0x1310: 0x055b, 0x1311: 0x055f,
	0x1312: 0x0563, 0x1313: 0x0567, 0x1314: 0x056f, 0x1315: 0x0577, 0x1316: 0x057f, 0x1317: 0x0583,
	0x1318: 0x0587, 0x1319: 0x058b, 0x131a: 0x058f, 0x131b: 0x0593, 0x131c: 0x0597, 0x131d: 0x05a7,
	0x131e: 0x4a7b, 0x131f: 0x4a81, 0x1320: 0x03c3, 0x1321: 0x0313, 0x1322: 0x0317, 0x1323: 0x4a3e,
	0x1324: 0x031b, 0x1325: 0x4a44, 0x1326: 0x4a4a, 0x1327: 0x031f, 0x1328: 0x0323, 0x1329: 0x0327,
	0x132a: 0x4a50, 0x132b: 0x4a56, 0x132c: 0x4a5c, 0x132d: 0x4a62, 0x132e: 0x4a68, 0x132f: 0x4a6e,
	0x1330: 0x0367, 0x1331: 0x032b, 0x1332: 0x032f, 0x1333: 0x0333, 0x1334: 0x037b, 0x1335: 0x0337,
	0x1336: 0x033b, 0x1337: 0x033f, 0x1338: 0x0343, 0x1339: 0x0347, 0x133a: 0x034b, 0x133b: 0x034f,
	0x133c: 0x0353, 0x133d: 0x0357, 0x133e: 0x035b,
	// Block 0x4d, offset 0x1340
	0x1342: 0x49c0, 0x1343: 0x49c6, 0x1344: 0x49cc, 0x1345: 0x49d2,
	0x1346: 0x49d8, 0x1347: 0x49de, 0x134a: 0x49e4, 0x134b: 0x49ea,
	0x134c: 0x49f0, 0x134d: 0x49f6, 0x134e: 0x49fc, 0x134f: 0x4a02,
	0x1352: 0x4a08, 0x1353: 0x4a0e, 0x1354: 0x4a14, 0x1355: 0x4a1a, 0x1356: 0x4a20, 0x1357: 0x4a26,
	0x135a: 0x4a2c, 0x135b: 0x4a32, 0x135c: 0x4a38,
	0x1360: 0x00bf, 0x1361: 0x00c2, 0x1362: 0x00cb, 0x1363: 0x4267,
	0x1364: 0x00c8, 0x1365: 0x00c5, 0x1366: 0x0447, 0x1368: 0x046b, 0x1369: 0x044b,
	0x136a: 0x044f, 0x136b: 0x0453, 0x136c: 0x0457, 0x136d: 0x046f, 0x136e: 0x0473,
	// Block 0x4e, offset 0x1380
	0x1380: 0x0063, 0x1381: 0x0065, 0x1382: 0x0067, 0x1383: 0x0069, 0x1384: 0x006b, 0x1385: 0x006d,
	0x1386: 0x006f, 0x1387: 0x0071, 0x1388: 0x0073, 0x1389: 0x0075, 0x138a: 0x0083, 0x138b: 0x0085,
	0x138c: 0x0087, 0x138d: 0x0089, 0x138e: 0x008b, 0x138f: 0x008d, 0x1390: 0x008f, 0x1391: 0x0091,
	0x1392: 0x0093, 0x1393: 0x0095, 0x1394: 0x0097, 0x1395: 0x0099, 0x1396: 0x009b, 0x1397: 0x009d,
	0x1398: 0x009f, 0x1399: 0x00a1, 0x139a: 0x00a3, 0x139b: 0x00a5, 0x139c: 0x00a7, 0x139d: 0x00a9,
	0x139e: 0x00ab, 0x139f: 0x00ad, 0x13a0: 0x00af, 0x13a1: 0x00b1, 0x13a2: 0x00b3, 0x13a3: 0x00b5,
	0x13a4: 0x00dd, 0x13a5: 0x00f2, 0x13a8: 0x0173, 0x13a9: 0x0176,
	0x13aa: 0x0179, 0x13ab: 0x017c, 0x13ac: 0x017f, 0x13ad: 0x0182, 0x13ae: 0x0185, 0x13af: 0x0188,
	0x13b0: 0x018b, 0x13b1: 0x018e, 0x13b2: 0x0191, 0x13b3: 0x0194, 0x13b4: 0x0197, 0x13b5: 0x019a,
	0x13b6: 0x019d, 0x13b7: 0x01a0, 0x13b8: 0x01a3, 0x13b9: 0x0188, 0x13ba: 0x01a6, 0x13bb: 0x01a9,
	0x13bc: 0x01ac, 0x13bd: 0x01af, 0x13be: 0x01b2, 0x13bf: 0x01b5,
	// Block 0x4f, offset 0x13c0
	0x13c0: 0x01fd, 0x13c1: 0x0200, 0x13c2: 0x0203, 0x13c3: 0x045b, 0x13c4: 0x01c7, 0x13c5: 0x01d0,
	0x13c6: 0x01d6, 0x13c7: 0x01fa, 0x13c8: 0x01eb, 0x13c9: 0x01e8, 0x13ca: 0x0206, 0x13cb: 0x0209,
	0x13ce: 0x0021, 0x13cf: 0x0023, 0x13d0: 0x0025, 0x13d1: 0x0027,
	0x13d2: 0x0029, 0x13d3: 0x002b, 0x13d4: 0x002d, 0x13d5: 0x002f, 0x13d6: 0x0031, 0x13d7: 0x0033,
	0x13d8: 0x0021, 0x13d9: 0x0023, 0x13da: 0x0025, 0x13db: 0x0027, 0x13dc: 0x0029, 0x13dd: 0x002b,
	0x13de: 0x002d, 0x13df: 0x002f, 0x13e0: 0x0031, 0x13e1: 0x0033, 0x13e2: 0x0021, 0x13e3: 0x0023,
	0x13e4: 0x0025, 0x13e5: 0x0027, 0x13e6: 0x0029, 0x13e7: 0x002b, 0x13e8: 0x002d, 0x13e9: 0x002f,
	0x13ea: 0x0031, 0x13eb: 0x0033, 0x13ec: 0x0021, 0x13ed: 0x0023, 0x13ee: 0x0025, 0x13ef: 0x0027,
	0x13f0: 0x0029, 0x13f1: 0x002b, 0x13f2: 0x002d, 0x13f3: 0x002f, 0x13f4: 0x0031, 0x13f5: 0x0033,
	0x13f6: 0x0021, 0x13f7: 0x0023, 0x13f8: 0x0025, 0x13f9: 0x0027, 0x13fa: 0x0029, 0x13fb: 0x002b,
	0x13fc: 0x002d, 0x13fd: 0x002f, 0x13fe: 0x0031, 0x13ff: 0x0033,
	// Block 0x50, offset 0x1400
	0x1400: 0x0239, 0x1401: 0x023c, 0x1402: 0x0248, 0x1403: 0x0251, 0x1405: 0x028a,
	0x1406: 0x025a, 0x1407: 0x024b, 0x1408: 0x0269, 0x1409: 0x0290, 0x140a: 0x027b, 0x140b: 0x027e,
	0x140c: 0x0281, 0x140d: 0x0284, 0x140e: 0x025d, 0x140f: 0x026f, 0x1410: 0x0275, 0x1411: 0x0263,
	0x1412: 0x0278, 0x1413: 0x0257, 0x1414: 0x0260, 0x1415: 0x0242, 0x1416: 0x0245, 0x1417: 0x024e,
	0x1418: 0x0254, 0x1419: 0x0266, 0x141a: 0x026c, 0x141b: 0x0272, 0x141c: 0x0293, 0x141d: 0x02e4,
	0x141e: 0x02cc, 0x141f: 0x0296, 0x1421: 0x023c, 0x1422: 0x0248,
	0x1424: 0x0287, 0x1427: 0x024b, 0x1429: 0x0290,
	0x142a: 0x027b, 0x142b: 0x027e, 0x142c: 0x0281, 0x142d: 0x0284, 0x142e: 0x025d, 0x142f: 0x026f,
	0x1430: 0x0275, 0x1431: 0x0263, 0x1432: 0x0278, 0x1434: 0x0260, 0x1435: 0x0242,
	0x1436: 0x0245, 0x1437: 0x024e, 0x1439: 0x0266, 0x143b: 0x0272,
	// Block 0x51, offset 0x1440
	0x1442: 0x0248,
	0x1447: 0x024b, 0x1449: 0x0290, 0x144b: 0x027e,
	0x144d: 0x0284, 0x144e: 0x025d, 0x144f: 0x026f, 0x1451: 0x0263,
	0x1452: 0x0278, 0x1454: 0x0260, 0x1457: 0x024e,
	0x1459: 0x0266, 0x145b: 0x0272, 0x145d: 0x02e4,
	0x145f: 0x0296, 0x1461: 0x023c, 0x1462: 0x0248,
	0x1464: 0x0287, 0x1467: 0x024b, 0x1468: 0x0269, 0x1469: 0x0290,
	0x146a: 0x027b, 0x146c: 0x0281, 0x146d: 0x0284, 0x146e: 0x025d, 0x146f: 0x026f,
	0x1470: 0x0275, 0x1471: 0x0263, 0x1472: 0x0278, 0x1474: 0x0260, 0x1475: 0x0242,
	0x1476: 0x0245, 0x1477: 0x024e, 0x1479: 0x0266, 0x147a: 0x026c, 0x147b: 0x0272,
	0x147c: 0x0293, 0x147e: 0x02cc,
	// Block 0x52, offset 0x1480
	0x1480: 0x0239, 0x1481: 0x023c, 0x1482: 0x0248, 0x1483: 0x0251, 0x1484: 0x0287, 0x1485: 0x028a,
	0x1486: 0x025a, 0x1487: 0x024b, 0x1488: 0x0269, 0x1489: 0x0290, 0x148b: 0x027e,
	0x148c: 0x0281, 0x148d: 0x0284, 0x148e: 0x025d, 0x148f: 0x026f, 0x1490: 0x0275, 0x1491: 0x0263,
	0x1492: 0x0278, 0x1493: 0x0257, 0x1494: 0x0260, 0x1495: 0x0242, 0x1496: 0x0245, 0x1497: 0x024e,
	0x1498: 0x0254, 0x1499: 0x0266, 0x149a: 0x026c, 0x149b: 0x0272,
	0x14a1: 0x023c, 0x14a2: 0x0248, 0x14a3: 0x0251,
	0x14a5: 0x028a, 0x14a6: 0x025a, 0x14a7: 0x024b, 0x14a8: 0x0269, 0x14a9: 0x0290,
	0x14ab: 0x027e, 0x14ac: 0x0281, 0x14ad: 0x0284, 0x14ae: 0x025d, 0x14af: 0x026f,
	0x14b0: 0x0275, 0x14b1: 0x0263, 0x14b2: 0x0278, 0x14b3: 0x0257, 0x14b4: 0x0260, 0x14b5: 0x0242,
	0x14b6: 0x0245, 0x14b7: 0x024e, 0x14b8: 0x0254, 0x14b9: 0x0266, 0x14ba: 0x026c, 0x14bb: 0x0272,
	// Block 0x53, offset 0x14c0
	0x14c0: 0x1879, 0x14c1: 0x1876, 0x14c2: 0x187c, 0x14c3: 0x18a0, 0x14c4: 0x18c4, 0x14c5: 0x18e8,
	0x14c6: 0x190c, 0x14c7: 0x1915, 0x14c8: 0x191b, 0x14c9: 0x1921, 0x14ca: 0x1927,
	0x14d0: 0x1a8f, 0x14d1: 0x1a93,
	0x14d2: 0x1a97, 0x14d3: 0x1a9b, 0x14d4: 0x1a9f, 0x14d5: 0x1aa3, 0x14d6: 0x1aa7, 0x14d7: 0x1aab,
	0x14d8: 0x1aaf, 0x14d9: 0x1ab3, 0x14da: 0x1ab7, 0x14db: 0x1abb, 0x14dc: 0x1abf, 0x14dd: 0x1ac3,
	0x14de: 0x1ac7, 0x14df: 0x1acb, 0x14e0: 0x1acf, 0x14e1: 0x1ad3, 0x14e2: 0x1ad7, 0x14e3: 0x1adb,
	0x14e4: 0x1adf, 0x14e5: 0x1ae3, 0x14e6: 0x1ae7, 0x14e7: 0x1aeb, 0x14e8: 0x1aef, 0x14e9: 0x1af3,
	0x14ea: 0x2721, 0x14eb: 0x0047, 0x14ec: 0x0065, 0x14ed: 0x193c, 0x14ee: 0x19b4,
	0x14f0: 0x0043, 0x14f1: 0x0045, 0x14f2: 0x0047, 0x14f3: 0x0049, 0x14f4: 0x004b, 0x14f5: 0x004d,
	0x14f6: 0x004f, 0x14f7: 0x0051, 0x14f8: 0x0053, 0x14f9: 0x0055, 0x14fa: 0x0057, 0x14fb: 0x0059,
	0x14fc: 0x005b, 0x14fd: 0x005d, 0x14fe: 0x005f, 0x14ff: 0x0061,
	// Block 0x54, offset 0x1500
	0x1500: 0x26b0, 0x1501: 0x26c5, 0x1502: 0x0503,
	0x1510: 0x0c0f, 0x1511: 0x0a47,
	0x1512: 0x08d3, 0x1513: 0x45c7, 0x1514: 0x071b, 0x1515: 0x09ef, 0x1516: 0x132f, 0x1517: 0x09ff,
	0x1518: 0x0727, 0x1519: 0x0cd7, 0x151a: 0x0eaf, 0x151b: 0x0caf, 0x151c: 0x0827, 0x151d: 0x0b6b,
	0x151e: 0x07bf, 0x151f: 0x0cb7, 0x1520: 0x0813, 0x1521: 0x1117, 0x1522: 0x0f83, 0x1523: 0x138b,
	0x1524: 0x09d3, 0x1525: 0x090b, 0x1526: 0x0e63, 0x1527: 0x0c1b, 0x1528: 0x0c47, 0x1529: 0x06bf,
	0x152a: 0x06cb, 0x152b: 0x140b, 0x152c: 0x0adb, 0x152d: 0x06e7, 0x152e: 0x08ef, 0x152f: 0x0c3b,
	0x1530: 0x13b3, 0x1531: 0x0c13, 0x1532: 0x106f, 0x1533: 0x10ab, 0x1534: 0x08f7, 0x1535: 0x0e43,
	0x1536: 0x0d0b, 0x1537: 0x0d07, 0x1538: 0x0f97, 0x1539: 0x082b, 0x153a: 0x0957, 0x153b: 0x1443,
	// Block 0x55, offset 0x1540
	0x1540: 0x06fb, 0x1541: 0x06f3, 0x1542: 0x0703, 0x1543: 0x1647, 0x1544: 0x0747, 0x1545: 0x0757,
	0x1546: 0x075b, 0x1547: 0x0763, 0x1548: 0x076b, 0x1549: 0x076f, 0x154a: 0x077b, 0x154b: 0x0773,
	0x154c: 0x05b3, 0x154d: 0x165b, 0x154e: 0x078f, 0x154f: 0x0793, 0x1550: 0x0797, 0x1551: 0x07b3,
	0x1552: 0x164c, 0x1553: 0x05b7, 0x1554: 0x079f, 0x1555: 0x07bf, 0x1556: 0x1656, 0x1557: 0x07cf,
	0x1558: 0x07d7, 0x1559: 0x0737, 0x155a: 0x07df, 0x155b: 0x07e3, 0x155c: 0x1831, 0x155d: 0x07ff,
	0x155e: 0x0807, 0x155f: 0x05bf, 0x1560: 0x081f, 0x1561: 0x0823, 0x1562: 0x082b, 0x1563: 0x082f,
	0x1564: 0x05c3, 0x1565: 0x0847, 0x1566: 0x084b, 0x1567: 0x0857, 0x1568: 0x0863, 0x1569: 0x0867,
	0x156a: 0x086b, 0x156b: 0x0873, 0x156c: 0x0893, 0x156d: 0x0897, 0x156e: 0x089f, 0x156f: 0x08af,
	0x1570: 0x08b7, 0x1571: 0x08bb, 0x1572: 0x08bb, 0x1573: 0x08bb, 0x1574: 0x166a, 0x1575: 0x0e93,
	0x1576: 0x08cf, 0x1577: 0x08d7, 0x1578: 0x166f, 0x1579: 0x08e3, 0x157a: 0x08eb, 0x157b: 0x08f3,
	0x157c: 0x091b, 0x157d: 0x0907, 0x157e: 0x0913, 0x157f: 0x0917,
	// Block 0x56, offset 0x1580
	0x1580: 0x091f, 0x1581: 0x0927, 0x1582: 0x092b, 0x1583: 0x0933, 0x1584: 0x093b, 0x1585: 0x093f,
	0x1586: 0x093f, 0x1587: 0x0947, 0x1588: 0x094f, 0x1589: 0x0953, 0x158a: 0x095f, 0x158b: 0x0983,
	0x158c: 0x0967, 0x158d: 0x0987, 0x158e: 0x096b, 0x158f: 0x0973, 0x1590: 0x080b, 0x1591: 0x09cf,
	0x1592: 0x0997, 0x1593: 0x099b, 0x1594: 0x099f, 0x1595: 0x0993, 0x1596: 0x09a7, 0x1597: 0x09a3,
	0x1598: 0x09bb, 0x1599: 0x1674, 0x159a: 0x09d7, 0x159b: 0x09db, 0x159c: 0x09e3, 0x159d: 0x09ef,
	0x159e: 0x09f7, 0x159f: 0x0a13, 0x15a0: 0x1679, 0x15a1: 0x167e, 0x15a2: 0x0a1f, 0x15a3: 0x0a23,
	0x15a4: 0x0a27, 0x15a5: 0x0a1b, 0x15a6: 0x0a2f, 0x15a7: 0x05c7, 0x15a8: 0x05cb, 0x15a9: 0x0a37,
	0x15aa: 0x0a3f, 0x15ab: 0x0a3f, 0x15ac: 0x1683, 0x15ad: 0x0a5b, 0x15ae: 0x0a5f, 0x15af: 0x0a63,
	0x15b0: 0x0a6b, 0x15b1: 0x1688, 0x15b2: 0x0a73, 0x15b3: 0x0a77, 0x15b4: 0x0b4f, 0x15b5: 0x0a7f,
	0x15b6: 0x05cf, 0x15b7: 0x0a8b, 0x15b8: 0x0a9b, 0x15b9: 0x0aa7, 0x15ba: 0x0aa3, 0x15bb: 0x1692,
	0x15bc: 0x0aaf, 0x15bd: 0x1697, 0x15be: 0x0abb, 0x15bf: 0x0ab7,
	// Block 0x57, offset 0x15c0
	0x15c0: 0x0abf, 0x15c1: 0x0acf, 0x15c2: 0x0ad3, 0x15c3: 0x05d3, 0x15c4: 0x0ae3, 0x15c5: 0x0aeb,
	0x15c6: 0x0aef, 0x15c7: 0x0af3, 0x15c8: 0x05d7, 0x15c9: 0x169c, 0x15ca: 0x05db, 0x15cb: 0x0b0f,
	0x15cc: 0x0b13, 0x15cd: 0x0b17, 0x15ce: 0x0b1f, 0x15cf: 0x1863, 0x15d0: 0x0b37, 0x15d1: 0x16a6,
	0x15d2: 0x16a6, 0x15d3: 0x11d7, 0x15d4: 0x0b47, 0x15d5: 0x0b47, 0x15d6: 0x05df, 0x15d7: 0x16c9,
	0x15d8: 0x179b, 0x15d9: 0x0b57, 0x15da: 0x0b5f, 0x15db: 0x05e3, 0x15dc: 0x0b73, 0x15dd: 0x0b83,
	0x15de: 0x0b87, 0x15df: 0x0b8f, 0x15e0: 0x0b9f, 0x15e1: 0x05eb, 0x15e2: 0x05e7, 0x15e3: 0x0ba3,
	0x15e4: 0x16ab, 0x15e5: 0x0ba7, 0x15e6: 0x0bbb, 0x15e7: 0x0bbf, 0x15e8: 0x0bc3, 0x15e9: 0x0bbf,
	0x15ea: 0x0bcf, 0x15eb: 0x0bd3, 0x15ec: 0x0be3, 0x15ed: 0x0bdb, 0x15ee: 0x0bdf, 0x15ef: 0x0be7,
	0x15f0: 0x0beb, 0x15f1: 0x0bef, 0x15f2: 0x0bfb, 0x15f3: 0x0bff, 0x15f4: 0x0c17, 0x15f5: 0x0c1f,
	0x15f6: 0x0c2f, 0x15f7: 0x0c43, 0x15f8: 0x16ba, 0x15f9: 0x0c3f, 0x15fa: 0x0c33, 0x15fb: 0x0c4b,
	0x15fc: 0x0c53, 0x15fd: 0x0c67, 0x15fe: 0x16bf, 0x15ff: 0x0c6f,
	// Block 0x58, offset 0x1600
	0x1600: 0x0c63, 0x1601: 0x0c5b, 0x1602: 0x05ef, 0x1603: 0x0c77, 0x1604: 0x0c7f, 0x1605: 0x0c87,
	0x1606: 0x0c7b, 0x1607: 0x05f3, 0x1608: 0x0c97, 0x1609: 0x0c9f, 0x160a: 0x16c4, 0x160b: 0x0ccb,
	0x160c: 0x0cff, 0x160d: 0x0cdb, 0x160e: 0x05ff, 0x160f: 0x0ce7, 0x1610: 0x05fb, 0x1611: 0x05f7,
	0x1612: 0x07c3, 0x1613: 0x07c7, 0x1614: 0x0d03, 0x1615: 0x0ceb, 0x1616: 0x11ab, 0x1617: 0x0663,
	0x1618: 0x0d0f, 0x1619: 0x0d13, 0x161a: 0x0d17, 0x161b: 0x0d2b, 0x161c: 0x0d23, 0x161d: 0x16dd,
	0x161e: 0x0603, 0x161f: 0x0d3f, 0x1620: 0x0d33, 0x1621: 0x0d4f, 0x1622: 0x0d57, 0x1623: 0x16e7,
	0x1624: 0x0d5b, 0x1625: 0x0d47, 0x1626: 0x0d63, 0x1627: 0x0607, 0x1628: 0x0d67, 0x1629: 0x0d6b,
	0x162a: 0x0d6f, 0x162b: 0x0d7b, 0x162c: 0x16ec, 0x162d: 0x0d83, 0x162e: 0x060b, 0x162f: 0x0d8f,
	0x1630: 0x16f1, 0x1631: 0x0d93, 0x1632: 0x060f, 0x1633: 0x0d9f, 0x1634: 0x0dab, 0x1635: 0x0db7,
	0x1636: 0x0dbb, 0x1637: 0x16f6, 0x1638: 0x168d, 0x1639: 0x16fb, 0x163a: 0x0ddb, 0x163b: 0x1700,
	0x163c: 0x0de7, 0x163d: 0x0def, 0x163e: 0x0ddf, 0x163f: 0x0dfb,
	// Block 0x59, offset 0x1640
	0x1640: 0x0e0b, 0x1641: 0x0e1b, 0x1642: 0x0e0f, 0x1643: 0x0e13, 0x1644: 0x0e1f, 0x1645: 0x0e23,
	0x1646: 0x1705, 0x1647: 0x0e07, 0x1648: 0x0e3b, 0x1649: 0x0e3f, 0x164a: 0x0613, 0x164b: 0x0e53,
	0x164c: 0x0e4f, 0x164d: 0x170a, 0x164e: 0x0e33, 0x164f: 0x0e6f, 0x1650: 0x170f, 0x1651: 0x1714,
	0x1652: 0x0e73, 0x1653: 0x0e87, 0x1654: 0x0e83, 0x1655: 0x0e7f, 0x1656: 0x0617, 0x1657: 0x0e8b,
	0x1658: 0x0e9b, 0x1659: 0x0e97, 0x165a: 0x0ea3, 0x165b: 0x1651, 0x165c: 0x0eb3, 0x165d: 0x1719,
	0x165e: 0x0ebf, 0x165f: 0x1723, 0x1660: 0x0ed3, 0x1661: 0x0edf, 0x1662: 0x0ef3, 0x1663: 0x1728,
	0x1664: 0x0f07, 0x1665: 0x0f0b, 0x1666: 0x172d, 0x1667: 0x1732, 0x1668: 0x0f27, 0x1669: 0x0f37,
	0x166a: 0x061b, 0x166b: 0x0f3b, 0x166c: 0x061f, 0x166d: 0x061f, 0x166e: 0x0f53, 0x166f: 0x0f57,
	0x1670: 0x0f5f, 0x1671: 0x0f63, 0x1672: 0x0f6f, 0x1673: 0x0623, 0x1674: 0x0f87, 0x1675: 0x1737,
	0x1676: 0x0fa3, 0x1677: 0x173c, 0x1678: 0x0faf, 0x1679: 0x16a1, 0x167a: 0x0fbf, 0x167b: 0x1741,
	0x167c: 0x1746, 0x167d: 0x174b, 0x167e: 0x0627, 0x167f: 0x062b,
	// Block 0x5a, offset 0x1680
	0x1680: 0x0ff7, 0x1681: 0x1755, 0x1682: 0x1750, 0x1683: 0x175a, 0x1684: 0x175f, 0x1685: 0x0fff,
	0x1686: 0x1003, 0x1687: 0x1003, 0x1688: 0x100b, 0x1689: 0x0633, 0x168a: 0x100f, 0x168b: 0x0637,
	0x168c: 0x063b, 0x168d: 0x1769, 0x168e: 0x1023, 0x168f: 0x102b, 0x1690: 0x1037, 0x1691: 0x063f,
	0x1692: 0x176e, 0x1693: 0x105b, 0x1694: 0x1773, 0x1695: 0x1778, 0x1696: 0x107b, 0x1697: 0x1093,
	0x1698: 0x0643, 0x1699: 0x109b, 0x169a: 0x109f, 0x169b: 0x10a3, 0x169c: 0x177d, 0x169d: 0x1782,
	0x169e: 0x1782, 0x169f: 0x10bb, 0x16a0: 0x0647, 0x16a1: 0x1787, 0x16a2: 0x10cf, 0x16a3: 0x10d3,
	0x16a4: 0x064b, 0x16a5: 0x178c, 0x16a6: 0x10ef, 0x16a7: 0x064f, 0x16a8: 0x10ff, 0x16a9: 0x10f7,
	0x16aa: 0x1107, 0x16ab: 0x1796, 0x16ac: 0x111f, 0x16ad: 0x0653, 0x16ae: 0x112b, 0x16af: 0x1133,
	0x16b0: 0x1143, 0x16b1: 0x0657, 0x16b2: 0x17a0, 0x16b3: 0x17a5, 0x16b4: 0x065b, 0x16b5: 0x17aa,
	0x16b6: 0x115b, 0x16b7: 0x17af, 0x16b8: 0x1167, 0x16b9: 0x1173, 0x16ba: 0x117b, 0x16bb: 0x17b4,
	0x16bc: 0x17b9, 0x16bd: 0x118f, 0x16be: 0x17be, 0x16bf: 0x1197,
	// Block 0x5b, offset 0x16c0
	0x16c0: 0x16ce, 0x16c1: 0x065f, 0x16c2: 0x11af, 0x16c3: 0x11b3, 0x16c4: 0x0667, 0x16c5: 0x11b7,
	0x16c6: 0x0a33, 0x16c7: 0x17c3, 0x16c8: 0x17c8, 0x16c9: 0x16d3, 0x16ca: 0x16d8, 0x16cb: 0x11d7,
	0x16cc: 0x11db, 0x16cd: 0x13f3, 0x16ce: 0x066b, 0x16cf: 0x1207, 0x16d0: 0x1203, 0x16d1: 0x120b,
	0x16d2: 0x083f, 0x16d3: 0x120f, 0x16d4: 0x1213, 0x16d5: 0x1217, 0x16d6: 0x121f, 0x16d7: 0x17cd,
	0x16d8: 0x121b, 0x16d9: 0x1223, 0x16da: 0x1237, 0x16db: 0x123b, 0x16dc: 0x1227, 0x16dd: 0x123f,
	0x16de: 0x1253, 0x16df: 0x1267, 0x16e0: 0x1233, 0x16e1: 0x1247, 0x16e2: 0x124b, 0x16e3: 0x124f,
	0x16e4: 0x17d2, 0x16e5: 0x17dc, 0x16e6: 0x17d7, 0x16e7: 0x066f, 0x16e8: 0x126f, 0x16e9: 0x1273,
	0x16ea: 0x127b, 0x16eb: 0x17f0, 0x16ec: 0x127f, 0x16ed: 0x17e1, 0x16ee: 0x0673, 0x16ef: 0x0677,
	0x16f0: 0x17e6, 0x16f1: 0x17eb, 0x16f2: 0x067b, 0x16f3: 0x129f, 0x16f4: 0x12a3, 0x16f5: 0x12a7,
	0x16f6: 0x12ab, 0x16f7: 0x12b7, 0x16f8: 0x12b3, 0x16f9: 0x12bf, 0x16fa: 0x12bb, 0x16fb: 0x12cb,
	0x16fc: 0x12c3, 0x16fd: 0x12c7, 0x16fe: 0x12cf, 0x16ff: 0x067f,
	// Block 0x5c, offset 0x1700
	0x1700: 0x12d7, 0x1701: 0x12db, 0x1702: 0x0683, 0x1703: 0x12eb, 0x1704: 0x12ef, 0x1705: 0x17f5,
	0x1706: 0x12fb, 0x1707: 0x12ff, 0x1708: 0x0687, 0x1709: 0x130b, 0x170a: 0x05bb, 0x170b: 0x17fa,
	0x170c: 0x17ff, 0x170d: 0x068b, 0x170e: 0x068f, 0x170f: 0x1337, 0x1710: 0x134f, 0x1711: 0x136b,
	0x1712: 0x137b, 0x1713: 0x1804, 0x1714: 0x138f, 0x1715: 0x1393, 0x1716: 0x13ab, 0x1717: 0x13b7,
	0x1718: 0x180e, 0x1719: 0x1660, 0x171a: 0x13c3, 0x171b: 0x13bf, 0x171c: 0x13cb, 0x171d: 0x1665,
	0x171e: 0x13d7, 0x171f: 0x13e3, 0x1720: 0x1813, 0x1721: 0x1818, 0x1722: 0x1423, 0x1723: 0x142f,
	0x1724: 0x1437, 0x1725: 0x181d, 0x1726: 0x143b, 0x1727: 0x1467, 0x1728: 0x1473, 0x1729: 0x1477,
	0x172a: 0x146f, 0x172b: 0x1483, 0x172c: 0x1487, 0x172d: 0x1822, 0x172e: 0x1493, 0x172f: 0x0693,
	0x1730: 0x149b, 0x1731: 0x1827, 0x1732: 0x0697, 0x1733: 0x14d3, 0x1734: 0x0ac3, 0x1735: 0x14eb,
	0x1736: 0x182c, 0x1737: 0x1836, 0x1738: 0x069b, 0x1739: 0x069f, 0x173a: 0x1513, 0x173b: 0x183b,
	0x173c: 0x06a3, 0x173d: 0x1840, 0x173e: 0x152b, 0x173f: 0x152b,
	// Block 0x5d, offset 0x1740
	0x1740: 0x1533, 0x1741: 0x1845, 0x1742: 0x154b, 0x1743: 0x06a7, 0x1744: 0x155b, 0x1745: 0x1567,
	0x1746: 0x156f, 0x1747: 0x1577, 0x1748: 0x06ab, 0x1749: 0x184a, 0x174a: 0x158b, 0x174b: 0x15a7,
	0x174c: 0x15b3, 0x174d: 0x06af, 0x174e: 0x06b3, 0x174f: 0x15b7, 0x1750: 0x184f, 0x1751: 0x06b7,
	0x1752: 0x1854, 0x1753: 0x1859, 0x1754: 0x185e, 0x1755: 0x15db, 0x1756: 0x06bb, 0x1757: 0x15ef,
	0x1758: 0x15f7, 0x1759: 0x15fb, 0x175a: 0x1603, 0x175b: 0x160b, 0x175c: 0x1613, 0x175d: 0x1868,
}

// nfkcIndex: 22 blocks, 1408 entries, 2816 bytes
// Block 0 is the zero block.
var nfkcIndex = [1408]uint16{
	// Block 0x0, offset 0x0
	// Block 0x1, offset 0x40
	// Block 0x2, offset 0x80
	// Block 0x3, offset 0xc0
	0xc2: 0x5c, 0xc3: 0x01, 0xc4: 0x02, 0xc5: 0x03, 0xc6: 0x5d, 0xc7: 0x04,
	0xc8: 0x05, 0xca: 0x5e, 0xcb: 0x5f, 0xcc: 0x06, 0xcd: 0x07, 0xce: 0x08, 0xcf: 0x09,
	0xd0: 0x0a, 0xd1: 0x60, 0xd2: 0x61, 0xd3: 0x0b, 0xd6: 0x0c, 0xd7: 0x62,
	0xd8: 0x63, 0xd9: 0x0d, 0xdb: 0x64, 0xdc: 0x65, 0xdd: 0x66, 0xdf: 0x67,
	0xe0: 0x02, 0xe1: 0x03, 0xe2: 0x04, 0xe3: 0x05,
	0xea: 0x06, 0xeb: 0x07, 0xec: 0x08, 0xed: 0x09, 0xef: 0x0a,
	0xf0: 0x13,
	// Block 0x4, offset 0x100
	0x120: 0x68, 0x121: 0x69, 0x123: 0x0e, 0x124: 0x6a, 0x125: 0x6b, 0x126: 0x6c, 0x127: 0x6d,
	0x128: 0x6e, 0x129: 0x6f, 0x12a: 0x70, 0x12b: 0x71, 0x12c: 0x6c, 0x12d: 0x72, 0x12e: 0x73, 0x12f: 0x74,
	0x131: 0x75, 0x132: 0x76, 0x133: 0x77, 0x134: 0x78, 0x135: 0x79, 0x137: 0x7a,
	0x138: 0x7b, 0x139: 0x7c, 0x13a: 0x7d, 0x13b: 0x7e, 0x13c: 0x7f, 0x13d: 0x80, 0x13e: 0x81, 0x13f: 0x82,
	// Block 0x5, offset 0x140
	0x140: 0x83, 0x142: 0x84, 0x143: 0x85, 0x144: 0x86, 0x145: 0x87, 0x146: 0x88, 0x147: 0x89,
	0x14d: 0x8a,
	0x15c: 0x8b, 0x15f: 0x8c,
	0x162: 0x8d, 0x164: 0x8e,
	0x168: 0x8f, 0x169: 0x90, 0x16a: 0x91, 0x16c: 0x0f, 0x16d: 0x92, 0x16e: 0x93, 0x16f: 0x94,
	0x170: 0x95, 0x173: 0x96, 0x174: 0x97, 0x175: 0x10, 0x176: 0x11, 0x177: 0x12,
	0x178: 0x13, 0x179: 0x14, 0x17a: 0x15, 0x17b: 0x16, 0x17c: 0x17, 0x17d: 0x18, 0x17e: 0x19, 0x17f: 0x1a,
	// Block 0x6, offset 0x180
	0x180: 0x98, 0x181: 0x99, 0x182: 0x9a, 0x183: 0x9b, 0x184: 0x1b, 0x185: 0x1c, 0x186: 0x9c, 0x187: 0x9d,
	0x188: 0x9e, 0x189: 0x1d, 0x18a: 0x1e, 0x18b: 0x9f, 0x18c: 0xa0,
	0x191: 0x1f, 0x192: 0x20, 0x193: 0xa1,
	0x1a8: 0xa2, 0x1a9: 0xa3, 0x1ab: 0xa4,
	0x1b1: 0xa5, 0x1b3: 0xa6, 0x1b5: 0xa7, 0x1b7: 0xa8,
	0x1ba: 0xa9, 0x1bb: 0xaa, 0x1bc: 0x21, 0x1bd: 0x22, 0x1be: 0x23, 0x1bf: 0xab,
	// Block 0x7, offset 0x1c0
	0x1c0: 0xac, 0x1c1: 0x24, 0x1c2: 0x25, 0x1c3: 0x26, 0x1c4: 0xad, 0x1c5: 0x27, 0x1c6: 0x28,
	0x1c8: 0x29, 0x1c9: 0x2a, 0x1ca: 0x2b, 0x1cb: 0x2c, 0x1cc: 0x2d, 0x1cd: 0x2e, 0x1ce: 0x2f, 0x1cf: 0x30,
	// Block 0x8, offset 0x200
	0x219: 0xae, 0x21a: 0xaf, 0x21b: 0xb0, 0x21d: 0xb1, 0x21f: 0xb2,
	0x220: 0xb3, 0x223: 0xb4, 0x224: 0xb5, 0x225: 0xb6, 0x226: 0xb7, 0x227: 0xb8,
	0x22a: 0xb9, 0x22b: 0xba, 0x22d: 0xbb, 0x22f: 0xbc,
	0x230: 0xbd, 0x231: 0xbe, 0x232: 0xbf, 0x233: 0xc0, 0x234: 0xc1, 0x235: 0xc2, 0x236: 0xc3, 0x237: 0xbd,
	0x238: 0xbe, 0x239: 0xbf, 0x23a: 0xc0, 0x23b: 0xc1, 0x23c: 0xc2, 0x23d: 0xc3, 0x23e: 0xbd, 0x23f: 0xbe,
	// Block 0x9, offset 0x240
	0x240: 0xbf, 0x241: 0xc0, 0x242: 0xc1, 0x243: 0xc2, 0x244: 0xc3, 0x245: 0xbd, 0x246: 0xbe, 0x247: 0xbf,
	0x248: 0xc0, 0x249: 0xc1, 0x24a: 0xc2, 0x24b: 0xc3, 0x24c: 0xbd, 0x24d: 0xbe, 0x24e: 0xbf, 0x24f: 0xc0,
	0x250: 0xc1, 0x251: 0xc2, 0x252: 0xc3, 0x253: 0xbd, 0x254: 0xbe, 0x255: 0xbf, 0x256: 0xc0, 0x257: 0xc1,
	0x258: 0xc2, 0x259: 0xc3, 0x25a: 0xbd, 0x25b: 0xbe, 0x25c: 0xbf, 0x25d: 0xc0, 0x25e: 0xc1, 0x25f: 0xc2,
	0x260: 0xc3, 0x261: 0xbd, 0x262: 0xbe, 0x263: 0xbf, 0x264: 0xc0, 0x265: 0xc1, 0x266: 0xc2, 0x267: 0xc3,
	0x268: 0xbd, 0x269: 0xbe, 0x26a: 0xbf, 0x26b: 0xc0, 0x26c: 0xc1, 0x26d: 0xc2, 0x26e: 0xc3, 0x26f: 0xbd,
	0x270: 0xbe, 0x271: 0xbf, 0x272: 0xc0, 0x273: 0xc1, 0x274: 0xc2, 0x275: 0xc3, 0x276: 0xbd, 0x277: 0xbe,
	0x278: 0xbf, 0x279: 0xc0, 0x27a: 0xc1, 0x27b: 0xc2, 0x27c: 0xc3, 0x27d: 0xbd, 0x27e: 0xbe, 0x27f: 0xbf,
	// Block 0xa, offset 0x280
	0x280: 0xc0, 0x281: 0xc1, 0x282: 0xc2, 0x283: 0xc3, 0x284: 0xbd, 0x285: 0xbe, 0x286: 0xbf, 0x287: 0xc0,
	0x288: 0xc1, 0x289: 0xc2, 0x28a: 0xc3, 0x28b: 0xbd, 0x28c: 0xbe, 0x28d: 0xbf, 0x28e: 0xc0, 0x28f: 0xc1,
	0x290: 0xc2, 0x291: 0xc3, 0x292: 0xbd, 0x293: 0xbe, 0x294: 0xbf, 0x295: 0xc0, 0x296: 0xc1, 0x297: 0xc2,
	0x298: 0xc3, 0x299: 0xbd, 0x29a: 0xbe, 0x29b: 0xbf, 0x29c: 0xc0, 0x29d: 0xc1, 0x29e: 0xc2, 0x29f: 0xc3,
	0x2a0: 0xbd, 0x2a1: 0xbe, 0x2a2: 0xbf, 0x2a3: 0xc0, 0x2a4: 0xc1, 0x2a5: 0xc2, 0x2a6: 0xc3, 0x2a7: 0xbd,
	0x2a8: 0xbe, 0x2a9: 0xbf, 0x2aa: 0xc0, 0x2ab: 0xc1, 0x2ac: 0xc2, 0x2ad: 0xc3, 0x2ae: 0xbd, 0x2af: 0xbe,
	0x2b0: 0xbf, 0x2b1: 0xc0, 0x2b2: 0xc1, 0x2b3: 0xc2, 0x2b4: 0xc3, 0x2b5: 0xbd, 0x2b6: 0xbe, 0x2b7: 0xbf,
	0x2b8: 0xc0, 0x2b9: 0xc1, 0x2ba: 0xc2, 0x2bb: 0xc3, 0x2bc: 0xbd, 0x2bd: 0xbe, 0x2be: 0xbf, 0x2bf: 0xc0,
	// Block 0xb, offset 0x2c0
	0x2c0: 0xc1, 0x2c1: 0xc2, 0x2c2: 0xc3, 0x2c3: 0xbd, 0x2c4: 0xbe, 0x2c5: 0xbf, 0x2c6: 0xc0, 0x2c7: 0xc1,
	0x2c8: 0xc2, 0x2c9: 0xc3, 0x2ca: 0xbd, 0x2cb: 0xbe, 0x2cc: 0xbf, 0x2cd: 0xc0, 0x2ce: 0xc1, 0x2cf: 0xc2,
	0x2d0: 0xc3, 0x2d1: 0xbd, 0x2d2: 0xbe, 0x2d3: 0xbf, 0x2d4: 0xc0, 0x2d5: 0xc1, 0x2d6: 0xc2, 0x2d7: 0xc3,
	0x2d8: 0xbd, 0x2d9: 0xbe, 0x2da: 0xbf, 0x2db: 0xc0, 0x2dc: 0xc1, 0x2dd: 0xc2, 0x2de: 0xc4,
	// Block 0xc, offset 0x300
	0x324: 0x31, 0x325: 0x32, 0x326: 0x33, 0x327: 0x34,
	0x328: 0x35, 0x329: 0x36, 0x32a: 0x37, 0x32b: 0x38, 0x32c: 0x39, 0x32d: 0x3a, 0x32e: 0x3b, 0x32f: 0x3c,
	0x330: 0x3d, 0x331: 0x3e, 0x332: 0x3f, 0x333: 0x40, 0x334: 0x41, 0x335: 0x42, 0x336: 0x43, 0x337: 0x44,
	0x338: 0x45, 0x339: 0x46, 0x33a: 0x47, 0x33b: 0x48, 0x33c: 0xc5, 0x33d: 0x49, 0x33e: 0x4a, 0x33f: 0x4b,
	// Block 0xd, offset 0x340
	0x347: 0xc6,
	0x34b: 0xc7, 0x34d: 0xc8,
	0x368: 0xc9, 0x36b: 0xca,
	0x374: 0xcb,
	0x37d: 0xcc,
	// Block 0xe, offset 0x380
	0x381: 0xcd, 0x382: 0xce, 0x384: 0xcf, 0x385: 0xb7, 0x387: 0xd0,
	0x388: 0xd1, 0x38b: 0xd2, 0x38c: 0xd3, 0x38d: 0xd4,
	0x391: 0xd5, 0x392: 0xd6, 0x393: 0xd7, 0x396: 0xd8, 0x397: 0xd9,
	0x398: 0xda, 0x39a: 0xdb, 0x39c: 0xdc,
	0x3a0: 0xdd, 0x3a7: 0xde,
	0x3a8: 0xdf, 0x3a9: 0xe0, 0x3aa: 0xe1,
	0x3b0: 0xda, 0x3b5: 0xe2, 0x3b6: 0xe3,
	// Block 0xf, offset 0x3c0
	0x3eb: 0xe4, 0x3ec: 0xe5,
	// Block 0x10, offset 0x400
	0x432: 0xe6,
	// Block 0x11, offset 0x440
	0x445: 0xe7, 0x446: 0xe8, 0x447: 0xe9,
	0x449: 0xea,
	0x450: 0xeb, 0x451: 0xec, 0x452: 0xed, 0x453: 0xee, 0x454: 0xef, 0x455: 0xf0, 0x456: 0xf1, 0x457: 0xf2,
	0x458: 0xf3, 0x459: 0xf4, 0x45a: 0x4c, 0x45b: 0xf5, 0x45c: 0xf6, 0x45d: 0xf7, 0x45e: 0xf8, 0x45f: 0x4d,
	// Block 0x12, offset 0x480
	0x480: 0xf9, 0x484: 0xe5,
	0x48b: 0xfa,
	0x4a3: 0xfb, 0x4a5: 0xfc,
	0x4b8: 0x4e, 0x4b9: 0x4f, 0x4ba: 0x50,
	// Block 0x13, offset 0x4c0
	0x4c4: 0x51, 0x4c5: 0xfd, 0x4c6: 0xfe,
	0x4c8: 0x52, 0x4c9: 0xff,
	// Block 0x14, offset 0x500
	0x520: 0x53, 0x521: 0x54, 0x522: 0x55, 0x523: 0x56, 0x524: 0x57, 0x525: 0x58, 0x526: 0x59, 0x527: 0x5a,
	0x528: 0x5b,
	// Block 0x15, offset 0x540
	0x550: 0x0b, 0x551: 0x0c, 0x556: 0x0d,
	0x55b: 0x0e, 0x55d: 0x0f, 0x55e: 0x10, 0x55f: 0x11,
	0x56f: 0x12,
}

// nfkcSparseOffset: 164 entries, 328 bytes
var nfkcSparseOffset = []uint16{0x0, 0xe, 0x12, 0x1b, 0x25, 0x35, 0x37, 0x3c, 0x47, 0x56, 0x63, 0x6b, 0x70, 0x75, 0x77, 0x7f, 0x86, 0x89, 0x91, 0x95, 0x99, 0x9b, 0x9d, 0xa6, 0xaa, 0xb1, 0xb6, 0xb9, 0xc3, 0xc6, 0xcd, 0xd5, 0xd9, 0xdb, 0xdf, 0xe3, 0xe9, 0xfa, 0x106, 0x108, 0x10e, 0x110, 0x112, 0x114, 0x116, 0x118, 0x11a, 0x11c, 0x11f, 0x122, 0x124, 0x127, 0x12a, 0x12e, 0x133, 0x13c, 0x13e, 0x141, 0x143, 0x14e, 0x159, 0x167, 0x175, 0x185, 0x193, 0x19a, 0x1a0, 0x1af, 0x1b3, 0x1b5, 0x1b9, 0x1bb, 0x1be, 0x1c0, 0x1c3, 0x1c5, 0x1c8, 0x1ca, 0x1cc, 0x1ce, 0x1da, 0x1e4, 0x1ee, 0x1f1, 0x1f5, 0x1f7, 0x1f9, 0x1fb, 0x1fd, 0x200, 0x202, 0x204, 0x206, 0x208, 0x20e, 0x211, 0x215, 0x217, 0x21e, 0x224, 0x22a, 0x232, 0x238, 0x23e, 0x244, 0x248, 0x24a, 0x24c, 0x24e, 0x250, 0x256, 0x259, 0x25b, 0x261, 0x264, 0x26c, 0x273, 0x276, 0x279, 0x27b, 0x27e, 0x286, 0x28a, 0x291, 0x294, 0x29a, 0x29c, 0x29e, 0x2a1, 0x2a3, 0x2a6, 0x2a8, 0x2aa, 0x2ac, 0x2ae, 0x2b1, 0x2b3, 0x2b5, 0x2b7, 0x2b9, 0x2c6, 0x2d0, 0x2d2, 0x2d4, 0x2d8, 0x2dd, 0x2e9, 0x2ee, 0x2f7, 0x2fd, 0x302, 0x306, 0x30b, 0x30f, 0x31f, 0x32d, 0x33b, 0x349, 0x34f, 0x351, 0x353, 0x356, 0x361, 0x363}

// nfkcSparseValues: 877 entries, 3508 bytes
var nfkcSparseValues = [877]valueRange{
	// Block 0x0, offset 0x0
	{value: 0x0002, lo: 0x0d},
	{value: 0x0001, lo: 0xa0, hi: 0xa0},
	{value: 0x427b, lo: 0xa8, hi: 0xa8},
	{value: 0x0083, lo: 0xaa, hi: 0xaa},
	{value: 0x4267, lo: 0xaf, hi: 0xaf},
	{value: 0x0025, lo: 0xb2, hi: 0xb3},
	{value: 0x425d, lo: 0xb4, hi: 0xb4},
	{value: 0x01dc, lo: 0xb5, hi: 0xb5},
	{value: 0x4294, lo: 0xb8, hi: 0xb8},
	{value: 0x0023, lo: 0xb9, hi: 0xb9},
	{value: 0x009f, lo: 0xba, hi: 0xba},
	{value: 0x221f, lo: 0xbc, hi: 0xbc},
	{value: 0x2213, lo: 0xbd, hi: 0xbd},
	{value: 0x22b5, lo: 0xbe, hi: 0xbe},
	// Block 0x1, offset 0xe
	{value: 0x0091, lo: 0x03},
	{value: 0x46e5, lo: 0xa0, hi: 0xa1},
	{value: 0x4717, lo: 0xaf, hi: 0xb0},
	{value: 0xa000, lo: 0xb7, hi: 0xb7},
	// Block 0x2, offset 0x12
	{value: 0x0003, lo: 0x08},
	{value: 0xa000, lo: 0x92, hi: 0x92},
	{value: 0x0091, lo: 0xb0, hi: 0xb0},
	{value: 0x0119, lo: 0xb1, hi: 0xb1},
	{value: 0x0095, lo: 0xb2, hi: 0xb2},
	{value: 0x00a5, lo: 0xb3, hi: 0xb3},
	{value: 0x0143, lo: 0xb4, hi: 0xb6},
	{value: 0x00af, lo: 0xb7, hi: 0xb7},
	{value: 0x00b3, lo: 0xb8, hi: 0xb8},
	// Block 0x3, offset 0x1b
	{value: 0x000a, lo: 0x09},
	{value: 0x4271, lo: 0x98, hi: 0x98},
	{value: 0x4276, lo: 0x99, hi: 0x9a},
	{value: 0x4299, lo: 0x9b, hi: 0x9b},
	{value: 0x4262, lo: 0x9c, hi: 0x9c},
	{value: 0x4285, lo: 0x9d, hi: 0x9d},
	{value: 0x0113, lo: 0xa0, hi: 0xa0},
	{value: 0x0099, lo: 0xa1, hi: 0xa1},
	{value: 0x00a7, lo: 0xa2, hi: 0xa3},
	{value: 0x0167, lo: 0xa4, hi: 0xa4},
	// Block 0x4, offset 0x25
	{value: 0x0000, lo: 0x0f},
	{value: 0xa000, lo: 0x83, hi: 0x83},
	{value: 0xa000, lo: 0x87, hi: 0x87},
	{value: 0xa000, lo: 0x8b, hi: 0x8b},
	{value: 0xa000, lo: 0x8d, hi: 0x8d},
	{value: 0x37a8, lo: 0x90, hi: 0x90},
	{value: 0x37b4, lo: 0x91, hi: 0x91},
	{value: 0x37a2, lo: 0x93, hi: 0x93},
	{value: 0xa000, lo: 0x96, hi: 0x96},
	{value: 0x381a, lo: 0x97, hi: 0x97},
	{value: 0x37e4, lo: 0x9c, hi: 0x9c},
	{value: 0x37cc, lo: 0x9d, hi: 0x9d},
	{value: 0x37f6, lo: 0x9e, hi: 0x9e},
	{value: 0xa000, lo: 0xb4, hi: 0xb5},
	{value: 0x3820, lo: 0xb6, hi: 0xb6},
	{value: 0x3826, lo: 0xb7, hi: 0xb7},
	// Block 0x5, offset 0x35
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0x83, hi: 0x87},
	// Block 0x6, offset 0x37
	{value: 0x0001, lo: 0x04},
	{value: 0x8113, lo: 0x81, hi: 0x82},
	{value: 0x8132, lo: 0x84, hi: 0x84},
	{value: 0x812d, lo: 0x85, hi: 0x85},
	{value: 0x810d, lo: 0x87, hi: 0x87},
	// Block 0x7, offset 0x3c
	{value: 0x0000, lo: 0x0a},
	{value: 0x8132, lo: 0x90, hi: 0x97},
	{value: 0x8119, lo: 0x98, hi: 0x98},
	{value: 0x811a, lo: 0x99, hi: 0x99},
	{value: 0x811b, lo: 0x9a, hi: 0x9a},
	{value: 0x3844, lo: 0xa2, hi: 0xa2},
	{value: 0x384a, lo: 0xa3, hi: 0xa3},
	{value: 0x3856, lo: 0xa4, hi: 0xa4},
	{value: 0x3850, lo: 0xa5, hi: 0xa5},
	{value: 0x385c, lo: 0xa6, hi: 0xa6},
	{value: 0xa000, lo: 0xa7, hi: 0xa7},
	// Block 0x8, offset 0x47
	{value: 0x0000, lo: 0x0e},
	{value: 0x386e, lo: 0x80, hi: 0x80},
	{value: 0xa000, lo: 0x81, hi: 0x81},
	{value: 0x3862, lo: 0x82, hi: 0x82},
	{value: 0xa000, lo: 0x92, hi: 0x92},
	{value: 0x3868, lo: 0x93, hi: 0x93},
	{value: 0xa000, lo: 0x95, hi: 0x95},
	{value: 0x8132, lo: 0x96, hi: 0x9c},
	{value: 0x8132, lo: 0x9f, hi: 0xa2},
	{value: 0x812d, lo: 0xa3, hi: 0xa3},
	{value: 0x8132, lo: 0xa4, hi: 0xa4},
	{value: 0x8132, lo: 0xa7, hi: 0xa8},
	{value: 0x812d, lo: 0xaa, hi: 0xaa},
	{value: 0x8132, lo: 0xab, hi: 0xac},
	{value: 0x812d, lo: 0xad, hi: 0xad},
	// Block 0x9, offset 0x56
	{value: 0x0000, lo: 0x0c},
	{value: 0x811f, lo: 0x91, hi: 0x91},
	{value: 0x8132, lo: 0xb0, hi: 0xb0},
	{value: 0x812d, lo: 0xb1, hi: 0xb1},
	{value: 0x8132, lo: 0xb2, hi: 0xb3},
	{value: 0x812d, lo: 0xb4, hi: 0xb4},
	{value: 0x8132, lo: 0xb5, hi: 0xb6},
	{value: 0x812d, lo: 0xb7, hi: 0xb9},
	{value: 0x8132, lo: 0xba, hi: 0xba},
	{value: 0x812d, lo: 0xbb, hi: 0xbc},
	{value: 0x8132, lo: 0xbd, hi: 0xbd},
	{value: 0x812d, lo: 0xbe, hi: 0xbe},
	{value: 0x8132, lo: 0xbf, hi: 0xbf},
	// Block 0xa, offset 0x63
	{value: 0x0005, lo: 0x07},
	{value: 0x8132, lo: 0x80, hi: 0x80},
	{value: 0x8132, lo: 0x81, hi: 0x81},
	{value: 0x812d, lo: 0x82, hi: 0x83},
	{value: 0x812d, lo: 0x84, hi: 0x85},
	{value: 0x812d, lo: 0x86, hi: 0x87},
	{value: 0x812d, lo: 0x88, hi: 0x89},
	{value: 0x8132, lo: 0x8a, hi: 0x8a},
	// Block 0xb, offset 0x6b
	{value: 0x0000, lo: 0x04},
	{value: 0x8132, lo: 0xab, hi: 0xb1},
	{value: 0x812d, lo: 0xb2, hi: 0xb2},
	{value: 0x8132, lo: 0xb3, hi: 0xb3},
	{value: 0x812d, lo: 0xbd, hi: 0xbd},
	// Block 0xc, offset 0x70
	{value: 0x0000, lo: 0x04},
	{value: 0x8132, lo: 0x96, hi: 0x99},
	{value: 0x8132, lo: 0x9b, hi: 0xa3},
	{value: 0x8132, lo: 0xa5, hi: 0xa7},
	{value: 0x8132, lo: 0xa9, hi: 0xad},
	// Block 0xd, offset 0x75
	{value: 0x0000, lo: 0x01},
	{value: 0x812d, lo: 0x99, hi: 0x9b},
	// Block 0xe, offset 0x77
	{value: 0x0000, lo: 0x07},
	{value: 0xa000, lo: 0xa8, hi: 0xa8},
	{value: 0x3edb, lo: 0xa9, hi: 0xa9},
	{value: 0xa000, lo: 0xb0, hi: 0xb0},
	{value: 0x3ee3, lo: 0xb1, hi: 0xb1},
	{value: 0xa000, lo: 0xb3, hi: 0xb3},
	{value: 0x3eeb, lo: 0xb4, hi: 0xb4},
	{value: 0x9902, lo: 0xbc, hi: 0xbc},
	// Block 0xf, offset 0x7f
	{value: 0x0008, lo: 0x06},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x8132, lo: 0x91, hi: 0x91},
	{value: 0x812d, lo: 0x92, hi: 0x92},
	{value: 0x8132, lo: 0x93, hi: 0x93},
	{value: 0x8132, lo: 0x94, hi: 0x94},
	{value: 0x451f, lo: 0x98, hi: 0x9f},
	// Block 0x10, offset 0x86
	{value: 0x0000, lo: 0x02},
	{value: 0x8102, lo: 0xbc, hi: 0xbc},
	{value: 0x9900, lo: 0xbe, hi: 0xbe},
	// Block 0x11, offset 0x89
	{value: 0x0008, lo: 0x07},
	{value: 0xa000, lo: 0x87, hi: 0x87},
	{value: 0x2ca1, lo: 0x8b, hi: 0x8c},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x9900, lo: 0x97, hi: 0x97},
	{value: 0x455f, lo: 0x9c, hi: 0x9d},
	{value: 0x456f, lo: 0x9f, hi: 0x9f},
	{value: 0x8132, lo: 0xbe, hi: 0xbe},
	// Block 0x12, offset 0x91
	{value: 0x0000, lo: 0x03},
	{value: 0x4597, lo: 0xb3, hi: 0xb3},
	{value: 0x459f, lo: 0xb6, hi: 0xb6},
	{value: 0x8102, lo: 0xbc, hi: 0xbc},
	// Block 0x13, offset 0x95
	{value: 0x0008, lo: 0x03},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x4577, lo: 0x99, hi: 0x9b},
	{value: 0x458f, lo: 0x9e, hi: 0x9e},
	// Block 0x14, offset 0x99
	{value: 0x0000, lo: 0x01},
	{value: 0x8102, lo: 0xbc, hi: 0xbc},
	// Block 0x15, offset 0x9b
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	// Block 0x16, offset 0x9d
	{value: 0x0000, lo: 0x08},
	{value: 0xa000, lo: 0x87, hi: 0x87},
	{value: 0x2cb9, lo: 0x88, hi: 0x88},
	{value: 0x2cb1, lo: 0x8b, hi: 0x8b},
	{value: 0x2cc1, lo: 0x8c, hi: 0x8c},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x9900, lo: 0x96, hi: 0x97},
	{value: 0x45a7, lo: 0x9c, hi: 0x9c},
	{value: 0x45af, lo: 0x9d, hi: 0x9d},
	// Block 0x17, offset 0xa6
	{value: 0x0000, lo: 0x03},
	{value: 0xa000, lo: 0x92, hi: 0x92},
	{value: 0x2cc9, lo: 0x94, hi: 0x94},
	{value: 0x9900, lo: 0xbe, hi: 0xbe},
	// Block 0x18, offset 0xaa
	{value: 0x0000, lo: 0x06},
	{value: 0xa000, lo: 0x86, hi: 0x87},
	{value: 0x2cd1, lo: 0x8a, hi: 0x8a},
	{value: 0x2ce1, lo: 0x8b, hi: 0x8b},
	{value: 0x2cd9, lo: 0x8c, hi: 0x8c},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x9900, lo: 0x97, hi: 0x97},
	// Block 0x19, offset 0xb1
	{value: 0x1801, lo: 0x04},
	{value: 0xa000, lo: 0x86, hi: 0x86},
	{value: 0x3ef3, lo: 0x88, hi: 0x88},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x8120, lo: 0x95, hi: 0x96},
	// Block 0x1a, offset 0xb6
	{value: 0x0000, lo: 0x02},
	{value: 0x8102, lo: 0xbc, hi: 0xbc},
	{value: 0xa000, lo: 0xbf, hi: 0xbf},
	// Block 0x1b, offset 0xb9
	{value: 0x0000, lo: 0x09},
	{value: 0x2ce9, lo: 0x80, hi: 0x80},
	{value: 0x9900, lo: 0x82, hi: 0x82},
	{value: 0xa000, lo: 0x86, hi: 0x86},
	{value: 0x2cf1, lo: 0x87, hi: 0x87},
	{value: 0x2cf9, lo: 0x88, hi: 0x88},
	{value: 0x2f53, lo: 0x8a, hi: 0x8a},
	{value: 0x2ddb, lo: 0x8b, hi: 0x8b},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x9900, lo: 0x95, hi: 0x96},
	// Block 0x1c, offset 0xc3
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0xbb, hi: 0xbc},
	{value: 0x9900, lo: 0xbe, hi: 0xbe},
	// Block 0x1d, offset 0xc6
	{value: 0x0000, lo: 0x06},
	{value: 0xa000, lo: 0x86, hi: 0x87},
	{value: 0x2d01, lo: 0x8a, hi: 0x8a},
	{value: 0x2d11, lo: 0x8b, hi: 0x8b},
	{value: 0x2d09, lo: 0x8c, hi: 0x8c},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x9900, lo: 0x97, hi: 0x97},
	// Block 0x1e, offset 0xcd
	{value: 0x6be7, lo: 0x07},
	{value: 0x9904, lo: 0x8a, hi: 0x8a},
	{value: 0x9900, lo: 0x8f, hi: 0x8f},
	{value: 0xa000, lo: 0x99, hi: 0x99},
	{value: 0x3efb, lo: 0x9a, hi: 0x9a},
	{value: 0x2f5b, lo: 0x9c, hi: 0x9c},
	{value: 0x2de6, lo: 0x9d, hi: 0x9d},
	{value: 0x2d19, lo: 0x9e, hi: 0x9f},
	// Block 0x1f, offset 0xd5
	{value: 0x0000, lo: 0x03},
	{value: 0x2624, lo: 0xb3, hi: 0xb3},
	{value: 0x8122, lo: 0xb8, hi: 0xb9},
	{value: 0x8104, lo: 0xba, hi: 0xba},
	// Block 0x20, offset 0xd9
	{value: 0x0000, lo: 0x01},
	{value: 0x8123, lo: 0x88, hi: 0x8b},
	// Block 0x21, offset 0xdb
	{value: 0x0000, lo: 0x03},
	{value: 0x2639, lo: 0xb3, hi: 0xb3},
	{value: 0x8124, lo: 0xb8, hi: 0xb9},
	{value: 0x8104, lo: 0xba, hi: 0xba},
	// Block 0x22, offset 0xdf
	{value: 0x0000, lo: 0x03},
	{value: 0x8125, lo: 0x88, hi: 0x8b},
	{value: 0x262b, lo: 0x9c, hi: 0x9c},
	{value: 0x2632, lo: 0x9d, hi: 0x9d},
	// Block 0x23, offset 0xe3
	{value: 0x0000, lo: 0x05},
	{value: 0x030b, lo: 0x8c, hi: 0x8c},
	{value: 0x812d, lo: 0x98, hi: 0x99},
	{value: 0x812d, lo: 0xb5, hi: 0xb5},
	{value: 0x812d, lo: 0xb7, hi: 0xb7},
	{value: 0x812b, lo: 0xb9, hi: 0xb9},
	// Block 0x24, offset 0xe9
	{value: 0x0000, lo: 0x10},
	{value: 0x2647, lo: 0x83, hi: 0x83},
	{value: 0x264e, lo: 0x8d, hi: 0x8d},
	{value: 0x2655, lo: 0x92, hi: 0x92},
	{value: 0x265c, lo: 0x97, hi: 0x97},
	{value: 0x2663, lo: 0x9c, hi: 0x9c},
	{value: 0x2640, lo: 0xa9, hi: 0xa9},
	{value: 0x8126, lo: 0xb1, hi: 0xb1},
	{value: 0x8127, lo: 0xb2, hi: 0xb2},
	{value: 0x4a87, lo: 0xb3, hi: 0xb3},
	{value: 0x8128, lo: 0xb4, hi: 0xb4},
	{value: 0x4a90, lo: 0xb5, hi: 0xb5},
	{value: 0x45b7, lo: 0xb6, hi: 0xb6},
	{value: 0x45f7, lo: 0xb7, hi: 0xb7},
	{value: 0x45bf, lo: 0xb8, hi: 0xb8},
	{value: 0x4602, lo: 0xb9, hi: 0xb9},
	{value: 0x8127, lo: 0xba, hi: 0xbd},
	// Block 0x25, offset 0xfa
	{value: 0x0000, lo: 0x0b},
	{value: 0x8127, lo: 0x80, hi: 0x80},
	{value: 0x4a99, lo: 0x81, hi: 0x81},
	{value: 0x8132, lo: 0x82, hi: 0x83},
	{value: 0x8104, lo: 0x84, hi: 0x84},
	{value: 0x8132, lo: 0x86, hi: 0x87},
	{value: 0x2671, lo: 0x93, hi: 0x93},
	{value: 0x2678, lo: 0x9d, hi: 0x9d},
	{value: 0x267f, lo: 0xa2, hi: 0xa2},
	{value: 0x2686, lo: 0xa7, hi: 0xa7},
	{value: 0x268d, lo: 0xac, hi: 0xac},
	{value: 0x266a, lo: 0xb9, hi: 0xb9},
	// Block 0x26, offset 0x106
	{value: 0x0000, lo: 0x01},
	{value: 0x812d, lo: 0x86, hi: 0x86},
	// Block 0x27, offset 0x108
	{value: 0x0000, lo: 0x05},
	{value: 0xa000, lo: 0xa5, hi: 0xa5},
	{value: 0x2d21, lo: 0xa6, hi: 0xa6},
	{value: 0x9900, lo: 0xae, hi: 0xae},
	{value: 0x8102, lo: 0xb7, hi: 0xb7},
	{value: 0x8104, lo: 0xb9, hi: 0xba},
	// Block 0x28, offset 0x10e
	{value: 0x0000, lo: 0x01},
	{value: 0x812d, lo: 0x8d, hi: 0x8d},
	// Block 0x29, offset 0x110
	{value: 0x0000, lo: 0x01},
	{value: 0x030f, lo: 0xbc, hi: 0xbc},
	// Block 0x2a, offset 0x112
	{value: 0x0000, lo: 0x01},
	{value: 0xa000, lo: 0x80, hi: 0x92},
	// Block 0x2b, offset 0x114
	{value: 0x0000, lo: 0x01},
	{value: 0xb900, lo: 0xa1, hi: 0xb5},
	// Block 0x2c, offset 0x116
	{value: 0x0000, lo: 0x01},
	{value: 0x9900, lo: 0xa8, hi: 0xbf},
	// Block 0x2d, offset 0x118
	{value: 0x0000, lo: 0x01},
	{value: 0x9900, lo: 0x80, hi: 0x82},
	// Block 0x2e, offset 0x11a
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0x9d, hi: 0x9f},
	// Block 0x2f, offset 0x11c
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0x94, hi: 0x94},
	{value: 0x8104, lo: 0xb4, hi: 0xb4},
	// Block 0x30, offset 0x11f
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0x92, hi: 0x92},
	{value: 0x8132, lo: 0x9d, hi: 0x9d},
	// Block 0x31, offset 0x122
	{value: 0x0000, lo: 0x01},
	{value: 0x8131, lo: 0xa9, hi: 0xa9},
	// Block 0x32, offset 0x124
	{value: 0x0004, lo: 0x02},
	{value: 0x812e, lo: 0xb9, hi: 0xba},
	{value: 0x812d, lo: 0xbb, hi: 0xbb},
	// Block 0x33, offset 0x127
	{value: 0x0000, lo: 0x02},
	{value: 0x8132, lo: 0x97, hi: 0x97},
	{value: 0x812d, lo: 0x98, hi: 0x98},
	// Block 0x34, offset 0x12a
	{value: 0x0000, lo: 0x03},
	{value: 0x8104, lo: 0xa0, hi: 0xa0},
	{value: 0x8132, lo: 0xb5, hi: 0xbc},
	{value: 0x812d, lo: 0xbf, hi: 0xbf},
	// Block 0x35, offset 0x12e
	{value: 0x0000, lo: 0x04},
	{value: 0x8132, lo: 0xb0, hi: 0xb4},
	{value: 0x812d, lo: 0xb5, hi: 0xba},
	{value: 0x8132, lo: 0xbb, hi: 0xbc},
	{value: 0x812d, lo: 0xbd, hi: 0xbd},
	// Block 0x36, offset 0x133
	{value: 0x0000, lo: 0x08},
	{value: 0x2d69, lo: 0x80, hi: 0x80},
	{value: 0x2d71, lo: 0x81, hi: 0x81},
	{value: 0xa000, lo: 0x82, hi: 0x82},
	{value: 0x2d79, lo: 0x83, hi: 0x83},
	{value: 0x8104, lo: 0x84, hi: 0x84},
	{value: 0x8132, lo: 0xab, hi: 0xab},
	{value: 0x812d, lo: 0xac, hi: 0xac},
	{value: 0x8132, lo: 0xad, hi: 0xb3},
	// Block 0x37, offset 0x13c
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0xaa, hi: 0xab},
	// Block 0x38, offset 0x13e
	{value: 0x0000, lo: 0x02},
	{value: 0x8102, lo: 0xa6, hi: 0xa6},
	{value: 0x8104, lo: 0xb2, hi: 0xb3},
	// Block 0x39, offset 0x141
	{value: 0x0000, lo: 0x01},
	{value: 0x8102, lo: 0xb7, hi: 0xb7},
	// Block 0x3a, offset 0x143
	{value: 0x0000, lo: 0x0a},
	{value: 0x8132, lo: 0x90, hi: 0x92},
	{value: 0x8101, lo: 0x94, hi: 0x94},
	{value: 0x812d, lo: 0x95, hi: 0x99},
	{value: 0x8132, lo: 0x9a, hi: 0x9b},
	{value: 0x812d, lo: 0x9c, hi: 0x9f},
	{value: 0x8132, lo: 0xa0, hi: 0xa0},
	{value: 0x8101, lo: 0xa2, hi: 0xa8},
	{value: 0x812d, lo: 0xad, hi: 0xad},
	{value: 0x8132, lo: 0xb4, hi: 0xb4},
	{value: 0x8132, lo: 0xb8, hi: 0xb9},
	// Block 0x3b, offset 0x14e
	{value: 0x0002, lo: 0x0a},
	{value: 0x0043, lo: 0xac, hi: 0xac},
	{value: 0x00d1, lo: 0xad, hi: 0xad},
	{value: 0x0045, lo: 0xae, hi: 0xae},
	{value: 0x0049, lo: 0xb0, hi: 0xb1},
	{value: 0x00e6, lo: 0xb2, hi: 0xb2},
	{value: 0x004f, lo: 0xb3, hi: 0xba},
	{value: 0x005f, lo: 0xbc, hi: 0xbc},
	{value: 0x00ef, lo: 0xbd, hi: 0xbd},
	{value: 0x0061, lo: 0xbe, hi: 0xbe},
	{value: 0x0065, lo: 0xbf, hi: 0xbf},
	// Block 0x3c, offset 0x159
	{value: 0x0000, lo: 0x0d},
	{value: 0x0001, lo: 0x80, hi: 0x8a},
	{value: 0x043b, lo: 0x91, hi: 0x91},
	{value: 0x429e, lo: 0x97, hi: 0x97},
	{value: 0x001d, lo: 0xa4, hi: 0xa4},
	{value: 0x1873, lo: 0xa5, hi: 0xa5},
	{value: 0x1b5f, lo: 0xa6, hi: 0xa6},
	{value: 0x0001, lo: 0xaf, hi: 0xaf},
	{value: 0x2694, lo: 0xb3, hi: 0xb3},
	{value: 0x2801, lo: 0xb4, hi: 0xb4},
	{value: 0x269b, lo: 0xb6, hi: 0xb6},
	{value: 0x280b, lo: 0xb7, hi: 0xb7},
	{value: 0x186d, lo: 0xbc, hi: 0xbc},
	{value: 0x426c, lo: 0xbe, hi: 0xbe},
	// Block 0x3d, offset 0x167
	{value: 0x0002, lo: 0x0d},
	{value: 0x1933, lo: 0x87, hi: 0x87},
	{value: 0x1930, lo: 0x88, hi: 0x88},
	{value: 0x1870, lo: 0x89, hi: 0x89},
	{value: 0x2991, lo: 0x97, hi: 0x97},
	{value: 0x0001, lo: 0x9f, hi: 0x9f},
	{value: 0x0021, lo: 0xb0, hi: 0xb0},
	{value: 0x0093, lo: 0xb1, hi: 0xb1},
	{value: 0x0029, lo: 0xb4, hi: 0xb9},
	{value: 0x0017, lo: 0xba, hi: 0xba},
	{value: 0x0467, lo: 0xbb, hi: 0xbb},
	{value: 0x003b, lo: 0xbc, hi: 0xbc},
	{value: 0x0011, lo: 0xbd, hi: 0xbe},
	{value: 0x009d, lo: 0xbf, hi: 0xbf},
	// Block 0x3e, offset 0x175
	{value: 0x0002, lo: 0x0f},
	{value: 0x0021, lo: 0x80, hi: 0x89},
	{value: 0x0017, lo: 0x8a, hi: 0x8a},
	{value: 0x0467, lo: 0x8b, hi: 0x8b},
	{value: 0x003b, lo: 0x8c, hi: 0x8c},
	{value: 0x0011, lo: 0x8d, hi: 0x8e},
	{value: 0x0083, lo: 0x90, hi: 0x90},
	{value: 0x008b, lo: 0x91, hi: 0x91},
	{value: 0x009f, lo: 0x92, hi: 0x92},
	{value: 0x00b1, lo: 0x93, hi: 0x93},
	{value: 0x0104, lo: 0x94, hi: 0x94},
	{value: 0x0091, lo: 0x95, hi: 0x95},
	{value: 0x0097, lo: 0x96, hi: 0x99},
	{value: 0x00a1, lo: 0x9a, hi: 0x9a},
	{value: 0x00a7, lo: 0x9b, hi: 0x9c},
	{value: 0x199c, lo: 0xa8, hi: 0xa8},
	// Block 0x3f, offset 0x185
	{value: 0x0000, lo: 0x0d},
	{value: 0x8132, lo: 0x90, hi: 0x91},
	{value: 0x8101, lo: 0x92, hi: 0x93},
	{value: 0x8132, lo: 0x94, hi: 0x97},
	{value: 0x8101, lo: 0x98, hi: 0x9a},
	{value: 0x8132, lo: 0x9b, hi: 0x9c},
	{value: 0x8132, lo: 0xa1, hi: 0xa1},
	{value: 0x8101, lo: 0xa5, hi: 0xa6},
	{value: 0x8132, lo: 0xa7, hi: 0xa7},
	{value: 0x812d, lo: 0xa8, hi: 0xa8},
	{value: 0x8132, lo: 0xa9, hi: 0xa9},
	{value: 0x8101, lo: 0xaa, hi: 0xab},
	{value: 0x812d, lo: 0xac, hi: 0xaf},
	{value: 0x8132, lo: 0xb0, hi: 0xb0},
	// Block 0x40, offset 0x193
	{value: 0x0007, lo: 0x06},
	{value: 0x2183, lo: 0x89, hi: 0x89},
	{value: 0xa000, lo: 0x90, hi: 0x90},
	{value: 0xa000, lo: 0x92, hi: 0x92},
	{value: 0xa000, lo: 0x94, hi: 0x94},
	{value: 0x3bbc, lo: 0x9a, hi: 0x9b},
	{value: 0x3bca, lo: 0xae, hi: 0xae},
	// Block 0x41, offset 0x19a
	{value: 0x000e, lo: 0x05},
	{value: 0x3bd1, lo: 0x8d, hi: 0x8e},
	{value: 0x3bd8, lo: 0x8f, hi: 0x8f},
	{value: 0xa000, lo: 0x90, hi: 0x90},
	{value: 0xa000, lo: 0x92, hi: 0x92},
	{value: 0xa000, lo: 0x94, hi: 0x94},
	// Block 0x42, offset 0x1a0
	{value: 0x0173, lo: 0x0e},
	{value: 0xa000, lo: 0x83, hi: 0x83},
	{value: 0x3be6, lo: 0x84, hi: 0x84},
	{value: 0xa000, lo: 0x88, hi: 0x88},
	{value: 0x3bed, lo: 0x89, hi: 0x89},
	{value: 0xa000, lo: 0x8b, hi: 0x8b},
	{value: 0x3bf4, lo: 0x8c, hi: 0x8c},
	{value: 0xa000, lo: 0xa3, hi: 0xa3},
	{value: 0x3bfb, lo: 0xa4, hi: 0xa4},
	{value: 0xa000, lo: 0xa5, hi: 0xa5},
	{value: 0x3c02, lo: 0xa6, hi: 0xa6},
	{value: 0x26a2, lo: 0xac, hi: 0xad},
	{value: 0x26a9, lo: 0xaf, hi: 0xaf},
	{value: 0x281f, lo: 0xb0, hi: 0xb0},
	{value: 0xa000, lo: 0xbc, hi: 0xbc},
	// Block 0x43, offset 0x1af
	{value: 0x0007, lo: 0x03},
	{value: 0x3c6b, lo: 0xa0, hi: 0xa1},
	{value: 0x3c95, lo: 0xa2, hi: 0xa3},
	{value: 0x3cbf, lo: 0xaa, hi: 0xad},
	// Block 0x44, offset 0x1b3
	{value: 0x0004, lo: 0x01},
	{value: 0x048b, lo: 0xa9, hi: 0xaa},
	// Block 0x45, offset 0x1b5
	{value: 0x0002, lo: 0x03},
	{value: 0x0057, lo: 0x80, hi: 0x8f},
	{value: 0x0083, lo: 0x90, hi: 0xa9},
	{value: 0x0021, lo: 0xaa, hi: 0xaa},
	// Block 0x46, offset 0x1b9
	{value: 0x0000, lo: 0x01},
	{value: 0x299e, lo: 0x8c, hi: 0x8c},
	// Block 0x47, offset 0x1bb
	{value: 0x0266, lo: 0x02},
	{value: 0x1b8f, lo: 0xb4, hi: 0xb4},
	{value: 0x192d, lo: 0xb5, hi: 0xb6},
	// Block 0x48, offset 0x1be
	{value: 0x0000, lo: 0x01},
	{value: 0x44e0, lo: 0x9c, hi: 0x9c},
	// Block 0x49, offset 0x1c0
	{value: 0x0000, lo: 0x02},
	{value: 0x0095, lo: 0xbc, hi: 0xbc},
	{value: 0x006d, lo: 0xbd, hi: 0xbd},
	// Block 0x4a, offset 0x1c3
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0xaf, hi: 0xb1},
	// Block 0x4b, offset 0x1c5
	{value: 0x0000, lo: 0x02},
	{value: 0x047f, lo: 0xaf, hi: 0xaf},
	{value: 0x8104, lo: 0xbf, hi: 0xbf},
	// Block 0x4c, offset 0x1c8
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0xa0, hi: 0xbf},
	// Block 0x4d, offset 0x1ca
	{value: 0x0000, lo: 0x01},
	{value: 0x0dc3, lo: 0x9f, hi: 0x9f},
	// Block 0x4e, offset 0x1cc
	{value: 0x0000, lo: 0x01},
	{value: 0x162f, lo: 0xb3, hi: 0xb3},
	// Block 0x4f, offset 0x1ce
	{value: 0x0004, lo: 0x0b},
	{value: 0x1597, lo: 0x80, hi: 0x82},
	{value: 0x15af, lo: 0x83, hi: 0x83},
	{value: 0x15c7, lo: 0x84, hi: 0x85},
	{value: 0x15d7, lo: 0x86, hi: 0x89},
	{value: 0x15eb, lo: 0x8a, hi: 0x8c},
	{value: 0x15ff, lo: 0x8d, hi: 0x8d},
	{value: 0x1607, lo: 0x8e, hi: 0x8e},
	{value: 0x160f, lo: 0x8f, hi: 0x90},
	{value: 0x161b, lo: 0x91, hi: 0x93},
	{value: 0x162b, lo: 0x94, hi: 0x94},
	{value: 0x1633, lo: 0x95, hi: 0x95},
	// Block 0x50, offset 0x1da
	{value: 0x0004, lo: 0x09},
	{value: 0x0001, lo: 0x80, hi: 0x80},
	{value: 0x812c, lo: 0xaa, hi: 0xaa},
	{value: 0x8131, lo: 0xab, hi: 0xab},
	{value: 0x8133, lo: 0xac, hi: 0xac},
	{value: 0x812e, lo: 0xad, hi: 0xad},
	{value: 0x812f, lo: 0xae, hi: 0xae},
	{value: 0x812f, lo: 0xaf, hi: 0xaf},
	{value: 0x04b3, lo: 0xb6, hi: 0xb6},
	{value: 0x0887, lo: 0xb8, hi: 0xba},
	// Block 0x51, offset 0x1e4
	{value: 0x0006, lo: 0x09},
	{value: 0x0313, lo: 0xb1, hi: 0xb1},
	{value: 0x0317, lo: 0xb2, hi: 0xb2},
	{value: 0x4a3e, lo: 0xb3, hi: 0xb3},
	{value: 0x031b, lo: 0xb4, hi: 0xb4},
	{value: 0x4a44, lo: 0xb5, hi: 0xb6},
	{value: 0x031f, lo: 0xb7, hi: 0xb7},
	{value: 0x0323, lo: 0xb8, hi: 0xb8},
	{value: 0x0327, lo: 0xb9, hi: 0xb9},
	{value: 0x4a50, lo: 0xba, hi: 0xbf},
	// Block 0x52, offset 0x1ee
	{value: 0x0000, lo: 0x02},
	{value: 0x8132, lo: 0xaf, hi: 0xaf},
	{value: 0x8132, lo: 0xb4, hi: 0xbd},
	// Block 0x53, offset 0x1f1
	{value: 0x0000, lo: 0x03},
	{value: 0x020f, lo: 0x9c, hi: 0x9c},
	{value: 0x0212, lo: 0x9d, hi: 0x9d},
	{value: 0x8132, lo: 0x9e, hi: 0x9f},
	// Block 0x54, offset 0x1f5
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0xb0, hi: 0xb1},
	// Block 0x55, offset 0x1f7
	{value: 0x0000, lo: 0x01},
	{value: 0x163b, lo: 0xb0, hi: 0xb0},
	// Block 0x56, offset 0x1f9
	{value: 0x000c, lo: 0x01},
	{value: 0x00d7, lo: 0xb8, hi: 0xb9},
	// Block 0x57, offset 0x1fb
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0x86, hi: 0x86},
	// Block 0x58, offset 0x1fd
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0x84, hi: 0x84},
	{value: 0x8132, lo: 0xa0, hi: 0xb1},
	// Block 0x59, offset 0x200
	{value: 0x0000, lo: 0x01},
	{value: 0x812d, lo: 0xab, hi: 0xad},
	// Block 0x5a, offset 0x202
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0x93, hi: 0x93},
	// Block 0x5b, offset 0x204
	{value: 0x0000, lo: 0x01},
	{value: 0x8102, lo: 0xb3, hi: 0xb3},
	// Block 0x5c, offset 0x206
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0x80, hi: 0x80},
	// Block 0x5d, offset 0x208
	{value: 0x0000, lo: 0x05},
	{value: 0x8132, lo: 0xb0, hi: 0xb0},
	{value: 0x8132, lo: 0xb2, hi: 0xb3},
	{value: 0x812d, lo: 0xb4, hi: 0xb4},
	{value: 0x8132, lo: 0xb7, hi: 0xb8},
	{value: 0x8132, lo: 0xbe, hi: 0xbf},
	// Block 0x5e, offset 0x20e
	{value: 0x0000, lo: 0x02},
	{value: 0x8132, lo: 0x81, hi: 0x81},
	{value: 0x8104, lo: 0xb6, hi: 0xb6},
	// Block 0x5f, offset 0x211
	{value: 0x0008, lo: 0x03},
	{value: 0x1637, lo: 0x9c, hi: 0x9d},
	{value: 0x0125, lo: 0x9e, hi: 0x9e},
	{value: 0x1643, lo: 0x9f, hi: 0x9f},
	// Block 0x60, offset 0x215
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0xad, hi: 0xad},
	// Block 0x61, offset 0x217
	{value: 0x0000, lo: 0x06},
	{value: 0xe500, lo: 0x80, hi: 0x80},
	{value: 0xc600, lo: 0x81, hi: 0x9b},
	{value: 0xe500, lo: 0x9c, hi: 0x9c},
	{value: 0xc600, lo: 0x9d, hi: 0xb7},
	{value: 0xe500, lo: 0xb8, hi: 0xb8},
	{value: 0xc600, lo: 0xb9, hi: 0xbf},
	// Block 0x62, offset 0x21e
	{value: 0x0000, lo: 0x05},
	{value: 0xc600, lo: 0x80, hi: 0x93},
	{value: 0xe500, lo: 0x94, hi: 0x94},
	{value: 0xc600, lo: 0x95, hi: 0xaf},
	{value: 0xe500, lo: 0xb0, hi: 0xb0},
	{value: 0xc600, lo: 0xb1, hi: 0xbf},
	// Block 0x63, offset 0x224
	{value: 0x0000, lo: 0x05},
	{value: 0xc600, lo: 0x80, hi: 0x8b},
	{value: 0xe500, lo: 0x8c, hi: 0x8c},
	{value: 0xc600, lo: 0x8d, hi: 0xa7},
	{value: 0xe500, lo: 0xa8, hi: 0xa8},
	{value: 0xc600, lo: 0xa9, hi: 0xbf},
	// Block 0x64, offset 0x22a
	{value: 0x0000, lo: 0x07},
	{value: 0xc600, lo: 0x80, hi: 0x83},
	{value: 0xe500, lo: 0x84, hi: 0x84},
	{value: 0xc600, lo: 0x85, hi: 0x9f},
	{value: 0xe500, lo: 0xa0, hi: 0xa0},
	{value: 0xc600, lo: 0xa1, hi: 0xbb},
	{value: 0xe500, lo: 0xbc, hi: 0xbc},
	{value: 0xc600, lo: 0xbd, hi: 0xbf},
	// Block 0x65, offset 0x232
	{value: 0x0000, lo: 0x05},
	{value: 0xc600, lo: 0x80, hi: 0x97},
	{value: 0xe500, lo: 0x98, hi: 0x98},
	{value: 0xc600, lo: 0x99, hi: 0xb3},
	{value: 0xe500, lo: 0xb4, hi: 0xb4},
	{value: 0xc600, lo: 0xb5, hi: 0xbf},
	// Block 0x66, offset 0x238
	{value: 0x0000, lo: 0x05},
	{value: 0xc600, lo: 0x80, hi: 0x8f},
	{value: 0xe500, lo: 0x90, hi: 0x90},
	{value: 0xc600, lo: 0x91, hi: 0xab},
	{value: 0xe500, lo: 0xac, hi: 0xac},
	{value: 0xc600, lo: 0xad, hi: 0xbf},
	// Block 0x67, offset 0x23e
	{value: 0x0000, lo: 0x05},
	{value: 0xc600, lo: 0x80, hi: 0x87},
	{value: 0xe500, lo: 0x88, hi: 0x88},
	{value: 0xc600, lo: 0x89, hi: 0xa3},
	{value: 0xe500, lo: 0xa4, hi: 0xa4},
	{value: 0xc600, lo: 0xa5, hi: 0xbf},
	// Block 0x68, offset 0x244
	{value: 0x0000, lo: 0x03},
	{value: 0xc600, lo: 0x80, hi: 0x87},
	{value: 0xe500, lo: 0x88, hi: 0x88},
	{value: 0xc600, lo: 0x89, hi: 0xa3},
	// Block 0x69, offset 0x248
	{value: 0x0002, lo: 0x01},
	{value: 0x0003, lo: 0x81, hi: 0xbf},
	// Block 0x6a, offset 0x24a
	{value: 0x0000, lo: 0x01},
	{value: 0x812d, lo: 0xbd, hi: 0xbd},
	// Block 0x6b, offset 0x24c
	{value: 0x0000, lo: 0x01},
	{value: 0x812d, lo: 0xa0, hi: 0xa0},
	// Block 0x6c, offset 0x24e
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0xb6, hi: 0xba},
	// Block 0x6d, offset 0x250
	{value: 0x002c, lo: 0x05},
	{value: 0x812d, lo: 0x8d, hi: 0x8d},
	{value: 0x8132, lo: 0x8f, hi: 0x8f},
	{value: 0x8132, lo: 0xb8, hi: 0xb8},
	{value: 0x8101, lo: 0xb9, hi: 0xba},
	{value: 0x8104, lo: 0xbf, hi: 0xbf},
	// Block 0x6e, offset 0x256
	{value: 0x0000, lo: 0x02},
	{value: 0x8132, lo: 0xa5, hi: 0xa5},
	{value: 0x812d, lo: 0xa6, hi: 0xa6},
	// Block 0x6f, offset 0x259
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0xa4, hi: 0xa7},
	// Block 0x70, offset 0x25b
	{value: 0x0000, lo: 0x05},
	{value: 0x812d, lo: 0x86, hi: 0x87},
	{value: 0x8132, lo: 0x88, hi: 0x8a},
	{value: 0x812d, lo: 0x8b, hi: 0x8b},
	{value: 0x8132, lo: 0x8c, hi: 0x8c},
	{value: 0x812d, lo: 0x8d, hi: 0x90},
	// Block 0x71, offset 0x261
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0x86, hi: 0x86},
	{value: 0x8104, lo: 0xbf, hi: 0xbf},
	// Block 0x72, offset 0x264
	{value: 0x17fe, lo: 0x07},
	{value: 0xa000, lo: 0x99, hi: 0x99},
	{value: 0x423b, lo: 0x9a, hi: 0x9a},
	{value: 0xa000, lo: 0x9b, hi: 0x9b},
	{value: 0x4245, lo: 0x9c, hi: 0x9c},
	{value: 0xa000, lo: 0xa5, hi: 0xa5},
	{value: 0x424f, lo: 0xab, hi: 0xab},
	{value: 0x8104, lo: 0xb9, hi: 0xba},
	// Block 0x73, offset 0x26c
	{value: 0x0000, lo: 0x06},
	{value: 0x8132, lo: 0x80, hi: 0x82},
	{value: 0x9900, lo: 0xa7, hi: 0xa7},
	{value: 0x2d81, lo: 0xae, hi: 0xae},
	{value: 0x2d8b, lo: 0xaf, hi: 0xaf},
	{value: 0xa000, lo: 0xb1, hi: 0xb2},
	{value: 0x8104, lo: 0xb3, hi: 0xb4},
	// Block 0x74, offset 0x273
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0x80, hi: 0x80},
	{value: 0x8102, lo: 0x8a, hi: 0x8a},
	// Block 0x75, offset 0x276
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0xb5, hi: 0xb5},
	{value: 0x8102, lo: 0xb6, hi: 0xb6},
	// Block 0x76, offset 0x279
	{value: 0x0002, lo: 0x01},
	{value: 0x8102, lo: 0xa9, hi: 0xaa},
	// Block 0x77, offset 0x27b
	{value: 0x0000, lo: 0x02},
	{value: 0x8102, lo: 0xbb, hi: 0xbc},
	{value: 0x9900, lo: 0xbe, hi: 0xbe},
	// Block 0x78, offset 0x27e
	{value: 0x0000, lo: 0x07},
	{value: 0xa000, lo: 0x87, hi: 0x87},
	{value: 0x2d95, lo: 0x8b, hi: 0x8b},
	{value: 0x2d9f, lo: 0x8c, hi: 0x8c},
	{value: 0x8104, lo: 0x8d, hi: 0x8d},
	{value: 0x9900, lo: 0x97, hi: 0x97},
	{value: 0x8132, lo: 0xa6, hi: 0xac},
	{value: 0x8132, lo: 0xb0, hi: 0xb4},
	// Block 0x79, offset 0x286
	{value: 0x0000, lo: 0x03},
	{value: 0x8104, lo: 0x82, hi: 0x82},
	{value: 0x8102, lo: 0x86, hi: 0x86},
	{value: 0x8132, lo: 0x9e, hi: 0x9e},
	// Block 0x7a, offset 0x28a
	{value: 0x6b57, lo: 0x06},
	{value: 0x9900, lo: 0xb0, hi: 0xb0},
	{value: 0xa000, lo: 0xb9, hi: 0xb9},
	{value: 0x9900, lo: 0xba, hi: 0xba},
	{value: 0x2db3, lo: 0xbb, hi: 0xbb},
	{value: 0x2da9, lo: 0xbc, hi: 0xbd},
	{value: 0x2dbd, lo: 0xbe, hi: 0xbe},
	// Block 0x7b, offset 0x291
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0x82, hi: 0x82},
	{value: 0x8102, lo: 0x83, hi: 0x83},
	// Block 0x7c, offset 0x294
	{value: 0x0000, lo: 0x05},
	{value: 0x9900, lo: 0xaf, hi: 0xaf},
	{value: 0xa000, lo: 0xb8, hi: 0xb9},
	{value: 0x2dc7, lo: 0xba, hi: 0xba},
	{value: 0x2dd1, lo: 0xbb, hi: 0xbb},
	{value: 0x8104, lo: 0xbf, hi: 0xbf},
	// Block 0x7d, offset 0x29a
	{value: 0x0000, lo: 0x01},
	{value: 0x8102, lo: 0x80, hi: 0x80},
	// Block 0x7e, offset 0x29c
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0xbf, hi: 0xbf},
	// Block 0x7f, offset 0x29e
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0xb6, hi: 0xb6},
	{value: 0x8102, lo: 0xb7, hi: 0xb7},
	// Block 0x80, offset 0x2a1
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0xab, hi: 0xab},
	// Block 0x81, offset 0x2a3
	{value: 0x0000, lo: 0x02},
	{value: 0x8104, lo: 0xb9, hi: 0xb9},
	{value: 0x8102, lo: 0xba, hi: 0xba},
	// Block 0x82, offset 0x2a6
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0xa0, hi: 0xa0},
	// Block 0x83, offset 0x2a8
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0xb4, hi: 0xb4},
	// Block 0x84, offset 0x2aa
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0x87, hi: 0x87},
	// Block 0x85, offset 0x2ac
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0x99, hi: 0x99},
	// Block 0x86, offset 0x2ae
	{value: 0x0000, lo: 0x02},
	{value: 0x8102, lo: 0x82, hi: 0x82},
	{value: 0x8104, lo: 0x84, hi: 0x85},
	// Block 0x87, offset 0x2b1
	{value: 0x0000, lo: 0x01},
	{value: 0x8104, lo: 0x97, hi: 0x97},
	// Block 0x88, offset 0x2b3
	{value: 0x0000, lo: 0x01},
	{value: 0x8101, lo: 0xb0, hi: 0xb4},
	// Block 0x89, offset 0x2b5
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0xb0, hi: 0xb6},
	// Block 0x8a, offset 0x2b7
	{value: 0x0000, lo: 0x01},
	{value: 0x8101, lo: 0x9e, hi: 0x9e},
	// Block 0x8b, offset 0x2b9
	{value: 0x0000, lo: 0x0c},
	{value: 0x45cf, lo: 0x9e, hi: 0x9e},
	{value: 0x45d9, lo: 0x9f, hi: 0x9f},
	{value: 0x460d, lo: 0xa0, hi: 0xa0},
	{value: 0x461b, lo: 0xa1, hi: 0xa1},
	{value: 0x4629, lo: 0xa2, hi: 0xa2},
	{value: 0x4637, lo: 0xa3, hi: 0xa3},
	{value: 0x4645, lo: 0xa4, hi: 0xa4},
	{value: 0x812b, lo: 0xa5, hi: 0xa6},
	{value: 0x8101, lo: 0xa7, hi: 0xa9},
	{value: 0x8130, lo: 0xad, hi: 0xad},
	{value: 0x812b, lo: 0xae, hi: 0xb2},
	{value: 0x812d, lo: 0xbb, hi: 0xbf},
	// Block 0x8c, offset 0x2c6
	{value: 0x0000, lo: 0x09},
	{value: 0x812d, lo: 0x80, hi: 0x82},
	{value: 0x8132, lo: 0x85, hi: 0x89},
	{value: 0x812d, lo: 0x8a, hi: 0x8b},
	{value: 0x8132, lo: 0xaa, hi: 0xad},
	{value: 0x45e3, lo: 0xbb, hi: 0xbb},
	{value: 0x45ed, lo: 0xbc, hi: 0xbc},
	{value: 0x4653, lo: 0xbd, hi: 0xbd},
	{value: 0x466f, lo: 0xbe, hi: 0xbe},
	{value: 0x4661, lo: 0xbf, hi: 0xbf},
	// Block 0x8d, offset 0x2d0
	{value: 0x0000, lo: 0x01},
	{value: 0x467d, lo: 0x80, hi: 0x80},
	// Block 0x8e, offset 0x2d2
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0x82, hi: 0x84},
	// Block 0x8f, offset 0x2d4
	{value: 0x0002, lo: 0x03},
	{value: 0x0043, lo: 0x80, hi: 0x99},
	{value: 0x0083, lo: 0x9a, hi: 0xb3},
	{value: 0x0043, lo: 0xb4, hi: 0xbf},
	// Block 0x90, offset 0x2d8
	{value: 0x0002, lo: 0x04},
	{value: 0x005b, lo: 0x80, hi: 0x8d},
	{value: 0x0083, lo: 0x8e, hi: 0x94},
	{value: 0x0093, lo: 0x96, hi: 0xa7},
	{value: 0x0043, lo: 0xa8, hi: 0xbf},
	// Block 0x91, offset 0x2dd
	{value: 0x0002, lo: 0x0b},
	{value: 0x0073, lo: 0x80, hi: 0x81},
	{value: 0x0083, lo: 0x82, hi: 0x9b},
	{value: 0x0043, lo: 0x9c, hi: 0x9c},
	{value: 0x0047, lo: 0x9e, hi: 0x9f},
	{value: 0x004f, lo: 0xa2, hi: 0xa2},
	{value: 0x0055, lo: 0xa5, hi: 0xa6},
	{value: 0x005d, lo: 0xa9, hi: 0xac},
	{value: 0x0067, lo: 0xae, hi: 0xb5},
	{value: 0x0083, lo: 0xb6, hi: 0xb9},
	{value: 0x008d, lo: 0xbb, hi: 0xbb},
	{value: 0x0091, lo: 0xbd, hi: 0xbf},
	// Block 0x92, offset 0x2e9
	{value: 0x0002, lo: 0x04},
	{value: 0x0097, lo: 0x80, hi: 0x83},
	{value: 0x00a1, lo: 0x85, hi: 0x8f},
	{value: 0x0043, lo: 0x90, hi: 0xa9},
	{value: 0x0083, lo: 0xaa, hi: 0xbf},
	// Block 0x93, offset 0x2ee
	{value: 0x0002, lo: 0x08},
	{value: 0x00af, lo: 0x80, hi: 0x83},
	{value: 0x0043, lo: 0x84, hi: 0x85},
	{value: 0x0049, lo: 0x87, hi: 0x8a},
	{value: 0x0055, lo: 0x8d, hi: 0x94},
	{value: 0x0067, lo: 0x96, hi: 0x9c},
	{value: 0x0083, lo: 0x9e, hi: 0xb7},
	{value: 0x0043, lo: 0xb8, hi: 0xb9},
	{value: 0x0049, lo: 0xbb, hi: 0xbe},
	// Block 0x94, offset 0x2f7
	{value: 0x0002, lo: 0x05},
	{value: 0x0053, lo: 0x80, hi: 0x84},
	{value: 0x005f, lo: 0x86, hi: 0x86},
	{value: 0x0067, lo: 0x8a, hi: 0x90},
	{value: 0x0083, lo: 0x92, hi: 0xab},
	{value: 0x0043, lo: 0xac, hi: 0xbf},
	// Block 0x95, offset 0x2fd
	{value: 0x0002, lo: 0x04},
	{value: 0x006b, lo: 0x80, hi: 0x85},
	{value: 0x0083, lo: 0x86, hi: 0x9f},
	{value: 0x0043, lo: 0xa0, hi: 0xb9},
	{value: 0x0083, lo: 0xba, hi: 0xbf},
	// Block 0x96, offset 0x302
	{value: 0x0002, lo: 0x03},
	{value: 0x008f, lo: 0x80, hi: 0x93},
	{value: 0x0043, lo: 0x94, hi: 0xad},
	{value: 0x0083, lo: 0xae, hi: 0xbf},
	// Block 0x97, offset 0x306
	{value: 0x0002, lo: 0x04},
	{value: 0x00a7, lo: 0x80, hi: 0x87},
	{value: 0x0043, lo: 0x88, hi: 0xa1},
	{value: 0x0083, lo: 0xa2, hi: 0xbb},
	{value: 0x0043, lo: 0xbc, hi: 0xbf},
	// Block 0x98, offset 0x30b
	{value: 0x0002, lo: 0x03},
	{value: 0x004b, lo: 0x80, hi: 0x95},
	{value: 0x0083, lo: 0x96, hi: 0xaf},
	{value: 0x0043, lo: 0xb0, hi: 0xbf},
	// Block 0x99, offset 0x30f
	{value: 0x0003, lo: 0x0f},
	{value: 0x01b8, lo: 0x80, hi: 0x80},
	{value: 0x045f, lo: 0x81, hi: 0x81},
	{value: 0x01bb, lo: 0x82, hi: 0x9a},
	{value: 0x045b, lo: 0x9b, hi: 0x9b},
	{value: 0x01c7, lo: 0x9c, hi: 0x9c},
	{value: 0x01d0, lo: 0x9d, hi: 0x9d},
	{value: 0x01d6, lo: 0x9e, hi: 0x9e},
	{value: 0x01fa, lo: 0x9f, hi: 0x9f},
	{value: 0x01eb, lo: 0xa0, hi: 0xa0},
	{value: 0x01e8, lo: 0xa1, hi: 0xa1},
	{value: 0x0173, lo: 0xa2, hi: 0xb2},
	{value: 0x0188, lo: 0xb3, hi: 0xb3},
	{value: 0x01a6, lo: 0xb4, hi: 0xba},
	{value: 0x045f, lo: 0xbb, hi: 0xbb},
	{value: 0x01bb, lo: 0xbc, hi: 0xbf},
	// Block 0x9a, offset 0x31f
	{value: 0x0003, lo: 0x0d},
	{value: 0x01c7, lo: 0x80, hi: 0x94},
	{value: 0x045b, lo: 0x95, hi: 0x95},
	{value: 0x01c7, lo: 0x96, hi: 0x96},
	{value: 0x01d0, lo: 0x97, hi: 0x97},
	{value: 0x01d6, lo: 0x98, hi: 0x98},
	{value: 0x01fa, lo: 0x99, hi: 0x99},
	{value: 0x01eb, lo: 0x9a, hi: 0x9a},
	{value: 0x01e8, lo: 0x9b, hi: 0x9b},
	{value: 0x0173, lo: 0x9c, hi: 0xac},
	{value: 0x0188, lo: 0xad, hi: 0xad},
	{value: 0x01a6, lo: 0xae, hi: 0xb4},
	{value: 0x045f, lo: 0xb5, hi: 0xb5},
	{value: 0x01bb, lo: 0xb6, hi: 0xbf},
	// Block 0x9b, offset 0x32d
	{value: 0x0003, lo: 0x0d},
	{value: 0x01d9, lo: 0x80, hi: 0x8e},
	{value: 0x045b, lo: 0x8f, hi: 0x8f},
	{value: 0x01c7, lo: 0x90, hi: 0x90},
	{value: 0x01d0, lo: 0x91, hi: 0x91},
	{value: 0x01d6, lo: 0x92, hi: 0x92},
	{value: 0x01fa, lo: 0x93, hi: 0x93},
	{value: 0x01eb, lo: 0x94, hi: 0x94},
	{value: 0x01e8, lo: 0x95, hi: 0x95},
	{value: 0x0173, lo: 0x96, hi: 0xa6},
	{value: 0x0188, lo: 0xa7, hi: 0xa7},
	{value: 0x01a6, lo: 0xa8, hi: 0xae},
	{value: 0x045f, lo: 0xaf, hi: 0xaf},
	{value: 0x01bb, lo: 0xb0, hi: 0xbf},
	// Block 0x9c, offset 0x33b
	{value: 0x0003, lo: 0x0d},
	{value: 0x01eb, lo: 0x80, hi: 0x88},
	{value: 0x045b, lo: 0x89, hi: 0x89},
	{value: 0x01c7, lo: 0x8a, hi: 0x8a},
	{value: 0x01d0, lo: 0x8b, hi: 0x8b},
	{value: 0x01d6, lo: 0x8c, hi: 0x8c},
	{value: 0x01fa, lo: 0x8d, hi: 0x8d},
	{value: 0x01eb, lo: 0x8e, hi: 0x8e},
	{value: 0x01e8, lo: 0x8f, hi: 0x8f},
	{value: 0x0173, lo: 0x90, hi: 0xa0},
	{value: 0x0188, lo: 0xa1, hi: 0xa1},
	{value: 0x01a6, lo: 0xa2, hi: 0xa8},
	{value: 0x045f, lo: 0xa9, hi: 0xa9},
	{value: 0x01bb, lo: 0xaa, hi: 0xbf},
	// Block 0x9d, offset 0x349
	{value: 0x0000, lo: 0x05},
	{value: 0x8132, lo: 0x80, hi: 0x86},
	{value: 0x8132, lo: 0x88, hi: 0x98},
	{value: 0x8132, lo: 0x9b, hi: 0xa1},
	{value: 0x8132, lo: 0xa3, hi: 0xa4},
	{value: 0x8132, lo: 0xa6, hi: 0xaa},
	// Block 0x9e, offset 0x34f
	{value: 0x0000, lo: 0x01},
	{value: 0x8132, lo: 0xac, hi: 0xaf},
	// Block 0x9f, offset 0x351
	{value: 0x0000, lo: 0x01},
	{value: 0x812d, lo: 0x90, hi: 0x96},
	// Block 0xa0, offset 0x353
	{value: 0x0000, lo: 0x02},
	{value: 0x8132, lo: 0x84, hi: 0x89},
	{value: 0x8102, lo: 0x8a, hi: 0x8a},
	// Block 0xa1, offset 0x356
	{value: 0x0002, lo: 0x0a},
	{value: 0x0063, lo: 0x80, hi: 0x89},
	{value: 0x1951, lo: 0x8a, hi: 0x8a},
	{value: 0x1984, lo: 0x8b, hi: 0x8b},
	{value: 0x199f, lo: 0x8c, hi: 0x8c},
	{value: 0x19a5, lo: 0x8d, hi: 0x8d},
	{value: 0x1bc3, lo: 0x8e, hi: 0x8e},
	{value: 0x19b1, lo: 0x8f, hi: 0x8f},
	{value: 0x197b, lo: 0xaa, hi: 0xaa},
	{value: 0x197e, lo: 0xab, hi: 0xab},
	{value: 0x1981, lo: 0xac, hi: 0xac},
	// Block 0xa2, offset 0x361
	{value: 0x0000, lo: 0x01},
	{value: 0x193f, lo: 0x90, hi: 0x90},
	// Block 0xa3, offset 0x363
	{value: 0x0028, lo: 0x09},
	{value: 0x2865, lo: 0x80, hi: 0x80},
	{value: 0x2829, lo: 0x81, hi: 0x81},
	{value: 0x2833, lo: 0x82, hi: 0x82},
	{value: 0x2847, lo: 0x83, hi: 0x84},
	{value: 0x2851, lo: 0x85, hi: 0x86},
	{value: 0x283d, lo: 0x87, hi: 0x87},
	{value: 0x285b, lo: 0x88, hi: 0x88},
	{value: 0x0b6f, lo: 0x90, hi: 0x90},
	{value: 0x08e7, lo: 0x91, hi: 0x91},
}

// recompMap: 7520 bytes (entries only)
var recompMap map[uint32]rune
var recompMapOnce sync.Once

const recompMapPacked = "" +
	"\x00A\x03\x00\x00\x00\x00\xc0" + // 0x00410300: 0x000000C0
	"\x00A\x03\x01\x00\x00\x00\xc1" + // 0x00410301: 0x000000C1
	"\x00A\x03\x02\x00\x00\x00\xc2" + // 0x00410302: 0x000000C2
	"\x00A\x03\x03\x00\x00\x00\xc3" + // 0x00410303: 0x000000C3
	"\x00A\x03\b\x00\x00\x00\xc4" + // 0x00410308: 0x000000C4
	"\x00A\x03\n\x00\x00\x00\xc5" + // 0x0041030A: 0x000000C5
	"\x00C\x03'\x00\x00\x00\xc7" + // 0x00430327: 0x000000C7
	"\x00E\x03\x00\x00\x00\x00\xc8" + // 0x00450300: 0x000000C8
	"\x00E\x03\x01\x00\x00\x00\xc9" + // 0x00450301: 0x000000C9
	"\x00E\x03\x02\x00\x00\x00\xca" + // 0x00450302: 0x000000CA
	"\x00E\x03\b\x00\x00\x00\xcb" + // 0x00450308: 0x000000CB
	"\x00I\x03\x00\x00\x00\x00\xcc" + // 0x00490300: 0x000000CC
	"\x00I\x03\x01\x00\x00\x00\xcd" + // 0x00490301: 0x000000CD
	"\x00I\x03\x02\x00\x00\x00\xce" + // 0x00490302: 0x000000CE
	"\x00I\x03\b\x00\x00\x00\xcf" + // 0x00490308: 0x000000CF
	"\x00N\x03\x03\x00\x00\x00\xd1" + // 0x004E0303: 0x000000D1
	"\x00O\x03\x00\x00\x00\x00\xd2" + // 0x004F0300: 0x000000D2
	"\x00O\x03\x01\x00\x00\x00\xd3" + // 0x004F0301: 0x000000D3
	"\x00O\x03\x02\x00\x00\x00\xd4" + // 0x004F0302: 0x000000D4
	"\x00O\x03\x03\x00\x00\x00\xd5" + // 0x004F0303: 0x000000D5
	"\x00O\x03\b\x00\x00\x00\xd6" + // 0x004F0308: 0x000000D6
	"\x00U\x03\x00\x00\x00\x00\xd9" + // 0x00550300: 0x000000D9
	"\x00U\x03\x01\x00\x00\x00\xda" + // 0x00550301: 0x000000DA
	"\x00U\x03\x02\x00\x00\x00\xdb" + // 0x00550302: 0x000000DB
	"\x00U\x03\b\x00\x00\x00\xdc" + // 0x00550308: 0x000000DC
	"\x00Y\x03\x01\x00\x00\x00\xdd" + // 0x00590301: 0x000000DD
	"\x00a\x03\x00\x00\x00\x00\xe0" + // 0x00610300: 0x000000E0
	"\x00a\x03\x01\x00\x00\x00\xe1" + // 0x00610301: 0x000000E1
	"\x00a\x03\x02\x00\x00\x00\xe2" + // 0x00610302: 0x000000E2
	"\x00a\x03\x03\x00\x00\x00\xe3" + // 0x00610303: 0x000000E3
	"\x00a\x03\b\x00\x00\x00\xe4" + // 0x00610308: 0x000000E4
	"\x00a\x03\n\x00\x00\x00\xe5" + // 0x0061030A: 0x000000E5
	"\x00c\x03'\x00\x00\x00\xe7" + // 0x00630327: 0x000000E7
	"\x00e\x03\x00\x00\x00\x00\xe8" + // 0x00650300: 0x000000E8
	"\x00e\x03\x01\x00\x00\x00\xe9" + // 0x00650301: 0x000000E9
	"\x00e\x03\x02\x00\x00\x00\xea" + // 0x00650302: 0x000000EA
	"\x00e\x03\b\x00\x00\x00\xeb" + // 0x00650308: 0x000000EB
	"\x00i\x03\x00\x00\x00\x00\xec" + // 0x00690300: 0x000000EC
	"\x00i\x03\x01\x00\x00\x00\xed" + // 0x00690301: 0x000000ED
	"\x00i\x03\x02\x00\x00\x00\xee" + // 0x00690302: 0x000000EE
	"\x00i\x03\b\x00\x00\x00\xef" + // 0x00690308: 0x000000EF
	"\x00n\x03\x03\x00\x00\x00\xf1" + // 0x006E0303: 0x000000F1
	"\x00o\x03\x00\x00\x00\x00\xf2" + // 0x006F0300: 0x000000F2
	"\x00o\x03\x01\x00\x00\x00\xf3" + // 0x006F0301: 0x000000F3
	"\x00o\x03\x02\x00\x00\x00\xf4" + // 0x006F0302: 0x000000F4
	"\x00o\x03\x03\x00\x00\x00\xf5" + // 0x006F0303: 0x000000F5
	"\x00o\x03\b\x00\x00\x00\xf6" + // 0x006F0308: 0x000000F6
	"\x00u\x03\x00\x00\x00\x00\xf9" + // 0x00750300: 0x000000F9
	"\x00u\x03\x01\x00\x00\x00\xfa" + // 0x00750301: 0x000000FA
	"\x00u\x03\x02\x00\x00\x00\xfb" + // 0x00750302: 0x000000FB
	"\x00u\x03\b\x00\x00\x00\xfc" + // 0x00750308: 0x000000FC
	"\x00y\x03\x01\x00\x00\x00\xfd" + // 0x00790301: 0x000000FD
	"\x00y\x03\b\x00\x00\x00\xff" + // 0x00790308: 0x000000FF
	"\x00A\x03\x04\x00\x00\x01\x00" + // 0x00410304: 0x00000100
	"\x00a\x03\x04\x00\x00\x01\x01" + // 0x00610304: 0x00000101
	"\x00A\x03\x06\x00\x00\x01\x02" + // 0x00410306: 0x00000102
	"\x00a\x03\x06\x00\x00\x01\x03" + // 0x00610306: 0x00000103
	"\x00A\x03(\x00\x00\x01\x04" + // 0x00410328: 0x00000104
	"\x00a\x03(\x00\x00\x01\x05" + // 0x00610328: 0x00000105
	"\x00C\x03\x01\x00\x00\x01\x06" + // 0x00430301: 0x00000106
	"\x00c\x03\x01\x00\x00\x01\a" + // 0x00630301: 0x00000107
	"\x00C\x03\x02\x00\x00\x01\b" + // 0x00430302: 0x00000108
	"\x00c\x03\x02\x00\x00\x01\t" + // 0x00630302: 0x00000109
	"\x00C\x03\a\x00\x00\x01\n" + // 0x00430307: 0x0000010A
	"\x00c\x03\a\x00\x00\x01\v" + // 0x00630307: 0x0000010B
	"\x00C\x03\f\x00\x00\x01\f" + // 0x0043030C: 0x0000010C
	"\x00c\x03\f\x00\x00\x01\r" + // 0x0063030C: 0x0000010D
	"\x00D\x03\f\x00\x00\x01\x0e" + // 0x0044030C: 0x0000010E
	"\x00d\x03\f\x00\x00\x01\x0f" + // 0x0064030C: 0x0000010F
	"\x00E\x03\x04\x00\x00\x01\x12" + // 0x00450304: 0x00000112
	"\x00e\x03\x04\x00\x00\x01\x13" + // 0x00650304: 0x00000113
	"\x00E\x03\x06\x00\x00\x01\x14" + // 0x00450306: 0x00000114
	"\x00e\x03\x06\x00\x00\x01\x15" + // 0x00650306: 0x00000115
	"\x00E\x03\a\x00\x00\x01\x16" + // 0x00450307: 0x00000116
	"\x00e\x03\a\x00\x00\x01\x17" + // 0x00650307: 0x00000117
	"\x00E\x03(\x00\x00\x01\x18" + // 0x00450328: 0x00000118
	"\x00e\x03(\x00\x00\x01\x19" + // 0x00650328: 0x00000119
	"\x00E\x03\f\x00\x00\x01\x1a" + // 0x0045030C: 0x0000011A
	"\x00e\x03\f\x00\x00\x01\x1b" + // 0x0065030C: 0x0000011B
	"\x00G\x03\x02\x00\x00\x01\x1c" + // 0x00470302: 0x0000011C
	"\x00g\x03\x02\x00\x00\x01\x1d" + // 0x00670302: 0x0000011D
	"\x00G\x03\x06\x00\x00\x01\x1e" + // 0x00470306: 0x0000011E
	"\x00g\x03\x06\x00\x00\x01\x1f" + // 0x00670306: 0x0000011F
	"\x00G\x03\a\x00\x00\x01 " + // 0x00470307: 0x00000120
	"\x00g\x03\a\x00\x00\x01!" + // 0x00670307: 0x00000121
	"\x00G\x03'\x00\x00\x01\"" + // 0x00470327: 0x00000122
	"\x00g\x03'\x00\x00\x01#" + // 0x00670327: 0x00000123
	"\x00H\x03\x02\x00\x00\x01$" + // 0x00480302: 0x00000124
	"\x00h\x03\x02\x00\x00\x01%" + // 0x00680302: 0x00000125
	"\x00I\x03\x03\x00\x00\x01(" + // 0x00490303: 0x00000128
	"\x00i\x03\x03\x00\x00\x01)" + // 0x00690303: 0x00000129
	"\x00I\x03\x04\x00\x00\x01*" + // 0x00490304: 0x0000012A
	"\x00i\x03\x04\x00\x00\x01+" + // 0x00690304: 0x0000012B
	"\x00I\x03\x06\x00\x00\x01," + // 0x00490306: 0x0000012C
	"\x00i\x03\x06\x00\x00\x01-" + // 0x00690306: 0x0000012D
	"\x00I\x03(\x00\x00\x01." + // 0x00490328: 0x0000012E
	"\x00i\x03(\x00\x00\x01/" + // 0x00690328: 0x0000012F
	"\x00I\x03\a\x00\x00\x010" + // 0x00490307: 0x00000130
	"\x00J\x03\x02\x00\x00\x014" + // 0x004A0302: 0x00000134
	"\x00j\x03\x02\x00\x00\x015" + // 0x006A0302: 0x00000135
	"\x00K\x03'\x00\x00\x016" + // 0x004B0327: 0x00000136
	"\x00k\x03'\x00\x00\x017" + // 0x006B0327: 0x00000137
	"\x00L\x03\x01\x00\x00\x019" + // 0x004C0301: 0x00000139
	"\x00l\x03\x01\x00\x00\x01:" + // 0x006C0301: 0x0000013A
	"\x00L\x03'\x00\x00\x01;" + // 0x004C0327: 0x0000013B
	"\x00l\x03'\x00\x00\x01<" + // 0x006C0327: 0x0000013C
	"\x00L\x03\f\x00\x00\x01=" + // 0x004C030C: 0x0000013D
	"\x00l\x03\f\x00\x00\x01>" + // 0x006C030C: 0x0000013E
	"\x00N\x03\x01\x00\x00\x01C" + // 0x004E0301: 0x00000143
	"\x00n\x03\x01\x00\x00\x01D" + // 0x006E0301: 0x00000144
	"\x00N\x03'\x00\x00\x01E" + // 0x004E0327: 0x00000145
	"\x00n\x03'\x00\x00\x01F" + // 0x006E0327: 0x00000146
	"\x00N\x03\f\x00\x00\x01G" + // 0x004E030C: 0x00000147
	"\x00n\x03\f\x00\x00\x01H" + // 0x006E030C: 0x00000148
	"\x00O\x03\x04\x00\x00\x01L" + // 0x004F0304: 0x0000014C
	"\x00o\x03\x04\x00\x00\x01M" + // 0x006F0304: 0x0000014D
	"\x00O\x03\x06\x00\x00\x01N" + // 0x004F0306: 0x0000014E
	"\x00o\x03\x06\x00\x00\x01O" + // 0x006F0306: 0x0000014F
	"\x00O\x03\v\x00\x00\x01P" + // 0x004F030B: 0x00000150
	"\x00o\x03\v\x00\x00\x01Q" + // 0x006F030B: 0x00000151
	"\x00R\x03\x01\x00\x00\x01T" + // 0x00520301: 0x00000154
	"\x00r\x03\x01\x00\x00\x01U" + // 0x00720301: 0x00000155
	"\x00R\x03'\x00\x00\x01V" + // 0x00520327: 0x00000156
	"\x00r\x03'\x00\x00\x01W" + // 0x00720327: 0x00000157
	"\x00R\x03\f\x00\x00\x01X" + // 0x0052030C: 0x00000158
	"\x00r\x03\f\x00\x00\x01Y" + // 0x0072030C: 0x00000159
	"\x00S\x03\x01\x00\x00\x01Z" + // 0x00530301: 0x0000015A
	"\x00s\x03\x01\x00\x00\x01[" + // 0x00730301: 0x0000015B
	"\x00S\x03\x02\x00\x00\x01\\" + // 0x00530302: 0x0000015C
	"\x00s\x03\x02\x00\x00\x01]" + // 0x00730302: 0x0000015D
	"\x00S\x03'\x00\x00\x01^" + // 0x00530327: 0x0000015E
	"\x00s\x03'\x00\x00\x01_" + // 0x00730327: 0x0000015F
	"\x00S\x03\f\x00\x00\x01`" + // 0x0053030C: 0x00000160
	"\x00s\x03\f\x00\x00\x01a" + // 0x0073030C: 0x00000161
	"\x00T\x03'\x00\x00\x01b" + // 0x00540327: 0x00000162
	"\x00t\x03'\x00\x00\x01c" + // 0x00740327: 0x00000163
	"\x00T\x03\f\x00\x00\x01d" + // 0x0054030C: 0x00000164
	"\x00t\x03\f\x00\x00\x01e" + // 0x0074030C: 0x00000165
	"\x00U\x03\x03\x00\x00\x01h" + // 0x00550303: 0x00000168
	"\x00u\x03\x03\x00\x00\x01i" + // 0x00750303: 0x00000169
	"\x00U\x03\x04\x00\x00\x01j" + // 0x00550304: 0x0000016A
	"\x00u\x03\x04\x00\x00\x01k" + // 0x00750304: 0x0000016B
	"\x00U\x03\x06\x00\x00\x01l" + // 0x00550306: 0x0000016C
	"\x00u\x03\x06\x00\x00\x01m" + // 0x00750306: 0x0000016D
	"\x00U\x03\n\x00\x00\x01n" + // 0x0055030A: 0x0000016E
	"\x00u\x03\n\x00\x00\x01o" + // 0x0075030A: 0x0000016F
	"\x00U\x03\v\x00\x00\x01p" + // 0x0055030B: 0x00000170
	"\x00u\x03\v\x00\x00\x01q" + // 0x0075030B: 0x00000171
	"\x00U\x03(\x00\x00\x01r" + // 0x00550328: 0x00000172
	"\x00u\x03(\x00\x00\x01s" + // 0x00750328: 0x00000173
	"\x00W\x03\x02\x00\x00\x01t" + // 0x00570302: 0x00000174
	"\x00w\x03\x02\x00\x00\x01u" + // 0x00770302: 0x00000175
	"\x00Y\x03\x02\x00\x00\x01v" + // 0x00590302: 0x00000176
	"\x00y\x03\x02\x00\x00\x01w" + // 0x00790302: 0x00000177
	"\x00Y\x03\b\x00\x00\x01x" + // 0x00590308: 0x00000178
	"\x00Z\x03\x01\x00\x00\x01y" + // 0x005A0301: 0x00000179
	"\x00z\x03\x01\x00\x00\x01z" + // 0x007A0301: 0x0000017A
	"\x00Z\x03\a\x00\x00\x01{" + // 0x005A0307: 0x0000017B
	"\x00z\x03\a\x00\x00\x01|" + // 0x007A0307: 0x0000017C
	"\x00Z\x03\f\x00\x00\x01}" + // 0x005A030C: 0x0000017D
	"\x00z\x03\f\x00\x00\x01~" + // 0x007A030C: 0x0000017E
	"\x00O\x03\x1b\x00\x00\x01\xa0" + // 0x004F031B: 0x000001A0
	"\x00o\x03\x1b\x00\x00\x01\xa1" + // 0x006F031B: 0x000001A1
	"\x00U\x03\x1b\x00\x00\x01\xaf" + // 0x0055031B: 0x000001AF
	"\x00u\x03\x1b\x00\x00\x01\xb0" + // 0x0075031B: 0x000001B0
	"\x00A\x03\f\x00\x00\x01\xcd" + // 0x0041030C: 0x000001CD
	"\x00a\x03\f\x00\x00\x01\xce" + // 0x0061030C: 0x000001CE
	"\x00I\x03\f\x00\x00\x01\xcf" + // 0x0049030C: 0x000001CF
	"\x00i\x03\f\x00\x00\x01\xd0" + // 0x0069030C: 0x000001D0
	"\x00O\x03\f\x00\x00\x01\xd1" + // 0x004F030C: 0x000001D1
	"\x00o\x03\f\x00\x00\x01\xd2" + // 0x006F030C: 0x000001D2
	"\x00U\x03\f\x00\x00\x01\xd3" + // 0x0055030C: 0x000001D3
	"\x00u\x03\f\x00\x00\x01\xd4" + // 0x0075030C: 0x000001D4
	"\x00\xdc\x03\x04\x00\x00\x01\xd5" + // 0x00DC0304: 0x000001D5
	"\x00\xfc\x03\x04\x00\x00\x01\xd6" + // 0x00FC0304: 0x000001D6
	"\x00\xdc\x03\x01\x00\x00\x01\xd7" + // 0x00DC0301: 0x000001D7
	"\x00\xfc\x03\x01\x00\x00\x01\xd8" + // 0x00FC0301: 0x000001D8
	"\x00\xdc\x03\f\x00\x00\x01\xd9" + // 0x00DC030C: 0x000001D9
	"\x00\xfc\x03\f\x00\x00\x01\xda" + // 0x00FC030C: 0x000001DA
	"\x00\xdc\x03\x00\x00\x00\x01\xdb" + // 0x00DC0300: 0x000001DB
	"\x00\xfc\x03\x00\x00\x00\x01\xdc" + // 0x00FC0300: 0x000001DC
	"\x00\xc4\x03\x04\x00\x00\x01\xde" + // 0x00C40304: 0x000001DE
	"\x00\xe4\x03\x04\x00\x00\x01\xdf" + // 0x00E40304: 0x000001DF
	"\x02&\x03\x04\x00\x00\x01\xe0" + // 0x02260304: 0x000001E0
	"\x02'\x03\x04\x00\x00\x01\xe1" + // 0x02270304: 0x000001E1
	"\x00\xc6\x03\x04\x00\x00\x01\xe2" + // 0x00C60304: 0x000001E2
	"\x00\xe6\x03\x04\x00\x00\x01\xe3" + // 0x00E60304: 0x000001E3
	"\x00G\x03\f\x00\x00\x01\xe6" + // 0x0047030C: 0x000001E6
	"\x00g\x03\f\x00\x00\x01\xe7" + // 0x0067030C: 0x000001E7
	"\x00K\x03\f\x00\x00\x01\xe8" + // 0x004B030C: 0x000001E8
	"\x00k\x03\f\x00\x00\x01\xe9" + // 0x006B030C: 0x000001E9
	"\x00O\x03(\x00\x00\x01\xea" + // 0x004F0328: 0x000001EA
	"\x00o\x03(\x00\x00\x01\xeb" + // 0x006F0328: 0x000001EB
	"\x01\xea\x03\x04\x00\x00\x01\xec" + // 0x01EA0304: 0x000001EC
	"\x01\xeb\x03\x04\x00\x00\x01\xed" + // 0x01EB0304: 0x000001ED
	"\x01\xb7\x03\f\x00\x00\x01\xee" + // 0x01B7030C: 0x000001EE
	"\x02\x92\x03\f\x00\x00\x01\xef" + // 0x0292030C: 0x000001EF
	"\x00j\x03\f\x00\x00\x01\xf0" + // 0x006A030C: 0x000001F0
	"\x00G\x03\x01\x00\x00\x01\xf4" + // 0x00470301: 0x000001F4
	"\x00g\x03\x01\x00\x00\x01\xf5" + // 0x00670301: 0x000001F5
	"\x00N\x03\x00\x00\x00\x01\xf8" + // 0x004E0300: 0x000001F8
	"\x00n\x03\x00\x00\x00\x01\xf9" + // 0x006E0300: 0x000001F9
	"\x00\xc5\x03\x01\x00\x00\x01\xfa" + // 0x00C50301: 0x000001FA
	"\x00\xe5\x03\x01\x00\x00\x01\xfb" + // 0x00E50301: 0x000001FB
	"\x00\xc6\x03\x01\x00\x00\x01\xfc" + // 0x00C60301: 0x000001FC
	"\x00\xe6\x03\x01\x00\x00\x01\xfd" + // 0x00E60301: 0x000001FD
	"\x00\xd8\x03\x01\x00\x00\x01\xfe" + // 0x00D80301: 0x000001FE
	"\x00\xf8\x03\x01\x00\x00\x01\xff" + // 0x00F80301: 0x000001FF
	"\x00A\x03\x0f\x00\x00\x02\x00" + // 0x0041030F: 0x00000200
	"\x00a\x03\x0f\x00\x00\x02\x01" + // 0x0061030F: 0x00000201
	"\x00A\x03\x11\x00\x00\x02\x02" + // 0x00410311: 0x00000202
	"\x00a\x03\x11\x00\x00\x02\x03" + // 0x00610311: 0x00000203
	"\x00E\x03\x0f\x00\x00\x02\x04" + // 0x0045030F: 0x00000204
	"\x00e\x03\x0f\x00\x00\x02\x05" + // 0x0065030F: 0x00000205
	"\x00E\x03\x11\x00\x00\x02\x06" + // 0x00450311: 0x00000206
	"\x00e\x03\x11\x00\x00\x02\a" + // 0x00650311: 0x00000207
	"\x00I\x03\x0f\x00\x00\x02\b" + // 0x0049030F: 0x00000208
	"\x00i\x03\x0f\x00\x00\x02\t" + // 0x0069030F: 0x00000209
	"\x00I\x03\x11\x00\x00\x02\n" + // 0x00490311: 0x0000020A
	"\x00i\x03\x11\x00\x00\x02\v" + // 0x00690311: 0x0000020B
	"\x00O\x03\x0f\x00\x00\x02\f" + // 0x004F030F: 0x0000020C
	"\x00o\x03\x0f\x00\x00\x02\r" + // 0x006F030F: 0x0000020D
	"\x00O\x03\x11\x00\x00\x02\x0e" + // 0x004F0311: 0x0000020E
	"\x00o\x03\x11\x00\x00\x02\x0f" + // 0x006F0311: 0x0000020F
	"\x00R\x03\x0f\x00\x00\x02\x10" + // 0x0052030F: 0x00000210
	"\x00r\x03\x0f\x00\x00\x02\x11" + // 0x0072030F: 0x00000211
	"\x00R\x03\x11\x00\x00\x02\x12" + // 0x00520311: 0x00000212
	"\x00r\x03\x11\x00\x00\x02\x13" + // 0x00720311: 0x00000213
	"\x00U\x03\x0f\x00\x00\x02\x14" + // 0x0055030F: 0x00000214
	"\x00u\x03\x0f\x00\x00\x02\x15" + // 0x0075030F: 0x00000215
	"\x00U\x03\x11\x00\x00\x02\x16" + // 0x00550311: 0x00000216
	"\x00u\x03\x11\x00\x00\x02\x17" + // 0x00750311: 0x00000217
	"\x00S\x03&\x00\x00\x02\x18" + // 0x00530326: 0x00000218
	"\x00s\x03&\x00\x00\x02\x19" + // 0x00730326: 0x00000219
	"\x00T\x03&\x00\x00\x02\x1a" + // 0x00540326: 0x0000021A
	"\x00t\x03&\x00\x00\x02\x1b" + // 0x00740326: 0x0000021B
	"\x00H\x03\f\x00\x00\x02\x1e" + // 0x0048030C: 0x0000021E
	"\x00h\x03\f\x00\x00\x02\x1f" + // 0x0068030C: 0x0000021F
	"\x00A\x03\a\x00\x00\x02&" + // 0x00410307: 0x00000226
	"\x00a\x03\a\x00\x00\x02'" + // 0x00610307: 0x00000227
	"\x00E\x03'\x00\x00\x02(" + // 0x00450327: 0x00000228
	"\x00e\x03'\x00\x00\x02)" + // 0x00650327: 0x00000229
	"\x00\xd6\x03\x04\x00\x00\x02*" + // 0x00D60304: 0x0000022A
	"\x00\xf6\x03\x04\x00\x00\x02+" + // 0x00F60304: 0x0000022B
	"\x00\xd5\x03\x04\x00\x00\x02," + // 0x00D50304: 0x0000022C
	"\x00\xf5\x03\x04\x00\x00\x02-" + // 0x00F50304: 0x0000022D
	"\x00O\x03\a\x00\x00\x02." + // 0x004F0307: 0x0000022E
	"\x00o\x03\a\x00\x00\x02/" + // 0x006F0307: 0x0000022F
	"\x02.\x03\x04\x00\x00\x020" + // 0x022E0304: 0x00000230
	"\x02/\x03\x04\x00\x00\x021" + // 0x022F0304: 0x00000231
	"\x00Y\x03\x04\x00\x00\x022" + // 0x00590304: 0x00000232
	"\x00y\x03\x04\x00\x00\x023" + // 0x00790304: 0x00000233
	"\x00\xa8\x03\x01\x00\x00\x03\x85" + // 0x00A80301: 0x00000385
	"\x03\x91\x03\x01\x00\x00\x03\x86" + // 0x03910301: 0x00000386
	"\x03\x95\x03\x01\x00\x00\x03\x88" + // 0x03950301: 0x00000388
	"\x03\x97\x03\x01\x00\x00\x03\x89" + // 0x03970301: 0x00000389
	"\x03\x99\x03\x01\x00\x00\x03\x8a" + // 0x03990301: 0x0000038A
	"\x03\x9f\x03\x01\x00\x00\x03\x8c" + // 0x039F0301: 0x0000038C
	"\x03\xa5\x03\x01\x00\x00\x03\x8e" + // 0x03A50301: 0x0000038E
	"\x03\xa9\x03\x01\x00\x00\x03\x8f" + // 0x03A90301: 0x0000038F
	"\x03\xca\x03\x01\x00\x00\x03\x90" + // 0x03CA0301: 0x00000390
	"\x03\x99\x03\b\x00\x00\x03\xaa" + // 0x03990308: 0x000003AA
	"\x03\xa5\x03\b\x00\x00\x03\xab" + // 0x03A50308: 0x000003AB
	"\x03\xb1\x03\x01\x00\x00\x03\xac" + // 0x03B10301: 0x000003AC
	"\x03\xb5\x03\x01\x00\x00\x03\xad" + // 0x03B50301: 0x000003AD
	"\x03\xb7\x03\x01\x00\x00\x03\xae" + // 0x03B70301: 0x000003AE
	"\x03\xb9\x03\x01\x00\x00\x03\xaf" + // 0x03B90301: 0x000003AF
	"\x03\xcb\x03\x01\x00\x00\x03\xb0" + // 0x03CB0301: 0x000003B0
	"\x03\xb9\x03\b\x00\x00\x03\xca" + // 0x03B90308: 0x000003CA
	"\x03\xc5\x03\b\x00\x00\x03\xcb" + // 0x03C50308: 0x000003CB
	"\x03\xbf\x03\x01\x00\x00\x03\xcc" + // 0x03BF0301: 0x000003CC
	"\x03\xc5\x03\x01\x00\x00\x03\xcd" + // 0x03C50301: 0x000003CD
	"\x03\xc9\x03\x01\x00\x00\x03\xce" + // 0x03C90301: 0x000003CE
	"\x03\xd2\x03\x01\x00\x00\x03\xd3" + // 0x03D20301: 0x000003D3
	"\x03\xd2\x03\b\x00\x00\x03\xd4" + // 0x03D20308: 0x000003D4
	"\x04\x15\x03\x00\x00\x00\x04\x00" + // 0x04150300: 0x00000400
	"\x04\x15\x03\b\x00\x00\x04\x01" + // 0x04150308: 0x00000401
	"\x04\x13\x03\x01\x00\x00\x04\x03" + // 0x04130301: 0x00000403
	"\x04\x06\x03\b\x00\x00\x04\a" + // 0x04060308: 0x00000407
	"\x04\x1a\x03\x01\x00\x00\x04\f" + // 0x041A0301: 0x0000040C
	"\x04\x18\x03\x00\x00\x00\x04\r" + // 0x04180300: 0x0000040D
	"\x04#\x03\x06\x00\x00\x04\x0e" + // 0x04230306: 0x0000040E
	"\x04\x18\x03\x06\x00\x00\x04\x19" + // 0x04180306: 0x00000419
	"\x048\x03\x06\x00\x00\x049" + // 0x04380306: 0x00000439
	"\x045\x03\x00\x00\x00\x04P" + // 0x04350300: 0x00000450
	"\x045\x03\b\x00\x00\x04Q" + // 0x04350308: 0x00000451
	"\x043\x03\x01\x00\x00\x04S" + // 0x04330301: 0x00000453
	"\x04V\x03\b\x00\x00\x04W" + // 0x04560308: 0x00000457
	"\x04:\x03\x01\x00\x00\x04\\" + // 0x043A0301: 0x0000045C
	"\x048\x03\x00\x00\x00\x04]" + // 0x04380300: 0x0000045D
	"\x04C\x03\x06\x00\x00\x04^" + // 0x04430306: 0x0000045E
	"\x04t\x03\x0f\x00\x00\x04v" + // 0x0474030F: 0x00000476
	"\x04u\x03\x0f\x00\x00\x04w" + // 0x0475030F: 0x00000477
	"\x04\x16\x03\x06\x00\x00\x04\xc1" + // 0x04160306: 0x000004C1
	"\x046\x03\x06\x00\x00\x04\xc2" + // 0x04360306: 0x000004C2
	"\x04\x10\x03\x06\x00\x00\x04\xd0" + // 0x04100306: 0x000004D0
	"\x040\x03\x06\x00\x00\x04\xd1" + // 0x04300306: 0x000004D1
	"\x04\x10\x03\b\x00\x00\x04\xd2" + // 0x04100308: 0x000004D2
	"\x040\x03\b\x00\x00\x04\xd3" + // 0x04300308: 0x000004D3
	"\x04\x15\x03\x06\x00\x00\x04\xd6" + // 0x04150306: 0x000004D6
	"\x045\x03\x06\x00\x00\x04\xd7" + // 0x04350306: 0x000004D7
	"\x04\xd8\x03\b\x00\x00\x04\xda" + // 0x04D80308: 0x000004DA
	"\x04\xd9\x03\b\x00\x00\x04\xdb" + // 0x04D90308: 0x000004DB
	"\x04\x16\x03\b\x00\x00\x04\xdc" + // 0x04160308: 0x000004DC
	"\x046\x03\b\x00\x00\x04\xdd" + // 0x04360308: 0x000004DD
	"\x04\x17\x03\b\x00\x00\x04\xde" + // 0x04170308: 0x000004DE
	"\x047\x03\b\x00\x00\x04\xdf" + // 0x04370308: 0x000004DF
	"\x04\x18\x03\x04\x00\x00\x04\xe2" + // 0x04180304: 0x000004E2
	"\x048\x03\x04\x00\x00\x04\xe3" + // 0x04380304: 0x000004E3
	"\x04\x18\x03\b\x00\x00\x04\xe4" + // 0x04180308: 0x000004E4
	"\x048\x03\b\x00\x00\x04\xe5" + // 0x04380308: 0x000004E5
	"\x04\x1e\x03\b\x00\x00\x04\xe6" + // 0x041E0308: 0x000004E6
	"\x04>\x03\b\x00\x00\x04\xe7" + // 0x043E0308: 0x000004E7
	"\x04\xe8\x03\b\x00\x00\x04\xea" + // 0x04E80308: 0x000004EA
	"\x04\xe9\x03\b\x00\x00\x04\xeb" + // 0x04E90308: 0x000004EB
	"\x04-\x03\b\x00\x00\x04\xec" + // 0x042D0308: 0x000004EC
	"\x04M\x03\b\x00\x00\x04\xed" + // 0x044D0308: 0x000004ED
	"\x04#\x03\x04\x00\x00\x04\xee" + // 0x04230304: 0x000004EE
	"\x04C\x03\x04\x00\x00\x04\xef" + // 0x04430304: 0x000004EF
	"\x04#\x03\b\x00\x00\x04\xf0" + // 0x04230308: 0x000004F0
	"\x04C\x03\b\x00\x00\x04\xf1" + // 0x04430308: 0x000004F1
	"\x04#\x03\v\x00\x00\x04\xf2" + // 0x0423030B: 0x000004F2
	"\x04C\x03\v\x00\x00\x04\xf3" + // 0x0443030B: 0x000004F3
	"\x04'\x03\b\x00\x00\x04\xf4" + // 0x04270308: 0x000004F4
	"\x04G\x03\b\x00\x00\x04\xf5" + // 0x04470308: 0x000004F5
	"\x04+\x03\b\x00\x00\x04\xf8" + // 0x042B0308: 0x000004F8
	"\x04K\x03\b\x00\x00\x04\xf9" + // 0x044B0308: 0x000004F9
	"\x06'\x06S\x00\x00\x06\"" + // 0x06270653: 0x00000622
	"\x06'\x06T\x00\x00\x06#" + // 0x06270654: 0x00000623
	"\x06H\x06T\x00\x00\x06$" + // 0x06480654: 0x00000624
	"\x06'\x06U\x00\x00\x06%" + // 0x06270655: 0x00000625
	"\x06J\x06T\x00\x00\x06&" + // 0x064A0654: 0x00000626
	"\x06\xd5\x06T\x00\x00\x06\xc0" + // 0x06D50654: 0x000006C0
	"\x06\xc1\x06T\x00\x00\x06\xc2" + // 0x06C10654: 0x000006C2
	"\x06\xd2\x06T\x00\x00\x06\xd3" + // 0x06D20654: 0x000006D3
	"\t(\t<\x00\x00\t)" + // 0x0928093C: 0x00000929
	"\t0\t<\x00\x00\t1" + // 0x0930093C: 0x00000931
	"\t3\t<\x00\x00\t4" + // 0x0933093C: 0x00000934
	"\t\xc7\t\xbe\x00\x00\t\xcb" + // 0x09C709BE: 0x000009CB
	"\t\xc7\t\xd7\x00\x00\t\xcc" + // 0x09C709D7: 0x000009CC
	"\vG\vV\x00\x00\vH" + // 0x0B470B56: 0x00000B48
	"\vG\v>\x00\x00\vK" + // 0x0B470B3E: 0x00000B4B
	"\vG\vW\x00\x00\vL" + // 0x0B470B57: 0x00000B4C
	"\v\x92\v\xd7\x00\x00\v\x94" + // 0x0B920BD7: 0x00000B94
	"\v\xc6\v\xbe\x00\x00\v\xca" + // 0x0BC60BBE: 0x00000BCA
	"\v\xc7\v\xbe\x00\x00\v\xcb" + // 0x0BC70BBE: 0x00000BCB
	"\v\xc6\v\xd7\x00\x00\v\xcc" + // 0x0BC60BD7: 0x00000BCC
	"\fF\fV\x00\x00\fH" + // 0x0C460C56: 0x00000C48
	"\f\xbf\f\xd5\x00\x00\f\xc0" + // 0x0CBF0CD5: 0x00000CC0
	"\f\xc6\f\xd5\x00\x00\f\xc7" + // 0x0CC60CD5: 0x00000CC7
	"\f\xc6\f\xd6\x00\x00\f\xc8" + // 0x0CC60CD6: 0x00000CC8
	"\f\xc6\f\xc2\x00\x00\f\xca" + // 0x0CC60CC2: 0x00000CCA
	"\f\xca\f\xd5\x00\x00\f\xcb" + // 0x0CCA0CD5: 0x00000CCB
	"\rF\r>\x00\x00\rJ" + // 0x0D460D3E: 0x00000D4A
	"\rG\r>\x00\x00\rK" + // 0x0D470D3E: 0x00000D4B
	"\rF\rW\x00\x00\rL" + // 0x0D460D57: 0x00000D4C
	"\r\xd9\r\xca\x00\x00\r\xda" + // 0x0DD90DCA: 0x00000DDA
	"\r\xd9\r\xcf\x00\x00\r\xdc" + // 0x0DD90DCF: 0x00000DDC
	"\r\xdc\r\xca\x00\x00\r\xdd" + // 0x0DDC0DCA: 0x00000DDD
	"\r\xd9\r\xdf\x00\x00\r\xde" + // 0x0DD90DDF: 0x00000DDE
	"\x10%\x10.\x00\x00\x10&" + // 0x1025102E: 0x00001026
	"\x1b\x05\x1b5\x00\x00\x1b\x06" + // 0x1B051B35: 0x00001B06
	"\x1b\a\x1b5\x00\x00\x1b\b" + // 0x1B071B35: 0x00001B08
	"\x1b\t\x1b5\x00\x00\x1b\n" + // 0x1B091B35: 0x00001B0A
	"\x1b\v\x1b5\x00\x00\x1b\f" + // 0x1B0B1B35: 0x00001B0C
	"\x1b\r\x1b5\x00\x00\x1b\x0e" + // 0x1B0D1B35: 0x00001B0E
	"\x1b\x11\x1b5\x00\x00\x1b\x12" + // 0x1B111B35: 0x00001B12
	"\x1b:\x1b5\x00\x00\x1b;" + // 0x1B3A1B35: 0x00001B3B
	"\x1b<\x1b5\x00\x00\x1b=" + // 0x1B3C1B35: 0x00001B3D
	"\x1b>\x1b5\x00\x00\x1b@" + // 0x1B3E1B35: 0x00001B40
	"\x1b?\x1b5\x00\x00\x1bA" + // 0x1B3F1B35: 0x00001B41
	"\x1bB\x1b5\x00\x00\x1bC" + // 0x1B421B35: 0x00001B43
	"\x00A\x03%\x00\x00\x1e\x00" + // 0x00410325: 0x00001E00
	"\x00a\x03%\x00\x00\x1e\x01" + // 0x00610325: 0x00001E01
	"\x00B\x03\a\x00\x00\x1e\x02" + // 0x00420307: 0x00001E02
	"\x00b\x03\a\x00\x00\x1e\x03" + // 0x00620307: 0x00001E03
	"\x00B\x03#\x00\x00\x1e\x04" + // 0x00420323: 0x00001E04
	"\x00b\x03#\x00\x00\x1e\x05" + // 0x00620323: 0x00001E05
	"\x00B\x031\x00\x00\x1e\x06" + // 0x00420331: 0x00001E06
	"\x00b\x031\x00\x00\x1e\a" + // 0x00620331: 0x00001E07
	"\x00\xc7\x03\x01\x00\x00\x1e\b" + // 0x00C70301: 0x00001E08
	"\x00\xe7\x03\x01\x00\x00\x1e\t" + // 0x00E70301: 0x00001E09
	"\x00D\x03\a\x00\x00\x1e\n" + // 0x00440307: 0x00001E0A
	"\x00d\x03\a\x00\x00\x1e\v" + // 0x00640307: 0x00001E0B
	"\x00D\x03#\x00\x00\x1e\f" + // 0x00440323: 0x00001E0C
	"\x00d\x03#\x00\x00\x1e\r" + // 0x00640323: 0x00001E0D
	"\x00D\x031\x00\x00\x1e\x0e" + // 0x00440331: 0x00001E0E
	"\x00d\x031\x00\x00\x1e\x0f" + // 0x00640331: 0x00001E0F
	"\x00D\x03'\x00\x00\x1e\x10" + // 0x00440327: 0x00001E10
	"\x00d\x03'\x00\x00\x1e\x11" + // 0x00640327: 0x00001E11
	"\x00D\x03-\x00\x00\x1e\x12" + // 0x0044032D: 0x00001E12
	"\x00d\x03-\x00\x00\x1e\x13" + // 0x0064032D: 0x00001E13
	"\x01\x12\x03\x00\x00\x00\x1e\x14" + // 0x01120300: 0x00001E14
	"\x01\x13\x03\x00\x00\x00\x1e\x15" + // 0x01130300: 0x00001E15
	"\x01\x12\x03\x01\x00\x00\x1e\x16" + // 0x01120301: 0x00001E16
	"\x01\x13\x03\x01\x00\x00\x1e\x17" + // 0x01130301: 0x00001E17
	"\x00E\x03-\x00\x00\x1e\x18" + // 0x0045032D: 0x00001E18
	"\x00e\x03-\x00\x00\x1e\x19" + // 0x0065032D: 0x00001E19
	"\x00E\x030\x00\x00\x1e\x1a" + // 0x00450330: 0x00001E1A
	"\x00e\x030\x00\x00\x1e\x1b" + // 0x00650330: 0x00001E1B
	"\x02(\x03\x06\x00\x00\x1e\x1c" + // 0x02280306: 0x00001E1C
	"\x02)\x03\x06\x00\x00\x1e\x1d" + // 0x02290306: 0x00001E1D
	"\x00F\x03\a\x00\x00\x1e\x1e" + // 0x00460307: 0x00001E1E
	"\x00f\x03\a\x00\x00\x1e\x1f" + // 0x00660307: 0x00001E1F
	"\x00G\x03\x04\x00\x00\x1e " + // 0x00470304: 0x00001E20
	"\x00g\x03\x04\x00\x00\x1e!" + // 0x00670304: 0x00001E21
	"\x00H\x03\a\x00\x00\x1e\"" + // 0x00480307: 0x00001E22
	"\x00h\x03\a\x00\x00\x1e#" + // 0x00680307: 0x00001E23
	"\x00H\x03#\x00\x00\x1e$" + // 0x00480323: 0x00001E24
	"\x00h\x03#\x00\x00\x1e%" + // 0x00680323: 0x00001E25
	"\x00H\x03\b\x00\x00\x1e&" + // 0x00480308: 0x00001E26
	"\x00h\x03\b\x00\x00\x1e'" + // 0x00680308: 0x00001E27
	"\x00H\x03'\x00\x00\x1e(" + // 0x00480327: 0x00001E28
	"\x00h\x03'\x00\x00\x1e)" + // 0x00680327: 0x00001E29
	"\x00H\x03.\x00\x00\x1e*" + // 0x0048032E: 0x00001E2A
	"\x00h\x03.\x00\x00\x1e+" + // 0x0068032E: 0x00001E2B
	"\x00I\x030\x00\x00\x1e," + // 0x00490330: 0x00001E2C
	"\x00i\x030\x00\x00\x1e-" + // 0x00690330: 0x00001E2D
	"\x00\xcf\x03\x01\x00\x00\x1e." + // 0x00CF0301: 0x00001E2E
	"\x00\xef\x03\x01\x00\x00\x1e/" + // 0x00EF0301: 0x00001E2F
	"\x00K\x03\x01\x00\x00\x1e0" + // 0x004B0301: 0x00001E30
	"\x00k\x03\x01\x00\x00\x1e1" + // 0x006B0301: 0x00001E31
	"\x00K\x03#\x00\x00\x1e2" + // 0x004B0323: 0x00001E32
	"\x00k\x03#\x00\x00\x1e3" + // 0x006B0323: 0x00001E33
	"\x00K\x031\x00\x00\x1e4" + // 0x004B0331: 0x00001E34
	"\x00k\x031\x00\x00\x1e5" + // 0x006B0331: 0x00001E35
	"\x00L\x03#\x00\x00\x1e6" + // 0x004C0323: 0x00001E36
	"\x00l\x03#\x00\x00\x1e7" + // 0x006C0323: 0x00001E37
	"\x1e6\x03\x04\x00\x00\x1e8" + // 0x1E360304: 0x00001E38
	"\x1e7\x03\x04\x00\x00\x1e9" + // 0x1E370304: 0x00001E39
	"\x00L\x031\x00\x00\x1e:" + // 0x004C0331: 0x00001E3A
	"\x00l\x031\x00\x00\x1e;" + // 0x006C0331: 0x00001E3B
	"\x00L\x03-\x00\x00\x1e<" + // 0x004C032D: 0x00001E3C
	"\x00l\x03-\x00\x00\x1e=" + // 0x006C032D: 0x00001E3D
	"\x00M\x03\x01\x00\x00\x1e>" + // 0x004D0301: 0x00001E3E
	"\x00m\x03\x01\x00\x00\x1e?" + // 0x006D0301: 0x00001E3F
	"\x00M\x03\a\x00\x00\x1e@" + // 0x004D0307: 0x00001E40
	"\x00m\x03\a\x00\x00\x1eA" + // 0x006D0307: 0x00001E41
	"\x00M\x03#\x00\x00\x1eB" + // 0x004D0323: 0x00001E42
	"\x00m\x03#\x00\x00\x1eC" + // 0x006D0323: 0x00001E43
	"\x00N\x03\a\x00\x00\x1eD" + // 0x004E0307: 0x00001E44
	"\x00n\x03\a\x00\x00\x1eE" + // 0x006E0307: 0x00001E45
	"\x00N\x03#\x00\x00\x1eF" + // 0x004E0323: 0x00001E46
	"\x00n\x03#\x00\x00\x1eG" + // 0x006E0323: 0x00001E47
	"\x00N\x031\x00\x00\x1eH" + // 0x004E0331: 0x00001E48
	"\x00n\x031\x00\x00\x1eI" + // 0x006E0331: 0x00001E49
	"\x00N\x03-\x00\x00\x1eJ" + // 0x004E032D: 0x00001E4A
	"\x00n\x03-\x00\x00\x1eK" + // 0x006E032D: 0x00001E4B
	"\x00\xd5\x03\x01\x00\x00\x1eL" + // 0x00D50301: 0x00001E4C
	"\x00\xf5\x03\x01\x00\x00\x1eM" + // 0x00F50301: 0x00001E4D
	"\x00\xd5\x03\b\x00\x00\x1eN" + // 0x00D50308: 0x00001E4E
	"\x00\xf5\x03\b\x00\x00\x1eO" + // 0x00F50308: 0x00001E4F
	"\x01L\x03\x00\x00\x00\x1eP" + // 0x014C0300: 0x00001E50
	"\x01M\x03\x00\x00\x00\x1eQ" + // 0x014D0300: 0x00001E51
	"\x01L\x03\x01\x00\x00\x1eR" + // 0x014C0301: 0x00001E52
	"\x01M\x03\x01\x00\x00\x1eS" + // 0x014D0301: 0x00001E53
	"\x00P\x03\x01\x00\x00\x1eT" + // 0x00500301: 0x00001E54
	"\x00p\x03\x01\x00\x00\x1eU" + // 0x00700301: 0x00001E55
	"\x00P\x03\a\x00\x00\x1eV" + // 0x00500307: 0x00001E56
	"\x00p\x03\a\x00\x00\x1eW" + // 0x00700307: 0x00001E57
	"\x00R\x03\a\x00\x00\x1eX" + // 0x00520307: 0x00001E58
	"\x00r\x03\a\x00\x00\x1eY" + // 0x00720307: 0x00001E59
	"\x00R\x03#\x00\x00\x1eZ" + // 0x00520323: 0x00001E5A
	"\x00r\x03#\x00\x00\x1e[" + // 0x00720323: 0x00001E5B
	"\x1eZ\x03\x04\x00\x00\x1e\\" + // 0x1E5A0304: 0x00001E5C
	"\x1e[\x03\x04\x00\x00\x1e]" + // 0x1E5B0304: 0x00001E5D
	"\x00R\x031\x00\x00\x1e^" + // 0x00520331: 0x00001E5E
	"\x00r\x031\x00\x00\x1e_" + // 0x00720331: 0x00001E5F
	"\x00S\x03\a\x00\x00\x1e`" + // 0x00530307: 0x00001E60
	"\x00s\x03\a\x00\x00\x1ea" + // 0x00730307: 0x00001E61
	"\x00S\x03#\x00\x00\x1eb" + // 0x00530323: 0x00001E62
	"\x00s\x03#\x00\x00\x1ec" + // 0x00730323: 0x00001E63
	"\x01Z\x03\a\x00\x00\x1ed" + // 0x015A0307: 0x00001E64
	"\x01[\x03\a\x00\x00\x1ee" + // 0x015B0307: 0x00001E65
	"\x01`\x03\a\x00\x00\x1ef" + // 0x01600307: 0x00001E66
	"\x01a\x03\a\x00\x00\x1eg" + // 0x01610307: 0x00001E67
	"\x1eb\x03\a\x00\x00\x1eh" + // 0x1E620307: 0x00001E68
	"\x1ec\x03\a\x00\x00\x1ei" + // 0x1E630307: 0x00001E69
	"\x00T\x03\a\x00\x00\x1ej" + // 0x00540307: 0x00001E6A
	"\x00t\x03\a\x00\x00\x1ek" + // 0x00740307: 0x00001E6B
	"\x00T\x03#\x00\x00\x1el" + // 0x00540323: 0x00001E6C
	"\x00t\x03#\x00\x00\x1em" + // 0x00740323: 0x00001E6D
	"\x00T\x031\x00\x00\x1en" + // 0x00540331: 0x00001E6E
	"\x00t\x031\x00\x00\x1eo" + // 0x00740331: 0x00001E6F
	"\x00T\x03-\x00\x00\x1ep" + // 0x0054032D: 0x00001E70
	"\x00t\x03-\x00\x00\x1eq" + // 0x0074032D: 0x00001E71
	"\x00U\x03$\x00\x00\x1er" + // 0x00550324: 0x00001E72
	"\x00u\x03$\x00\x00\x1es" + // 0x00750324: 0x00001E73
	"\x00U\x030\x00\x00\x1et" + // 0x00550330: 0x00001E74
	"\x00u\x030\x00\x00\x1eu" + // 0x00750330: 0x00001E75
	"\x00U\x03-\x00\x00\x1ev" + // 0x0055032D: 0x00001E76
	"\x00u\x03-\x00\x00\x1ew" + // 0x0075032D: 0x00001E77
	"\x01h\x03\x01\x00\x00\x1ex" + // 0x01680301: 0x00001E78
	"\x01i\x03\x01\x00\x00\x1ey" + // 0x01690301: 0x00001E79
	"\x01j\x03\b\x00\x00\x1ez" + // 0x016A0308: 0x00001E7A
	"\x01k\x03\b\x00\x00\x1e{" + // 0x016B0308: 0x00001E7B
	"\x00V\x03\x03\x00\x00\x1e|" + // 0x00560303: 0x00001E7C
	"\x00v\x03\x03\x00\x00\x1e}" + // 0x00760303: 0x00001E7D
	"\x00V\x03#\x00\x00\x1e~" + // 0x00560323: 0x00001E7E
	"\x00v\x03#\x00\x00\x1e\u007f" + // 0x00760323: 0x00001E7F
	"\x00W\x03\x00\x00\x00\x1e\x80" + // 0x00570300: 0x00001E80
	"\x00w\x03\x00\x00\x00\x1e\x81" + // 0x00770300: 0x00001E81
	"\x00W\x03\x01\x00\x00\x1e\x82" + // 0x00570301: 0x00001E82
	"\x00w\x03\x01\x00\x00\x1e\x83" + // 0x00770301: 0x00001E83
	"\x00W\x03\b\x00\x00\x1e\x84" + // 0x00570308: 0x00001E84
	"\x00w\x03\b\x00\x00\x1e\x85" + // 0x00770308: 0x00001E85
	"\x00W\x03\a\x00\x00\x1e\x86" + // 0x00570307: 0x00001E86
	"\x00w\x03\a\x00\x00\x1e\x87" + // 0x00770307: 0x00001E87
	"\x00W\x03#\x00\x00\x1e\x88" + // 0x00570323: 0x00001E88
	"\x00w\x03#\x00\x00\x1e\x89" + // 0x00770323: 0x00001E89
	"\x00X\x03\a\x00\x00\x1e\x8a" + // 0x00580307: 0x00001E8A
	"\x00x\x03\a\x00\x00\x1e\x8b" + // 0x00780307: 0x00001E8B
	"\x00X\x03\b\x00\x00\x1e\x8c" + // 0x00580308: 0x00001E8C
	"\x00x\x03\b\x00\x00\x1e\x8d" + // 0x00780308: 0x00001E8D
	"\x00Y\x03\a\x00\x00\x1e\x8e" + // 0x00590307: 0x00001E8E
	"\x00y\x03\a\x00\x00\x1e\x8f" + // 0x00790307: 0x00001E8F
	"\x00Z\x03\x02\x00\x00\x1e\x90" + // 0x005A0302: 0x00001E90
	"\x00z\x03\x02\x00\x00\x1e\x91" + // 0x007A0302: 0x00001E91
	"\x00Z\x03#\x00\x00\x1e\x92" + // 0x005A0323: 0x00001E92
	"\x00z\x03#\x00\x00\x1e\x93" + // 0x007A0323: 0x00001E93
	"\x00Z\x031\x00\x00\x1e\x94" + // 0x005A0331: 0x00001E94
	"\x00z\x031\x00\x00\x1e\x95" + // 0x007A0331: 0x00001E95
	"\x00h\x031\x00\x00\x1e\x96" + // 0x00680331: 0x00001E96
	"\x00t\x03\b\x00\x00\x1e\x97" + // 0x00740308: 0x00001E97
	"\x00w\x03\n\x00\x00\x1e\x98" + // 0x0077030A: 0x00001E98
	"\x00y\x03\n\x00\x00\x1e\x99" + // 0x0079030A: 0x00001E99
	"\x01\u007f\x03\a\x00\x00\x1e\x9b" + // 0x017F0307: 0x00001E9B
	"\x00A\x03#\x00\x00\x1e\xa0" + // 0x00410323: 0x00001EA0
	"\x00a\x03#\x00\x00\x1e\xa1" + // 0x00610323: 0x00001EA1
	"\x00A\x03\t\x00\x00\x1e\xa2" + // 0x00410309: 0x00001EA2
	"\x00a\x03\t\x00\x00\x1e\xa3" + // 0x00610309: 0x00001EA3
	"\x00\xc2\x03\x01\x00\x00\x1e\xa4" + // 0x00C20301: 0x00001EA4
	"\x00\xe2\x03\x01\x00\x00\x1e\xa5" + // 0x00E20301: 0x00001EA5
	"\x00\xc2\x03\x00\x00\x00\x1e\xa6" + // 0x00C20300: 0x00001EA6
	"\x00\xe2\x03\x00\x00\x00\x1e\xa7" + // 0x00E20300: 0x00001EA7
	"\x00\xc2\x03\t\x00\x00\x1e\xa8" + // 0x00C20309: 0x00001EA8
	"\x00\xe2\x03\t\x00\x00\x1e\xa9" + // 0x00E20309: 0x00001EA9
	"\x00\xc2\x03\x03\x00\x00\x1e\xaa" + // 0x00C20303: 0x00001EAA
	"\x00\xe2\x03\x03\x00\x00\x1e\xab" + // 0x00E20303: 0x00001EAB
	"\x1e\xa0\x03\x02\x00\x00\x1e\xac" + // 0x1EA00302: 0x00001EAC
	"\x1e\xa1\x03\x02\x00\x00\x1e\xad" + // 0x1EA10302: 0x00001EAD
	"\x01\x02\x03\x01\x00\x00\x1e\xae" + // 0x01020301: 0x00001EAE
	"\x01\x03\x03\x01\x00\x00\x1e\xaf" + // 0x01030301: 0x00001EAF
	"\x01\x02\x03\x00\x00\x00\x1e\xb0" + // 0x01020300: 0x00001EB0
	"\x01\x03\x03\x00\x00\x00\x1e\xb1" + // 0x01030300: 0x00001EB1
	"\x01\x02\x03\t\x00\x00\x1e\xb2" + // 0x01020309: 0x00001EB2
	"\x01\x03\x03\t\x00\x00\x1e\xb3" + // 0x01030309: 0x00001EB3
	"\x01\x02\x03\x03\x00\x00\x1e\xb4" + // 0x01020303: 0x00001EB4
	"\x01\x03\x03\x03\x00\x00\x1e\xb5" + // 0x01030303: 0x00001EB5
	"\x1e\xa0\x03\x06\x00\x00\x1e\xb6" + // 0x1EA00306: 0x00001EB6
	"\x1e\xa1\x03\x06\x00\x00\x1e\xb7" + // 0x1EA10306: 0x00001EB7
	"\x00E\x03#\x00\x00\x1e\xb8" + // 0x00450323: 0x00001EB8
	"\x00e\x03#\x00\x00\x1e\xb9" + // 0x00650323: 0x00001EB9
	"\x00E\x03\t\x00\x00\x1e\xba" + // 0x00450309: 0x00001EBA
	"\x00e\x03\t\x00\x00\x1e\xbb" + // 0x00650309: 0x00001EBB
	"\x00E\x03\x03\x00\x00\x1e\xbc" + // 0x00450303: 0x00001EBC
	"\x00e\x03\x03\x00\x00\x1e\xbd" + // 0x00650303: 0x00001EBD
	"\x00\xca\x03\x01\x00\x00\x1e\xbe" + // 0x00CA0301: 0x00001EBE
	"\x00\xea\x03\x01\x00\x00\x1e\xbf" + // 0x00EA0301: 0x00001EBF
	"\x00\xca\x03\x00\x00\x00\x1e\xc0" + // 0x00CA0300: 0x00001EC0
	"\x00\xea\x03\x00\x00\x00\x1e\xc1" + // 0x00EA0300: 0x00001EC1
	"\x00\xca\x03\t\x00\x00\x1e\xc2" + // 0x00CA0309: 0x00001EC2
	"\x00\xea\x03\t\x00\x00\x1e\xc3" + // 0x00EA0309: 0x00001EC3
	"\x00\xca\x03\x03\x00\x00\x1e\xc4" + // 0x00CA0303: 0x00001EC4
	"\x00\xea\x03\x03\x00\x00\x1e\xc5" + // 0x00EA0303: 0x00001EC5
	"\x1e\xb8\x03\x02\x00\x00\x1e\xc6" + // 0x1EB80302: 0x00001EC6
	"\x1e\xb9\x03\x02\x00\x00\x1e\xc7" + // 0x1EB90302: 0x00001EC7
	"\x00I\x03\t\x00\x00\x1e\xc8" + // 0x00490309: 0x00001EC8
	"\x00i\x03\t\x00\x00\x1e\xc9" + // 0x00690309: 0x00001EC9
	"\x00I\x03#\x00\x00\x1e\xca" + // 0x00490323: 0x00001ECA
	"\x00i\x03#\x00\x00\x1e\xcb" + // 0x00690323: 0x00001ECB
	"\x00O\x03#\x00\x00\x1e\xcc" + // 0x004F0323: 0x00001ECC
	"\x00o\x03#\x00\x00\x1e\xcd" + // 0x006F0323: 0x00001ECD
	"\x00O\x03\t\x00\x00\x1e\xce" + // 0x004F0309: 0x00001ECE
	"\x00o\x03\t\x00\x00\x1e\xcf" + // 0x006F0309: 0x00001ECF
	"\x00\xd4\x03\x01\x00\x00\x1e\xd0" + // 0x00D40301: 0x00001ED0
	"\x00\xf4\x03\x01\x00\x00\x1e\xd1" + // 0x00F40301: 0x00001ED1
	"\x00\xd4\x03\x00\x00\x00\x1e\xd2" + // 0x00D40300: 0x00001ED2
	"\x00\xf4\x03\x00\x00\x00\x1e\xd3" + // 0x00F40300: 0x00001ED3
	"\x00\xd4\x03\t\x00\x00\x1e\xd4" + // 0x00D40309: 0x00001ED4
	"\x00\xf4\x03\t\x00\x00\x1e\xd5" + // 0x00F40309: 0x00001ED5
	"\x00\xd4\x03\x03\x00\x00\x1e\xd6" + // 0x00D40303: 0x00001ED6
	"\x00\xf4\x03\x03\x00\x00\x1e\xd7" + // 0x00F40303: 0x00001ED7
	"\x1e\xcc\x03\x02\x00\x00\x1e\xd8" + // 0x1ECC0302: 0x00001ED8
	"\x1e\xcd\x03\x02\x00\x00\x1e\xd9" + // 0x1ECD0302: 0x00001ED9
	"\x01\xa0\x03\x01\x00\x00\x1e\xda" + // 0x01A00301: 0x00001EDA
	"\x01\xa1\x03\x01\x00\x00\x1e\xdb" + // 0x01A10301: 0x00001EDB
	"\x01\xa0\x03\x00\x00\x00\x1e\xdc" + // 0x01A00300: 0x00001EDC
	"\x01\xa1\x03\x00\x00\x00\x1e\xdd" + // 0x01A10300: 0x00001EDD
	"\x01\xa0\x03\t\x00\x00\x1e\xde" + // 0x01A00309: 0x00001EDE
	"\x01\xa1\x03\t\x00\x00\x1e\xdf" + // 0x01A10309: 0x00001EDF
	"\x01\xa0\x03\x03\x00\x00\x1e\xe0" + // 0x01A00303: 0x00001EE0
	"\x01\xa1\x03\x03\x00\x00\x1e\xe1" + // 0x01A10303: 0x00001EE1
	"\x01\xa0\x03#\x00\x00\x1e\xe2" + // 0x01A00323: 0x00001EE2
	"\x01\xa1\x03#\x00\x00\x1e\xe3" + // 0x01A10323: 0x00001EE3
	"\x00U\x03#\x00\x00\x1e\xe4" + // 0x00550323: 0x00001EE4
	"\x00u\x03#\x00\x00\x1e\xe5" + // 0x00750323: 0x00001EE5
	"\x00U\x03\t\x00\x00\x1e\xe6" + // 0x00550309: 0x00001EE6
	"\x00u\x03\t\x00\x00\x1e\xe7" + // 0x00750309: 0x00001EE7
	"\x01\xaf\x03\x01\x00\x00\x1e\xe8" + // 0x01AF0301: 0x00001EE8
	"\x01\xb0\x03\x01\x00\x00\x1e\xe9" + // 0x01B00301: 0x00001EE9
	"\x01\xaf\x03\x00\x00\x00\x1e\xea" + // 0x01AF0300: 0x00001EEA
	"\x01\xb0\x03\x00\x00\x00\x1e\xeb" + // 0x01B00300: 0x00001EEB
	"\x01\xaf\x03\t\x00\x00\x1e\xec" + // 0x01AF0309: 0x00001EEC
	"\x01\xb0\x03\t\x00\x00\x1e\xed" + // 0x01B00309: 0x00001EED
	"\x01\xaf\x03\x03\x00\x00\x1e\xee" + // 0x01AF0303: 0x00001EEE
	"\x01\xb0\x03\x03\x00\x00\x1e\xef" + // 0x01B00303: 0x00001EEF
	"\x01\xaf\x03#\x00\x00\x1e\xf0" + // 0x01AF0323: 0x00001EF0
	"\x01\xb0\x03#\x00\x00\x1e\xf1" + // 0x01B00323: 0x00001EF1
	"\x00Y\x03\x00\x00\x00\x1e\xf2" + // 0x00590300: 0x00001EF2
	"\x00y\x03\x00\x00\x00\x1e\xf3" + // 0x00790300: 0x00001EF3
	"\x00Y\x03#\x00\x00\x1e\xf4" + // 0x00590323: 0x00001EF4
	"\x00y\x03#\x00\x00\x1e\xf5" + // 0x00790323: 0x00001EF5
	"\x00Y\x03\t\x00\x00\x1e\xf6" + // 0x00590309: 0x00001EF6
	"\x00y\x03\t\x00\x00\x1e\xf7" + // 0x00790309: 0x00001EF7
	"\x00Y\x03\x03\x00\x00\x1e\xf8" + // 0x00590303: 0x00001EF8
	"\x00y\x03\x03\x00\x00\x1e\xf9" + // 0x00790303: 0x00001EF9
	"\x03\xb1\x03\x13\x00\x00\x1f\x00" + // 0x03B10313: 0x00001F00
	"\x03\xb1\x03\x14\x00\x00\x1f\x01" + // 0x03B10314: 0x00001F01
	"\x1f\x00\x03\x00\x00\x00\x1f\x02" + // 0x1F000300: 0x00001F02
	"\x1f\x01\x03\x00\x00\x00\x1f\x03" + // 0x1F010300: 0x00001F03
	"\x1f\x00\x03\x01\x00\x00\x1f\x04" + // 0x1F000301: 0x00001F04
	"\x1f\x01\x03\x01\x00\x00\x1f\x05" + // 0x1F010301: 0x00001F05
	"\x1f\x00\x03B\x00\x00\x1f\x06" + // 0x1F000342: 0x00001F06
	"\x1f\x01\x03B\x00\x00\x1f\a" + // 0x1F010342: 0x00001F07
	"\x03\x91\x03\x13\x00\x00\x1f\b" + // 0x03910313: 0x00001F08
	"\x03\x91\x03\x14\x00\x00\x1f\t" + // 0x03910314: 0x00001F09
	"\x1f\b\x03\x00\x00\x00\x1f\n" + // 0x1F080300: 0x00001F0A
	"\x1f\t\x03\x00\x00\x00\x1f\v" + // 0x1F090300: 0x00001F0B
	"\x1f\b\x03\x01\x00\x00\x1f\f" + // 0x1F080301: 0x00001F0C
	"\x1f\t\x03\x01\x00\x00\x1f\r" + // 0x1F090301: 0x00001F0D
	"\x1f\b\x03B\x00\x00\x1f\x0e" + // 0x1F080342: 0x00001F0E
	"\x1f\t\x03B\x00\x00\x1f\x0f" + // 0x1F090342: 0x00001F0F
	"\x03\xb5\x03\x13\x00\x00\x1f\x10" + // 0x03B50313: 0x00001F10
	"\x03\xb5\x03\x14\x00\x00\x1f\x11" + // 0x03B50314: 0x00001F11
	"\x1f\x10\x03\x00\x00\x00\x1f\x12" + // 0x1F100300: 0x00001F12
	"\x1f\x11\x03\x00\x00\x00\x1f\x13" + // 0x1F110300: 0x00001F13
	"\x1f\x10\x03\x01\x00\x00\x1f\x14" + // 0x1F100301: 0x00001F14
	"\x1f\x11\x03\x01\x00\x00\x1f\x15" + // 0x1F110301: 0x00001F15
	"\x03\x95\x03\x13\x00\x00\x1f\x18" + // 0x03950313: 0x00001F18
	"\x03\x95\x03\x14\x00\x00\x1f\x19" + // 0x03950314: 0x00001F19
	"\x1f\x18\x03\x00\x00\x00\x1f\x1a" + // 0x1F180300: 0x00001F1A
	"\x1f\x19\x03\x00\x00\x00\x1f\x1b" + // 0x1F190300: 0x00001F1B
	"\x1f\x18\x03\x01\x00\x00\x1f\x1c" + // 0x1F180301: 0x00001F1C
	"\x1f\x19\x03\x01\x00\x00\x1f\x1d" + // 0x1F190301: 0x00001F1D
	"\x03\xb7\x03\x13\x00\x00\x1f " + // 0x03B70313: 0x00001F20
	"\x03\xb7\x03\x14\x00\x00\x1f!" + // 0x03B70314: 0x00001F21
	"\x1f \x03\x00\x00\x00\x1f\"" + // 0x1F200300: 0x00001F22
	"\x1f!\x03\x00\x00\x00\x1f#" + // 0x1F210300: 0x00001F23
	"\x1f \x03\x01\x00\x00\x1f$" + // 0x1F200301: 0x00001F24
	"\x1f!\x03\x01\x00\x00\x1f%" + // 0x1F210301: 0x00001F25
	"\x1f \x03B\x00\x00\x1f&" + // 0x1F200342: 0x00001F26
	"\x1f!\x03B\x00\x00\x1f'" + // 0x1F210342: 0x00001F27
	"\x03\x97\x03\x13\x00\x00\x1f(" + // 0x03970313: 0x00001F28
	"\x03\x97\x03\x14\x00\x00\x1f)" + // 0x03970314: 0x00001F29
	"\x1f(\x03\x00\x00\x00\x1f*" + // 0x1F280300: 0x00001F2A
	"\x1f)\x03\x00\x00\x00\x1f+" + // 0x1F290300: 0x00001F2B
	"\x1f(\x03\x01\x00\x00\x1f," + // 0x1F280301: 0x00001F2C
	"\x1f)\x03\x01\x00\x00\x1f-" + // 0x1F290301: 0x00001F2D
	"\x1f(\x03B\x00\x00\x1f." + // 0x1F280342: 0x00001F2E
	"\x1f)\x03B\x00\x00\x1f/" + // 0x1F290342: 0x00001F2F
	"\x03\xb9\x03\x13\x00\x00\x1f0" + // 0x03B90313: 0x00001F30
	"\x03\xb9\x03\x14\x00\x00\x1f1" + // 0x03B90314: 0x00001F31
	"\x1f0\x03\x00\x00\x00\x1f2" + // 0x1F300300: 0x00001F32
	"\x1f1\x03\x00\x00\x00\x1f3" + // 0x1F310300: 0x00001F33
	"\x1f0\x03\x01\x00\x00\x1f4" + // 0x1F300301: 0x00001F34
	"\x1f1\x03\x01\x00\x00\x1f5" + // 0x1F310301: 0x00001F35
	"\x1f0\x03B\x00\x00\x1f6" + // 0x1F300342: 0x00001F36
	"\x1f1\x03B\x00\x00\x1f7" + // 0x1F310342: 0x00001F37
	"\x03\x99\x03\x13\x00\x00\x1f8" + // 0x03990313: 0x00001F38
	"\x03\x99\x03\x14\x00\x00\x1f9" + // 0x03990314: 0x00001F39
	"\x1f8\x03\x00\x00\x00\x1f:" + // 0x1F380300: 0x00001F3A
	"\x1f9\x03\x00\x00\x00\x1f;" + // 0x1F390300: 0x00001F3B
	"\x1f8\x03\x01\x00\x00\x1f<" + // 0x1F380301: 0x00001F3C
	"\x1f9\x03\x01\x00\x00\x1f=" + // 0x1F390301: 0x00001F3D
	"\x1f8\x03B\x00\x00\x1f>" + // 0x1F380342: 0x00001F3E
	"\x1f9\x03B\x00\x00\x1f?" + // 0x1F390342: 0x00001F3F
	"\x03\xbf\x03\x13\x00\x00\x1f@" + // 0x03BF0313: 0x00001F40
	"\x03\xbf\x03\x14\x00\x00\x1fA" + // 0x03BF0314: 0x00001F41
	"\x1f@\x03\x00\x00\x00\x1fB" + // 0x1F400300: 0x00001F42
	"\x1fA\x03\x00\x00\x00\x1fC" + // 0x1F410300: 0x00001F43
	"\x1f@\x03\x01\x00\x00\x1fD" + // 0x1F400301: 0x00001F44
	"\x1fA\x03\x01\x00\x00\x1fE" + // 0x1F410301: 0x00001F45
	"\x03\x9f\x03\x13\x00\x00\x1fH" + // 0x039F0313: 0x00001F48
	"\x03\x9f\x03\x14\x00\x00\x1fI" + // 0x039F0314: 0x00001F49
	"\x1fH\x03\x00\x00\x00\x1fJ" + // 0x1F480300: 0x00001F4A
	"\x1fI\x03\x00\x00\x00\x1fK" + // 0x1F490300: 0x00001F4B
	"\x1fH\x03\x01\x00\x00\x1fL" + // 0x1F480301: 0x00001F4C
	"\x1fI\x03\x01\x00\x00\x1fM" + // 0x1F490301: 0x00001F4D
	"\x03\xc5\x03\x13\x00\x00\x1fP" + // 0x03C50313: 0x00001F50
	"\x03\xc5\x03\x14\x00\x00\x1fQ" + // 0x03C50314: 0x00001F51
	"\x1fP\x03\x00\x00\x00\x1fR" + // 0x1F500300: 0x00001F52
	"\x1fQ\x03\x00\x00\x00\x1fS" + // 0x1F510300: 0x00001F53
	"\x1fP\x03\x01\x00\x00\x1fT" + // 0x1F500301: 0x00001F54
	"\x1fQ\x03\x01\x00\x00\x1fU" + // 0x1F510301: 0x00001F55
	"\x1fP\x03B\x00\x00\x1fV" + // 0x1F500342: 0x00001F56
	"\x1fQ\x03B\x00\x00\x1fW" + // 0x1F510342: 0x00001F57
	"\x03\xa5\x03\x14\x00\x00\x1fY" + // 0x03A50314: 0x00001F59
	"\x1fY\x03\x00\x00\x00\x1f[" + // 0x1F590300: 0x00001F5B
	"\x1fY\x03\x01\x00\x00\x1f]" + // 0x1F590301: 0x00001F5D
	"\x1fY\x03B\x00\x00\x1f_" + // 0x1F590342: 0x00001F5F
	"\x03\xc9\x03\x13\x00\x00\x1f`" + // 0x03C90313: 0x00001F60
	"\x03\xc9\x03\x14\x00\x00\x1fa" + // 0x03C90314: 0x00001F61
	"\x1f`\x03\x00\x00\x00\x1fb" + // 0x1F600300: 0x00001F62
	"\x1fa\x03\x00\x00\x00\x1fc" + // 0x1F610300: 0x00001F63
	"\x1f`\x03\x01\x00\x00\x1fd" + // 0x1F600301: 0x00001F64
	"\x1fa\x03\x01\x00\x00\x1fe" + // 0x1F610301: 0x00001F65
	"\x1f`\x03B\x00\x00\x1ff" + // 0x1F600342: 0x00001F66
	"\x1fa\x03B\x00\x00\x1fg" + // 0x1F610342: 0x00001F67
	"\x03\xa9\x03\x13\x00\x00\x1fh" + // 0x03A90313: 0x00001F68
	"\x03\xa9\x03\x14\x00\x00\x1fi" + // 0x03A90314: 0x00001F69
	"\x1fh\x03\x00\x00\x00\x1fj" + // 0x1F680300: 0x00001F6A
	"\x1fi\x03\x00\x00\x00\x1fk" + // 0x1F690300: 0x00001F6B
	"\x1fh\x03\x01\x00\x00\x1fl" + // 0x1F680301: 0x00001F6C
	"\x1fi\x03\x01\x00\x00\x1fm" + // 0x1F690301: 0x00001F6D
	"\x1fh\x03B\x00\x00\x1fn" + // 0x1F680342: 0x00001F6E
	"\x1fi\x03B\x00\x00\x1fo" + // 0x1F690342: 0x00001F6F
	"\x03\xb1\x03\x00\x00\x00\x1fp" + // 0x03B10300: 0x00001F70
	"\x03\xb5\x03\x00\x00\x00\x1fr" + // 0x03B50300: 0x00001F72
	"\x03\xb7\x03\x00\x00\x00\x1ft" + // 0x03B70300: 0x00001F74
	"\x03\xb9\x03\x00\x00\x00\x1fv" + // 0x03B90300: 0x00001F76
	"\x03\xbf\x03\x00\x00\x00\x1fx" + // 0x03BF0300: 0x00001F78
	"\x03\xc5\x03\x00\x00\x00\x1fz" + // 0x03C50300: 0x00001F7A
	"\x03\xc9\x03\x00\x00\x00\x1f|" + // 0x03C90300: 0x00001F7C
	"\x1f\x00\x03E\x00\x00\x1f\x80" + // 0x1F000345: 0x00001F80
	"\x1f\x01\x03E\x00\x00\x1f\x81" + // 0x1F010345: 0x00001F81
	"\x1f\x02\x03E\x00\x00\x1f\x82" + // 0x1F020345: 0x00001F82
	"\x1f\x03\x03E\x00\x00\x1f\x83" + // 0x1F030345: 0x00001F83
	"\x1f\x04\x03E\x00\x00\x1f\x84" + // 0x1F040345: 0x00001F84
	"\x1f\x05\x03E\x00\x00\x1f\x85" + // 0x1F050345: 0x00001F85
	"\x1f\x06\x03E\x00\x00\x1f\x86" + // 0x1F060345: 0x00001F86
	"\x1f\a\x03E\x00\x00\x1f\x87" + // 0x1F070345: 0x00001F87
	"\x1f\b\x03E\x00\x00\x1f\x88" + // 0x1F080345: 0x00001F88
	"\x1f\t\x03E\x00\x00\x1f\x89" + // 0x1F090345: 0x00001F89
	"\x1f\n\x03E\x00\x00\x1f\x8a" + // 0x1F0A0345: 0x00001F8A
	"\x1f\v\x03E\x00\x00\x1f\x8b" + // 0x1F0B0345: 0x00001F8B
	"\x1f\f\x03E\x00\x00\x1f\x8c" + // 0x1F0C0345: 0x00001F8C
	"\x1f\r\x03E\x00\x00\x1f\x8d" + // 0x1F0D0345: 0x00001F8D
	"\x1f\x0e\x03E\x00\x00\x1f\x8e" + // 0x1F0E0345: 0x00001F8E
	"\x1f\x0f\x03E\x00\x00\x1f\x8f" + // 0x1F0F0345: 0x00001F8F
	"\x1f \x03E\x00\x00\x1f\x90" + // 0x1F200345: 0x00001F90
	"\x1f!\x03E\x00\x00\x1f\x91" + // 0x1F210345: 0x00001F91
	"\x1f\"\x03E\x00\x00\x1f\x92" + // 0x1F220345: 0x00001F92
	"\x1f#\x03E\x00\x00\x1f\x93" + // 0x1F230345: 0x00001F93
	"\x1f$\x03E\x00\x00\x1f\x94" + // 0x1F240345: 0x00001F94
	"\x1f%\x03E\x00\x00\x1f\x95" + // 0x1F250345: 0x00001F95
	"\x1f&\x03E\x00\x00\x1f\x96" + // 0x1F260345: 0x00001F96
	"\x1f'\x03E\x00\x00\x1f\x97" + // 0x1F270345: 0x00001F97
	"\x1f(\x03E\x00\x00\x1f\x98" + // 0x1F280345: 0x00001F98
	"\x1f)\x03E\x00\x00\x1f\x99" + // 0x1F290345: 0x00001F99
	"\x1f*\x03E\x00\x00\x1f\x9a" + // 0x1F2A0345: 0x00001F9A
	"\x1f+\x03E\x00\x00\x1f\x9b" + // 0x1F2B0345: 0x00001F9B
	"\x1f,\x03E\x00\x00\x1f\x9c" + // 0x1F2C0345: 0x00001F9C
	"\x1f-\x03E\x00\x00\x1f\x9d" + // 0x1F2D0345: 0x00001F9D
	"\x1f.\x03E\x00\x00\x1f\x9e" + // 0x1F2E0345: 0x00001F9E
	"\x1f/\x03E\x00\x00\x1f\x9f" + // 0x1F2F0345: 0x00001F9F
	"\x1f`\x03E\x00\x00\x1f\xa0" + // 0x1F600345: 0x00001FA0
	"\x1fa\x03E\x00\x00\x1f\xa1" + // 0x1F610345: 0x00001FA1
	"\x1fb\x03E\x00\x00\x1f\xa2" + // 0x1F620345: 0x00001FA2
	"\x1fc\x03E\x00\x00\x1f\xa3" + // 0x1F630345: 0x00001FA3
	"\x1fd\x03E\x00\x00\x1f\xa4" + // 0x1F640345: 0x00001FA4
	"\x1fe\x03E\x00\x00\x1f\xa5" + // 0x1F650345: 0x00001FA5
	"\x1ff\x03E\x00\x00\x1f\xa6" + // 0x1F660345: 0x00001FA6
	"\x1fg\x03E\x00\x00\x1f\xa7" + // 0x1F670345: 0x00001FA7
	"\x1fh\x03E\x00\x00\x1f\xa8" + // 0x1F680345: 0x00001FA8
	"\x1fi\x03E\x00\x00\x1f\xa9" + // 0x1F690345: 0x00001FA9
	"\x1fj\x03E\x00\x00\x1f\xaa" + // 0x1F6A0345: 0x00001FAA
	"\x1fk\x03E\x00\x00\x1f\xab" + // 0x1F6B0345: 0x00001FAB
	"\x1fl\x03E\x00\x00\x1f\xac" + // 0x1F6C0345: 0x00001FAC
	"\x1fm\x03E\x00\x00\x1f\xad" + // 0x1F6D0345: 0x00001FAD
	"\x1fn\x03E\x00\x00\x1f\xae" + // 0x1F6E0345: 0x00001FAE
	"\x1fo\x03E\x00\x00\x1f\xaf" + // 0x1F6F0345: 0x00001FAF
	"\x03\xb1\x03\x06\x00\x00\x1f\xb0" + // 0x03B10306: 0x00001FB0
	"\x03\xb1\x03\x04\x00\x00\x1f\xb1" + // 0x03B10304: 0x00001FB1
	"\x1fp\x03E\x00\x00\x1f\xb2" + // 0x1F700345: 0x00001FB2
	"\x03\xb1\x03E\x00\x00\x1f\xb3" + // 0x03B10345: 0x00001FB3
	"\x03\xac\x03E\x00\x00\x1f\xb4" + // 0x03AC0345: 0x00001FB4
	"\x03\xb1\x03B\x00\x00\x1f\xb6" + // 0x03B10342: 0x00001FB6
	"\x1f\xb6\x03E\x00\x00\x1f\xb7" + // 0x1FB60345: 0x00001FB7
	"\x03\x91\x03\x06\x00\x00\x1f\xb8" + // 0x03910306: 0x00001FB8
	"\x03\x91\x03\x04\x00\x00\x1f\xb9" + // 0x03910304: 0x00001FB9
	"\x03\x91\x03\x00\x00\x00\x1f\xba" + // 0x03910300: 0x00001FBA
	"\x03\x91\x03E\x00\x00\x1f\xbc" + // 0x03910345: 0x00001FBC
	"\x00\xa8\x03B\x00\x00\x1f\xc1" + // 0x00A80342: 0x00001FC1
	"\x1ft\x03E\x00\x00\x1f\xc2" + // 0x1F740345: 0x00001FC2
	"\x03\xb7\x03E\x00\x00\x1f\xc3" + // 0x03B70345: 0x00001FC3
	"\x03\xae\x03E\x00\x00\x1f\xc4" + // 0x03AE0345: 0x00001FC4
	"\x03\xb7\x03B\x00\x00\x1f\xc6" + // 0x03B70342: 0x00001FC6
	"\x1f\xc6\x03E\x00\x00\x1f\xc7" + // 0x1FC60345: 0x00001FC7
	"\x03\x95\x03\x00\x00\x00\x1f\xc8" + // 0x03950300: 0x00001FC8
	"\x03\x97\x03\x00\x00\x00\x1f\xca" + // 0x03970300: 0x00001FCA
	"\x03\x97\x03E\x00\x00\x1f\xcc" + // 0x03970345: 0x00001FCC
	"\x1f\xbf\x03\x00\x00\x00\x1f\xcd" + // 0x1FBF0300: 0x00001FCD
	"\x1f\xbf\x03\x01\x00\x00\x1f\xce" + // 0x1FBF0301: 0x00001FCE
	"\x1f\xbf\x03B\x00\x00\x1f\xcf" + // 0x1FBF0342: 0x00001FCF
	"\x03\xb9\x03\x06\x00\x00\x1f\xd0" + // 0x03B90306: 0x00001FD0
	"\x03\xb9\x03\x04\x00\x00\x1f\xd1" + // 0x03B90304: 0x00001FD1
	"\x03\xca\x03\x00\x00\x00\x1f\xd2" + // 0x03CA0300: 0x00001FD2
	"\x03\xb9\x03B\x00\x00\x1f\xd6" + // 0x03B90342: 0x00001FD6
	"\x03\xca\x03B\x00\x00\x1f\xd7" + // 0x03CA0342: 0x00001FD7
	"\x03\x99\x03\x06\x00\x00\x1f\xd8" + // 0x03990306: 0x00001FD8
	"\x03\x99\x03\x04\x00\x00\x1f\xd9" + // 0x03990304: 0x00001FD9
	"\x03\x99\x03\x00\x00\x00\x1f\xda" + // 0x03990300: 0x00001FDA
	"\x1f\xfe\x03\x00\x00\x00\x1f\xdd" + // 0x1FFE0300: 0x00001FDD
	"\x1f\xfe\x03\x01\x00\x00\x1f\xde" + // 0x1FFE0301: 0x00001FDE
	"\x1f\xfe\x03B\x00\x00\x1f\xdf" + // 0x1FFE0342: 0x00001FDF
	"\x03\xc5\x03\x06\x00\x00\x1f\xe0" + // 0x03C50306: 0x00001FE0
	"\x03\xc5\x03\x04\x00\x00\x1f\xe1" + // 0x03C50304: 0x00001FE1
	"\x03\xcb\x03\x00\x00\x00\x1f\xe2" + // 0x03CB0300: 0x00001FE2
	"\x03\xc1\x03\x13\x00\x00\x1f\xe4" + // 0x03C10313: 0x00001FE4
	"\x03\xc1\x03\x14\x00\x00\x1f\xe5" + // 0x03C10314: 0x00001FE5
	"\x03\xc5\x03B\x00\x00\x1f\xe6" + // 0x03C50342: 0x00001FE6
	"\x03\xcb\x03B\x00\x00\x1f\xe7" + // 0x03CB0342: 0x00001FE7
	"\x03\xa5\x03\x06\x00\x00\x1f\xe8" + // 0x03A50306: 0x00001FE8
	"\x03\xa5\x03\x04\x00\x00\x1f\xe9" + // 0x03A50304: 0x00001FE9
	"\x03\xa5\x03\x00\x00\x00\x1f\xea" + // 0x03A50300: 0x00001FEA
	"\x03\xa1\x03\x14\x00\x00\x1f\xec" + // 0x03A10314: 0x00001FEC
	"\x00\xa8\x03\x00\x00\x00\x1f\xed" + // 0x00A80300: 0x00001FED
	"\x1f|\x03E\x00\x00\x1f\xf2" + // 0x1F7C0345: 0x00001FF2
	"\x03\xc9\x03E\x00\x00\x1f\xf3" + // 0x03C90345: 0x00001FF3
	"\x03\xce\x03E\x00\x00\x1f\xf4" + // 0x03CE0345: 0x00001FF4
	"\x03\xc9\x03B\x00\x00\x1f\xf6" + // 0x03C90342: 0x00001FF6
	"\x1f\xf6\x03E\x00\x00\x1f\xf7" + // 0x1FF60345: 0x00001FF7
	"\x03\x9f\x03\x00\x00\x00\x1f\xf8" + // 0x039F0300: 0x00001FF8
	"\x03\xa9\x03\x00\x00\x00\x1f\xfa" + // 0x03A90300: 0x00001FFA
	"\x03\xa9\x03E\x00\x00\x1f\xfc" + // 0x03A90345: 0x00001FFC
	"!\x90\x038\x00\x00!\x9a" + // 0x21900338: 0x0000219A
	"!\x92\x038\x00\x00!\x9b" + // 0x21920338: 0x0000219B
	"!\x94\x038\x00\x00!\xae" + // 0x21940338: 0x000021AE
	"!\xd0\x038\x00\x00!\xcd" + // 0x21D00338: 0x000021CD
	"!\xd4\x038\x00\x00!\xce" + // 0x21D40338: 0x000021CE
	"!\xd2\x038\x00\x00!\xcf" + // 0x21D20338: 0x000021CF
	"\"\x03\x038\x00\x00\"\x04" + // 0x22030338: 0x00002204
	"\"\b\x038\x00\x00\"\t" + // 0x22080338: 0x00002209
	"\"\v\x038\x00\x00\"\f" + // 0x220B0338: 0x0000220C
	"\"#\x038\x00\x00\"$" + // 0x22230338: 0x00002224
	"\"%\x038\x00\x00\"&" + // 0x22250338: 0x00002226
	"\"<\x038\x00\x00\"A" + // 0x223C0338: 0x00002241
	"\"C\x038\x00\x00\"D" + // 0x22430338: 0x00002244
	"\"E\x038\x00\x00\"G" + // 0x22450338: 0x00002247
	"\"H\x038\x00\x00\"I" + // 0x22480338: 0x00002249
	"\x00=\x038\x00\x00\"`" + // 0x003D0338: 0x00002260
	"\"a\x038\x00\x00\"b" + // 0x22610338: 0x00002262
	"\"M\x038\x00\x00\"m" + // 0x224D0338: 0x0000226D
	"\x00<\x038\x00\x00\"n" + // 0x003C0338: 0x0000226E
	"\x00>\x038\x00\x00\"o" + // 0x003E0338: 0x0000226F
	"\"d\x038\x00\x00\"p" + // 0x22640338: 0x00002270
	"\"e\x038\x00\x00\"q" + // 0x22650338: 0x00002271
	"\"r\x038\x00\x00\"t" + // 0x22720338: 0x00002274
	"\"s\x038\x00\x00\"u" + // 0x22730338: 0x00002275
	"\"v\x038\x00\x00\"x" + // 0x22760338: 0x00002278
	"\"w\x038\x00\x00\"y" + // 0x22770338: 0x00002279
	"\"z\x038\x00\x00\"\x80" + // 0x227A0338: 0x00002280
	"\"{\x038\x00\x00\"\x81" + // 0x227B0338: 0x00002281
	"\"\x82\x038\x00\x00\"\x84" + // 0x22820338: 0x00002284
	"\"\x83\x038\x00\x00\"\x85" + // 0x22830338: 0x00002285
	"\"\x86\x038\x00\x00\"\x88" + // 0x22860338: 0x00002288
	"\"\x87\x038\x00\x00\"\x89" + // 0x22870338: 0x00002289
	"\"\xa2\x038\x00\x00\"\xac" + // 0x22A20338: 0x000022AC
	"\"\xa8\x038\x00\x00\"\xad" + // 0x22A80338: 0x000022AD
	"\"\xa9\x038\x00\x00\"\xae" + // 0x22A90338: 0x000022AE
	"\"\xab\x038\x00\x00\"\xaf" + // 0x22AB0338: 0x000022AF
	"\"|\x038\x00\x00\"\xe0" + // 0x227C0338: 0x000022E0
	"\"}\x038\x00\x00\"\xe1" + // 0x227D0338: 0x000022E1
	"\"\x91\x038\x00\x00\"\xe2" + // 0x22910338: 0x000022E2
	"\"\x92\x038\x00\x00\"\xe3" + // 0x22920338: 0x000022E3
	"\"\xb2\x038\x00\x00\"\xea" + // 0x22B20338: 0x000022EA
	"\"\xb3\x038\x00\x00\"\xeb" + // 0x22B30338: 0x000022EB
	"\"\xb4\x038\x00\x00\"\xec" + // 0x22B40338: 0x000022EC
	"\"\xb5\x038\x00\x00\"\xed" + // 0x22B50338: 0x000022ED
	"0K0\x99\x00\x000L" + // 0x304B3099: 0x0000304C
	"0M0\x99\x00\x000N" + // 0x304D3099: 0x0000304E
	"0O0\x99\x00\x000P" + // 0x304F3099: 0x00003050
	"0Q0\x99\x00\x000R" + // 0x30513099: 0x00003052
	"0S0\x99\x00\x000T" + // 0x30533099: 0x00003054
	"0U0\x99\x00\x000V" + // 0x30553099: 0x00003056
	"0W0\x99\x00\x000X" + // 0x30573099: 0x00003058
	"0Y0\x99\x00\x000Z" + // 0x30593099: 0x0000305A
	"0[0\x99\x00\x000\\" + // 0x305B3099: 0x0000305C
	"0]0\x99\x00\x000^" + // 0x305D3099: 0x0000305E
	"0_0\x99\x00\x000`" + // 0x305F3099: 0x00003060
	"0a0\x99\x00\x000b" + // 0x30613099: 0x00003062
	"0d0\x99\x00\x000e" + // 0x30643099: 0x00003065
	"0f0\x99\x00\x000g" + // 0x30663099: 0x00003067
	"0h0\x99\x00\x000i" + // 0x30683099: 0x00003069
	"0o0\x99\x00\x000p" + // 0x306F3099: 0x00003070
	"0o0\x9a\x00\x000q" + // 0x306F309A: 0x00003071
	"0r0\x99\x00\x000s" + // 0x30723099: 0x00003073
	"0r0\x9a\x00\x000t" + // 0x3072309A: 0x00003074
	"0u0\x99\x00\x000v" + // 0x30753099: 0x00003076
	"0u0\x9a\x00\x000w" + // 0x3075309A: 0x00003077
	"0x0\x99\x00\x000y" + // 0x30783099: 0x00003079
	"0x0\x9a\x00\x000z" + // 0x3078309A: 0x0000307A
	"0{0\x99\x00\x000|" + // 0x307B3099: 0x0000307C
	"0{0\x9a\x00\x000}" + // 0x307B309A: 0x0000307D
	"0F0\x99\x00\x000\x94" + // 0x30463099: 0x00003094
	"0\x9d0\x99\x00\x000\x9e" + // 0x309D3099: 0x0000309E
	"0\xab0\x99\x00\x000\xac" + // 0x30AB3099: 0x000030AC
	"0\xad0\x99\x00\x000\xae" + // 0x30AD3099: 0x000030AE
	"0\xaf0\x99\x00\x000\xb0" + // 0x30AF3099: 0x000030B0
	"0\xb10\x99\x00\x000\xb2" + // 0x30B13099: 0x000030B2
	"0\xb30\x99\x00\x000\xb4" + // 0x30B33099: 0x000030B4
	"0\xb50\x99\x00\x000\xb6" + // 0x30B53099: 0x000030B6
	"0\xb70\x99\x00\x000\xb8" + // 0x30B73099: 0x000030B8
	"0\xb90\x99\x00\x000\xba" + // 0x30B93099: 0x000030BA
	"0\xbb0\x99\x00\x000\xbc" + // 0x30BB3099: 0x000030BC
	"0\xbd0\x99\x00\x000\xbe" + // 0x30BD3099: 0x000030BE
	"0\xbf0\x99\x00\x000\xc0" + // 0x30BF3099: 0x000030C0
	"0\xc10\x99\x00\x000\xc2" + // 0x30C13099: 0x000030C2
	"0\xc40\x99\x00\x000\xc5" + // 0x30C43099: 0x000030C5
	"0\xc60\x99\x00\x000\xc7" + // 0x30C63099: 0x000030C7
	"0\xc80\x99\x00\x000\xc9" + // 0x30C83099: 0x000030C9
	"0\xcf0\x99\x00\x000\xd0" + // 0x30CF3099: 0x000030D0
	"0\xcf0\x9a\x00\x000\xd1" + // 0x30CF309A: 0x000030D1
	"0\xd20\x99\x00\x000\xd3" + // 0x30D23099: 0x000030D3
	"0\xd20\x9a\x00\x000\xd4" + // 0x30D2309A: 0x000030D4
	"0\xd50\x99\x00\x000\xd6" + // 0x30D53099: 0x000030D6
	"0\xd50\x9a\x00\x000\xd7" + // 0x30D5309A: 0x000030D7
	"0\xd80\x99\x00\x000\xd9" + // 0x30D83099: 0x000030D9
	"0\xd80\x9a\x00\x000\xda" + // 0x30D8309A: 0x000030DA
	"0\xdb0\x99\x00\x000\xdc" + // 0x30DB3099: 0x000030DC
	"0\xdb0\x9a\x00\x000\xdd" + // 0x30DB309A: 0x000030DD
	"0\xa60\x99\x00\x000\xf4" + // 0x30A63099: 0x000030F4
	"0\xef0\x99\x00\x000\xf7" + // 0x30EF3099: 0x000030F7
	"0\xf00\x99\x00\x000\xf8" + // 0x30F03099: 0x000030F8
	"0\xf10\x99\x00\x000\xf9" + // 0x30F13099: 0x000030F9
	"0\xf20\x99\x00\x000\xfa" + // 0x30F23099: 0x000030FA
	"0\xfd0\x99\x00\x000\xfe" + // 0x30FD3099: 0x000030FE
	"\x10\x99\x10\xba\x00\x01\x10\x9a" + // 0x109910BA: 0x0001109A
	"\x10\x9b\x10\xba\x00\x01\x10\x9c" + // 0x109B10BA: 0x0001109C
	"\x10\xa5\x10\xba\x00\x01\x10\xab" + // 0x10A510BA: 0x000110AB
	"\x111\x11'\x00\x01\x11." + // 0x11311127: 0x0001112E
	"\x112\x11'\x00\x01\x11/" + // 0x11321127: 0x0001112F
	"\x13G\x13>\x00\x01\x13K" + // 0x1347133E: 0x0001134B
	"\x13G\x13W\x00\x01\x13L" + // 0x13471357: 0x0001134C
	"\x14\xb9\x14\xba\x00\x01\x14\xbb" + // 0x14B914BA: 0x000114BB
	"\x14\xb9\x14\xb0\x00\x01\x14\xbc" + // 0x14B914B0: 0x000114BC
	"\x14\xb9\x14\xbd\x00\x01\x14\xbe" + // 0x14B914BD: 0x000114BE
	"\x15\xb8\x15\xaf\x00\x01\x15\xba" + // 0x15B815AF: 0x000115BA
	"\x15\xb9\x15\xaf\x00\x01\x15\xbb" + // 0x15B915AF: 0x000115BB
	""
	// Total size of tables: 55KB (55977 bytes)
