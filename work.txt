第二项：Raft 一致性协议（40 分）
复制服务通过在多个复制服务器上存储其状态（即数据）的完整副本来实
现容错。即使部分服务器出现故障（崩溃或网络中断或不稳定），复制也能使
服务继续运行。然而，故障可能会导致副本持有不同的数据副本。
Raft 将客户端请求整理成一个序列（称为日志），并确保所有副本服务器
都能看到相同的日志。每个副本按日志顺序执行客户请求，并将其应用到服务
状态的本地副本中。由于所有实时副本都能看到相同的日志内容，因此它们都
会以相同的顺序执行相同的请求，从而继续保持相同的服务状态。如果服务器
出现故障，但随后又恢复了，Raft 会负责更新其日志。只要至少有大多数服务
器还保持运行并能相互通信，Raft 就会继续运行。如果没有过半数的服务器存
活，Raft 将不会执行任何事务处理操作，但只要有过半数的服务器能再次通信，
Raft 就会继续运行。
在本实验中，你需要把 Raft 作为 Go 对象类型来实现，并提供相关方法，
以便在更大的服务中作为一个模块使用。一组 Raft 实例通过 RPC 相互通信，
以维护复制的日志。Raft 的日志条目用索引号编号，并最终将被提交。此时，
Raft 需要将日志条目发送给更大的服务，以便其执行对应的操作。 一、项目的具体实验内容：
请阅读论文（https://pdos.csail.mit.edu/6.824/papers/raft-extended.pdf），并深
入理解 Raft 协议（可以参考动画 https://raft.github.io/）。根据 MIT 6.824 课程
（http://nil.csail.mit.edu/6.824/2022/labs/lab-raft.html），完成实验二：Raft 的全
部实验内容。 二、数据及相关环境
1）操作系统要求
节点规模：3 节点
操作系统: 使用 Linux（Ubuntu 20.04 或 CentOS 7）
硬件要求:
CPU: 至少 2 核 CPU，推荐 4 核以上。
内存: 最少 8 GB RAM，推荐 16 GB 及以上。
存储: 至少 50 GB 可用空间，具体取决于数据量和日志存储需求。
网络: 多节点配置需要稳定的网络连接。
2）软件要求
Go 语言运行环境
5）整体要求
完成代码，通过所有的测试用例。

1）Raft选举（10分）
阅读实验要求，实现 Raft 领导者选举和心跳机制（无日志条目的 AppendEntries RPC），并保证代码通过测试用例。

2）Raft日志（10分）
阅读实验要求，实现领导者和跟随者日志复制过程的代码，实现在分布式节点上追加新的日志条目，并保证代码通过测试用例。

3）Raft持久化（10分）
阅读实验要求，通过添加保存和恢复持久化状态的代码，完成 raft.go 中的函数 persist() 和 readPersist()，并保证代码通过测试用例。

4）Raft日志压缩（10分）
阅读实验要求，实现 Snapshot() 和 InstallSnapshot RPC，并保证代码通过测试用例。

