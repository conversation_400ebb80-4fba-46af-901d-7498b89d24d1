# github.com/google/pprof v0.0.0-20241101162523-b92577c0c142
## explicit; go 1.22
github.com/google/pprof/driver
github.com/google/pprof/internal/binutils
github.com/google/pprof/internal/driver
github.com/google/pprof/internal/elfexec
github.com/google/pprof/internal/graph
github.com/google/pprof/internal/measurement
github.com/google/pprof/internal/plugin
github.com/google/pprof/internal/report
github.com/google/pprof/internal/symbolizer
github.com/google/pprof/internal/symbolz
github.com/google/pprof/internal/transport
github.com/google/pprof/profile
github.com/google/pprof/third_party/svgpan
# github.com/ianlancetaylor/demangle v0.0.0-20240912202439-0a2b6291aafd
## explicit; go 1.13
github.com/ianlancetaylor/demangle
# golang.org/x/arch v0.12.0
## explicit; go 1.18
golang.org/x/arch/arm/armasm
golang.org/x/arch/arm64/arm64asm
golang.org/x/arch/loong64/loong64asm
golang.org/x/arch/ppc64/ppc64asm
golang.org/x/arch/riscv64/riscv64asm
golang.org/x/arch/s390x/s390xasm
golang.org/x/arch/x86/x86asm
# golang.org/x/build v0.0.0-20241205234318-b850320af2a4
## explicit; go 1.22.0
golang.org/x/build/relnote
# golang.org/x/mod v0.22.0
## explicit; go 1.22.0
golang.org/x/mod/internal/lazyregexp
golang.org/x/mod/modfile
golang.org/x/mod/module
golang.org/x/mod/semver
golang.org/x/mod/sumdb
golang.org/x/mod/sumdb/dirhash
golang.org/x/mod/sumdb/note
golang.org/x/mod/sumdb/tlog
golang.org/x/mod/zip
# golang.org/x/sync v0.10.0
## explicit; go 1.18
golang.org/x/sync/errgroup
golang.org/x/sync/semaphore
# golang.org/x/sys v0.28.0
## explicit; go 1.18
golang.org/x/sys/plan9
golang.org/x/sys/unix
golang.org/x/sys/windows
# golang.org/x/telemetry v0.0.0-20241204182053-c0ac0e154df3
## explicit; go 1.22.0
golang.org/x/telemetry
golang.org/x/telemetry/counter
golang.org/x/telemetry/counter/countertest
golang.org/x/telemetry/internal/config
golang.org/x/telemetry/internal/configstore
golang.org/x/telemetry/internal/counter
golang.org/x/telemetry/internal/crashmonitor
golang.org/x/telemetry/internal/mmap
golang.org/x/telemetry/internal/telemetry
golang.org/x/telemetry/internal/upload
# golang.org/x/term v0.27.0
## explicit; go 1.18
golang.org/x/term
# golang.org/x/text v0.21.0
## explicit; go 1.18
golang.org/x/text/cases
golang.org/x/text/internal
golang.org/x/text/internal/language
golang.org/x/text/internal/language/compact
golang.org/x/text/internal/tag
golang.org/x/text/language
golang.org/x/text/transform
golang.org/x/text/unicode/norm
# golang.org/x/tools v0.28.1-0.20250131145412-98746475647e
## explicit; go 1.22.0
golang.org/x/tools/cmd/bisect
golang.org/x/tools/cover
golang.org/x/tools/go/analysis
golang.org/x/tools/go/analysis/internal/analysisflags
golang.org/x/tools/go/analysis/passes/appends
golang.org/x/tools/go/analysis/passes/asmdecl
golang.org/x/tools/go/analysis/passes/assign
golang.org/x/tools/go/analysis/passes/atomic
golang.org/x/tools/go/analysis/passes/bools
golang.org/x/tools/go/analysis/passes/buildtag
golang.org/x/tools/go/analysis/passes/cgocall
golang.org/x/tools/go/analysis/passes/composite
golang.org/x/tools/go/analysis/passes/copylock
golang.org/x/tools/go/analysis/passes/ctrlflow
golang.org/x/tools/go/analysis/passes/defers
golang.org/x/tools/go/analysis/passes/directive
golang.org/x/tools/go/analysis/passes/errorsas
golang.org/x/tools/go/analysis/passes/framepointer
golang.org/x/tools/go/analysis/passes/httpresponse
golang.org/x/tools/go/analysis/passes/ifaceassert
golang.org/x/tools/go/analysis/passes/inspect
golang.org/x/tools/go/analysis/passes/internal/analysisutil
golang.org/x/tools/go/analysis/passes/loopclosure
golang.org/x/tools/go/analysis/passes/lostcancel
golang.org/x/tools/go/analysis/passes/nilfunc
golang.org/x/tools/go/analysis/passes/printf
golang.org/x/tools/go/analysis/passes/shift
golang.org/x/tools/go/analysis/passes/sigchanyzer
golang.org/x/tools/go/analysis/passes/slog
golang.org/x/tools/go/analysis/passes/stdmethods
golang.org/x/tools/go/analysis/passes/stdversion
golang.org/x/tools/go/analysis/passes/stringintconv
golang.org/x/tools/go/analysis/passes/structtag
golang.org/x/tools/go/analysis/passes/testinggoroutine
golang.org/x/tools/go/analysis/passes/tests
golang.org/x/tools/go/analysis/passes/timeformat
golang.org/x/tools/go/analysis/passes/unmarshal
golang.org/x/tools/go/analysis/passes/unreachable
golang.org/x/tools/go/analysis/passes/unsafeptr
golang.org/x/tools/go/analysis/passes/unusedresult
golang.org/x/tools/go/analysis/unitchecker
golang.org/x/tools/go/ast/inspector
golang.org/x/tools/go/cfg
golang.org/x/tools/go/types/objectpath
golang.org/x/tools/go/types/typeutil
golang.org/x/tools/internal/aliases
golang.org/x/tools/internal/analysisinternal
golang.org/x/tools/internal/bisect
golang.org/x/tools/internal/facts
golang.org/x/tools/internal/stdlib
golang.org/x/tools/internal/typeparams
golang.org/x/tools/internal/typesinternal
golang.org/x/tools/internal/versions
# rsc.io/markdown v0.0.0-20240306144322-0bf8f97ee8ef
## explicit; go 1.20
rsc.io/markdown
